{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", "storybook-static/**"], "dependsOn": ["^build"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "clean": {"cache": false}, "storybook": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "preview-storybook": {"dependsOn": ["^build"], "cache": false}}}