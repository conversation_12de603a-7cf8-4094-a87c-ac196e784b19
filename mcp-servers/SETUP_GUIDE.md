# MCP Server Setup Guide

This guide covers the setup and configuration of MCP servers for this project.

## Brave Search MCP Server

### Installation Status
✅ **Configuration Created**: `.vscode/mcp.json` with Brave Search server configuration  
✅ **Directory Structure**: `mcp-servers/brave-search/` created  
✅ **Documentation**: Setup and usage documentation created  
✅ **Package Verified**: `@modelcontextprotocol/server-brave-search` package is available via NPX  

### Next Steps to Complete Setup

1. **Restart VS Code** or reload the MCP configuration to connect the server
2. **Enter API Key** when prompted by VS Code
3. **Verify Connection** in the MCP servers panel

### Configuration Details

**Server Name**: `github.com/modelcontextprotocol/servers/tree/main/src/brave-search`  
**Command**: `npx -y @modelcontextprotocol/server-brave-search`  
**Environment**: Requires `BRAVE_API_KEY`

### Available Tools After Connection

- `brave_web_search` - General web search with pagination
- `brave_local_search` - Local business search with fallback

### Testing the Server

Once connected, you can test the server with:

```javascript
// Web search example
{
  "query": "React 18 best practices 2024",
  "count": 5
}

// Local search example  
{
  "query": "restaurants near San Francisco",
  "count": 10
}
```

## Getting a Brave Search API Key

1. Visit [Brave Search API](https://brave.com/search/api/)
2. Sign up for an account
3. Choose a plan (Free tier: 2,000 queries/month)
4. Generate API key from [developer dashboard](https://api-dashboard.search.brave.com/app/keys)

## Troubleshooting

### Server Not Connecting
- Restart VS Code completely
- Check that `.vscode/mcp.json` exists and is valid JSON
- Verify API key is entered correctly when prompted

### API Key Issues
- Ensure the API key is active and not expired
- Check API usage limits haven't been exceeded
- Verify the key has proper permissions

### Package Installation Issues
- Ensure internet connection is available
- Try running `npx -y @modelcontextprotocol/server-brave-search` manually
- Check NPM registry access

## Project Integration

The Brave Search MCP server integrates with the existing MCP infrastructure documented in [`docs/development/mcp-servers.md`](../docs/development/mcp-servers.md).

This server complements the existing tools:
- **Playwright MCP Server**: Browser automation and testing
- **File System Tools**: Code editing and file management  
- **Process Management**: Running builds and development servers

## Usage in Development Workflow

The Brave Search server is particularly useful for:
- Researching latest best practices and patterns
- Finding solutions to specific technical problems
- Discovering new libraries and tools
- Getting current information about frameworks and technologies

Example workflow:
1. Use `brave_web_search` to research a technical topic
2. Use `codebase-retrieval` to find existing patterns in the codebase
3. Use `str-replace-editor` to implement new code based on research
4. Use Playwright tools to test the implementation