# Brave Search MCP Server

This directory contains the configuration and documentation for the Brave Search MCP server integration.

## Setup

The MCP server is configured in `.vscode/mcp.json` with the following configuration:

- **Server Name**: `github.com/modelcontextprotocol/servers/tree/main/src/brave-search`
- **Command**: `npx -y @modelcontextprotocol/server-brave-search`
- **Environment**: Requires `BRAVE_API_KEY` environment variable

## API Key Setup

1. Sign up for a [Brave Search API account](https://brave.com/search/api/)
2. Choose a plan (Free tier available with 2,000 queries/month)
3. Generate your API key [from the developer dashboard](https://api-dashboard.search.brave.com/app/keys)
4. When prompted by VS Code, enter your API key

## Available Tools

### brave_web_search
- Execute web searches with pagination and filtering
- Parameters:
  - `query` (string): Search terms
  - `count` (number, optional): Results per page (max 20)
  - `offset` (number, optional): Pagination offset (max 9)

### brave_local_search
- Search for local businesses and services
- Parameters:
  - `query` (string): Local search terms
  - `count` (number, optional): Number of results (max 20)
- Automatically falls back to web search if no local results found

## Usage

Once configured, the MCP server will be available in VS Code and can be used through the MCP tools interface.

## Testing

To test the server functionality, you can use the `brave_web_search` tool with a simple query like "latest technology news" or the `brave_local_search` tool with a query like "restaurants near me".