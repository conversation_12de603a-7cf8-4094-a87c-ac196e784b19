# Playwright MCP Server

This directory contains the setup for the Playwright MCP Server from https://github.com/executeautomation/mcp-playwright.

## Installation

The server has been installed globally using:
```bash
npm install -g @executeautomation/playwright-mcp-server
```

## Configuration

The server is configured in the MCP settings with the name `github.com/executeautomation/mcp-playwright` and uses:
- Command: `npx`
- Args: `["-y", "@executeautomation/playwright-mcp-server"]`

## Capabilities

This MCP server provides browser automation capabilities using Playwright, including:
- Web page navigation
- Taking screenshots
- Clicking elements
- Filling forms
- Executing JavaScript
- HTTP requests (GET, POST, PUT, PATCH, DELETE)
- Code generation for test automation
- Console log retrieval
- PDF generation
- And much more

## Available Tools

The server provides numerous tools for browser automation. See the main repository documentation for a complete list of available tools and their usage.