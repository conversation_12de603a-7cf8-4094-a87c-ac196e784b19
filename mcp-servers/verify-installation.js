#!/usr/bin/env node

/**
 * Verification script for Brave Search MCP Server
 * This script verifies that the MCP server package is available and properly configured.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Brave Search MCP Server Installation Verification\n');

// Check if mcp.json exists
const mcpConfigPath = path.join(__dirname, '..', '.vscode', 'mcp.json');
console.log('1. Checking MCP configuration file...');
if (fs.existsSync(mcpConfigPath)) {
  console.log('   ✅ .vscode/mcp.json exists');
  
  try {
    const config = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'));
    const serverName = 'github.com/modelcontextprotocol/servers/tree/main/src/brave-search';
    
    if (config.servers && config.servers[serverName]) {
      console.log('   ✅ Brave Search server configuration found');
      console.log(`   📝 Command: ${config.servers[serverName].command}`);
      console.log(`   📝 Args: ${config.servers[serverName].args.join(' ')}`);
    } else {
      console.log('   ❌ Brave Search server configuration not found');
    }
  } catch (error) {
    console.log('   ❌ Invalid JSON in mcp.json:', error.message);
  }
} else {
  console.log('   ❌ .vscode/mcp.json not found');
}

// Check if NPX can find the package
console.log('\n2. Checking NPX package availability...');
try {
  // This will fail due to missing API key, but confirms package exists
  execSync('npx -y @modelcontextprotocol/server-brave-search --help', { 
    stdio: 'pipe',
    timeout: 30000 
  });
  console.log('   ✅ Package available via NPX');
} catch (error) {
  if (error.stdout && error.stdout.toString().includes('BRAVE_API_KEY')) {
    console.log('   ✅ Package available via NPX (requires API key as expected)');
  } else {
    console.log('   ❌ Package not available:', error.message);
  }
}

// Check directory structure
console.log('\n3. Checking directory structure...');
const directories = [
  'mcp-servers',
  'mcp-servers/brave-search'
];

directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (fs.existsSync(dirPath)) {
    console.log(`   ✅ ${dir}/ exists`);
  } else {
    console.log(`   ❌ ${dir}/ missing`);
  }
});

// Check documentation files
console.log('\n4. Checking documentation...');
const docs = [
  'mcp-servers/brave-search/README.md',
  'mcp-servers/SETUP_GUIDE.md'
];

docs.forEach(doc => {
  const docPath = path.join(__dirname, '..', doc);
  if (fs.existsSync(docPath)) {
    console.log(`   ✅ ${doc} exists`);
  } else {
    console.log(`   ❌ ${doc} missing`);
  }
});

console.log('\n🎯 Next Steps:');
console.log('   1. Restart VS Code to load the MCP configuration');
console.log('   2. Enter your Brave Search API key when prompted');
console.log('   3. Verify the server appears in Connected MCP Servers');
console.log('   4. Test with brave_web_search or brave_local_search tools');

console.log('\n📚 Documentation:');
console.log('   • Setup Guide: mcp-servers/SETUP_GUIDE.md');
console.log('   • Server Docs: mcp-servers/brave-search/README.md');
console.log('   • All MCP Tools: docs/development/mcp-servers.md');