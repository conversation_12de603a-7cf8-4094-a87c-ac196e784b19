const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('http://localhost:3001');
  await page.waitForTimeout(2000); // Wait for page to load
  await page.screenshot({ path: './aug-admin-screenshot.png', fullPage: true });
  console.log('Screenshot saved to ./aug-admin-screenshot.png');
  await browser.close();
})();