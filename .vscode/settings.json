{"editor.fontSize": 14, "markdown.preview.fontSize": 12, "files.exclude": {".old/**": true, "**/node_modules": true, "**/node_modules/**": true}, "typescript.preferences.exclude": [".old/**"], "search.exclude": {".old/**": true, "**/node_modules": true, "**/node_modules/**": true}, "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.validate.enable": true, "typescript.preferences.disableSuggestions": false, "files.watcherExclude": {".old/**": true, "**/node_modules/**": true, "**/.git/**": true}, "eslint.workingDirectories": ["apps/dashboard"], "eslint.exclude": [".old/**"]}