# AU25 Turbo React Design

A modern React-based auction application built with TypeScript, Valtio state management, and a real-time event-driven CQRS architecture. This project replaces the legacy Charlize Vue2 application with a modern React ecosystem.

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Start development
just storybook-dev

# Clean build artifacts
just clean
```

## Justfile and turborepo

NOTE: we use `just` to run many commands, always favor those over `pnpm` scripts if available.
In particular, never use `pnpm dev` rather:
`turbo run dev --filter=<app-or-package-name>` 
as turbo dev will make sure that dependent packages are built (if the cache is out of date)


## 📁 Project Structure

This is a Turborepo monorepo with the following structure:

- **`apps/`** - Applications
  - `storybook/` - Component documentation and testing
  - `aug-admin-dashboard/` - Admin dashboard demo
- **`packages/`** - Shared packages
  - `api-client/` - WebSocket client with real-time CQRS architecture
  - `data-grid/` - AG Grid-based data table components
  - `ui/` - Shared UI components

## 📚 Documentation

- **[Architecture](./docs/ARCHITECTURE.md)** - System architecture and design patterns
- **[Decisions](./docs/DECISIONS.md)** - Architectural decisions and rationale
- **[Packages](./docs/PACKAGES.md)** - Detailed package documentation
- **[Changelog](./docs/CHANGELOG.md)** - Project history and changes
- **[Standards](./docs/STANDARDS.md)** - Coding standards and conventions

## 🏗️ Key Features

### Real-time Event-driven CQRS
- WebSocket-based real-time communication
- Complete state materialization (no incremental updates)
- Optimized for auction environments
- See [`packages/api-client/README.md`](./packages/api-client/README.md) for details

### Modern React Stack
- **TypeScript** for type safety
- **Valtio** for state management (no Provider pattern)
- **AG Grid** for advanced data tables
- **Storybook** for component development
- **Turborepo** for monorepo management

### Component Library
- **DeRoundTable** - Auction round table with real-time updates
- **BaseAgGrid** - Enhanced AG Grid wrapper with pinned rows
- **Data Grid Components** - Reusable table components

## 🛠️ Development

### Package Management
This project uses **pnpm** as the package manager. Always use pnpm commands:

```bash
pnpm install          # Install dependencies
pnpm build            # Build all packages
pnpm dev              # Start development servers
```

### State Management
We use Valtio with the `getDomainStore()` pattern instead of React Context:

```typescript
// ✅ Preferred approach
const domainStore = getDomainStore();
const users = domainStore.users;

// ❌ Avoid Provider pattern
const { users } = useContext(UserContext);
```

### Testing & Documentation
Stories are co-located with components in packages:

```bash
just storybook-dev     # Start Storybook
```

## 🎯 Migration from Charlize

This project migrates components from the legacy Charlize Vue2 application:

- **Technology**: Vue2 + Pinia → React + Valtio
- **Architecture**: Request/response → Real-time CQRS
- **Components**: Vue SFCs → React TypeScript components
- **State**: Vuex/Pinia → Valtio with getDomainStore()

## 📝 Contributing

1. Follow the [coding standards](./docs/STANDARDS.md)
2. Read [architectural decisions](./docs/DECISIONS.md)
3. Keep stories co-located with components
4. Use `getDomainStore()` for state access
5. Always use pnpm for package management

## 📄 License

[Add license information]

---

**Created:** May 2025
**Based on:** create-turbo design-system template
**Replaces:** Charlize Vue2 auction application
