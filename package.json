{"name": "au25-turbo-react-design", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=storybook^... && changeset publish", "storybook-preview": "turbo run preview-storybook", "test:storybook": "node scripts/test-storybook.js"}, "devDependencies": {"@changesets/cli": "^2.29.4", "prettier": "^3.5.3", "rimraf": "^6.0.1", "turbo": "^2.5.3"}, "packageManager": "pnpm@8.15.6"}