// This file is used to configure how stories are rendered globally.
// It's the place to import global CSS, add decorators, etc.
import type { Preview } from "@storybook/react";
import React from 'react';
import '../src/index.css'; // Import the main CSS file

// Suppress React act() warnings for AG Grid
// These warnings occur because AG Grid makes state updates during rendering
const originalError = console.error;
console.error = (...args) => {
  // Suppress specific React act() warnings for AG Grid
  if (args[0] && typeof args[0] === 'string' &&
     (args[0].includes('Warning: The current testing environment is not configured to support act') ||
      args[0].includes('not wrapped in act(...)'))) {
    return;
  }
  originalError(...args);
};

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    // Set dark background for all stories
    backgrounds: {
      default: 'dark',
      values: [
        {
          name: 'dark',
          value: '#0f172a', // slate-900 - matches our app background
        },
        {
          name: 'light',
          value: '#ffffff',
        },
      ],
    },
  },
};

export default preview;
