import { dirname, join, resolve } from "path";

function getAbsolutePath(value) {
  return dirname(require.resolve(join(value, "package.json")));
}

const config = {
  stories: [
    "../stories/*.stories.tsx",
    "../stories/**/*.stories.tsx",
    "../src/**/*.stories.tsx"
  ],
  staticDirs: ["../public"], // Add this line to find the favicon.icon (which I got from bwp.auctionologies.com/favicon.ico)
  addons: [
    getAbsolutePath("@storybook/addon-links"),
    getAbsolutePath("@storybook/addon-essentials"),
  ],
  framework: {
    name: getAbsolutePath("@storybook/react-vite"),
    options: {},
  },

  core: {},

  async viteFinal(config, { configType }) {
    // customize the Vite config here
    return {
      ...config,
      define: { "process.env": {} },
      resolve: {
        alias: [
          {
            find: "@",
            replacement: resolve(__dirname, "../src"),
          },
        ],
      },
    };
  },

  docs: {
    autodocs: true,
  },
};

export default config;
