// tests/helpers/websocket-server.ts
import { WebSocketServer, WebSocket } from 'ws';
import pako from 'pako';
import type { Server as HttpServer } from 'http'; // Only needed if attaching to existing HTTP server

// Interface for the controls returned by the start function
export interface TestServerControls {
    server: WebSocketServer;
    httpServer?: HttpServer; // Optional underlying HTTP server (not used in global setup)
    url: string;
    close: () => Promise<void>;
    waitForConnection: (timeout?: number) => Promise<WebSocket>;
    getLastReceivedMessage: () => any | null;
    sendCompressed: (client: WebSocket, data: any) => void;
}

// Module-level state for the helper instance
let lastReceivedMessage: any = null;
let connectionResolver: ((client: WebSocket) => void) | null = null;
// @ts-ignore - Used in multiple places but TypeScript doesn't detect it
let connectionRejecter: ((reason?: any) => void) | null = null;
let connectionTimeoutId: NodeJS.Timeout | null = null;

/**
 * Starts a WebSocket test server.
 * @param port The port to listen on.
 * @returns Controls for interacting with the server.
 */
export function startTestServer(port: number): Promise<TestServerControls> {
    console.log(`[Test Server Helper] Attempting to start WebSocket server on port ${port}...`);
    // Reset state for this instance
    lastReceivedMessage = null;
    connectionResolver = null;
    connectionRejecter = null;
    if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
    connectionTimeoutId = null;

    return new Promise<TestServerControls>((resolveStart, rejectStart) => {
        const wss = new WebSocketServer({ port });

        const startupErrorHandler = (error: Error) => {
            console.error(`[Test Server Helper] Startup Error on port ${port}: ${error.message}`);
            wss.off('listening', listeningHandler); // Remove listener
            rejectStart(error); // Reject the main promise
        };

        const listeningHandler = () => {
            const address = wss.address();
            const serverUrl = `ws://localhost:${(address as any).port}`;
            console.log(`[Test Server Helper] Server listening on ${serverUrl}`);
            wss.off('error', startupErrorHandler); // Remove startup error handler

            // --- Server Logic ---
            wss.on('connection', (wsClient) => {
                console.log(`[Test Server Helper] Client connected to ${serverUrl}.`);
                // Resolve the waitForConnection promise if it's pending
                if (connectionResolver) {
                    if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
                    connectionResolver(wsClient);
                    connectionResolver = null;
                    connectionRejecter = null;
                    connectionTimeoutId = null;
                }

                wsClient.on('message', (message) => {
                    try {
                        const messageString = message.toString();
                        console.log(`[Test Server Helper] Received on port ${port}: ${messageString.substring(0, 100)}...`); // Log truncated message
                        const parsed = JSON.parse(messageString);
                        lastReceivedMessage = parsed;

                        // Respond to SessionConnectCommand with compressed CommandSucceeded
                        if (parsed?.simplename === 'SessionConnectCommand') {
                            console.log(`[Test Server Helper] Port ${port}: Received SessionConnectCommand, sending compressed CommandSucceeded.`);
                            const response = { command: 'CommandSucceeded' };
                            sendCompressedData(wsClient, response, port); // Pass port for logging
                        }
                        // Add other command handlers here if needed

                    } catch (e) {
                        console.error(`[Test Server Helper] Port ${port}: Error processing message:`, e);
                        lastReceivedMessage = { error: 'Failed to parse message', raw: message.toString() };
                    }
                });

                wsClient.on('close', (code) => {
                    console.log(`[Test Server Helper] Port ${port}: Client disconnected (Code: ${code}).`);
                });

                wsClient.on('error', (error) => {
                    console.error(`[Test Server Helper] Port ${port}: Client WebSocket error:`, error);
                });
            });

            // General server runtime error handler
            wss.on('error', (error) => {
                console.error(`[Test Server Helper] Port ${port}: Server runtime error:`, error);
            });
            // --- End Server Logic ---


            // Function to wait for the next client connection
            const waitForConnection = (timeout = 5000): Promise<WebSocket> => {
                return new Promise((resolveWait, rejectWait) => {
                    console.log(`[Test Server Helper] Port ${port}: Waiting for client connection...`);
                    connectionResolver = resolveWait;
                    connectionRejecter = rejectWait; // Store rejecter
                    connectionTimeoutId = setTimeout(() => {
                        console.error(`[Test Server Helper] Port ${port}: Timeout waiting for client connection.`);
                        connectionResolver = null;
                        connectionRejecter = null;
                        rejectWait(new Error(`Timeout waiting for client connection on port ${port}`));
                    }, timeout);
                });
            };

            // Function to send pako-compressed data
            const sendCompressed = (client: WebSocket, data: any) => {
                sendCompressedData(client, data, port);
            };

            // Close function
            const close = (): Promise<void> => {
                console.log(`[Test Server Helper] Port ${port}: Closing server...`);
                return new Promise((resolveClose, rejectClose) => {
                    // Stop accepting new connections immediately
                    wss.close((err) => {
                        if (err) {
                            console.error(`[Test Server Helper] Port ${port}: Error closing server listener:`, err);
                            // Continue to close clients even if server close fails initially
                        } else {
                            console.log(`[Test Server Helper] Port ${port}: Server listener closed.`);
                        }

                        // Close existing client connections
                        const clientClosePromises: Promise<void>[] = [];
                        console.log(`[Test Server Helper] Port ${port}: Closing ${wss.clients.size} client(s)...`);
                        wss.clients.forEach(client => {
                            if (client.readyState === WebSocket.OPEN) {
                                clientClosePromises.push(new Promise<void>(resolveClient => {
                                    client.on('close', () => resolveClient());
                                    client.terminate(); // Force close after stopping server
                                }));
                            } else if (client.readyState !== WebSocket.CLOSED) {
                                client.terminate(); // Terminate if closing or connecting
                            }
                        });

                        Promise.all(clientClosePromises).then(() => {
                            console.log(`[Test Server Helper] Port ${port}: All clients closed/terminated.`);
                            lastReceivedMessage = null; // Reset state
                            connectionResolver = null;
                            connectionRejecter = null;
                            if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
                            resolveClose();
                        }).catch(clientErr => {
                            console.error(`[Test Server Helper] Port ${port}: Error closing clients:`, clientErr);
                            rejectClose(clientErr); // Reject if client closing fails
                        });
                    });
                });
            };

            // Resolve the main promise with the server controls
            resolveStart({
                server: wss,
                url: serverUrl,
                close,
                waitForConnection,
                getLastReceivedMessage: () => lastReceivedMessage,
                sendCompressed,
            });

        }; // End listeningHandler

        wss.once('listening', listeningHandler);
        wss.once('error', startupErrorHandler);

    }); // End main promise
}

// Helper function to compress and send data
function sendCompressedData(client: WebSocket, data: any, port: number) {
    if (client.readyState === WebSocket.OPEN) {
        try {
            const jsonString = JSON.stringify(data);
            const encoded = new TextEncoder().encode(jsonString);
            const compressed = pako.deflate(encoded);
            client.send(compressed);
            console.log(`[Test Server Helper] Port ${port}: Sent compressed data: ${jsonString.substring(0,100)}...`);
        } catch (e) {
            console.error(`[Test Server Helper] Port ${port}: Error compressing/sending data:`, e);
        }
    } else {
        console.warn(`[Test Server Helper] Port ${port}: Attempted to send data to a non-open client (state: ${client.readyState}).`);
    }
}
