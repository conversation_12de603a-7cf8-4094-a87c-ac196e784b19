// tests/websocket-server-node.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { WebSocketServer, WebSocket } from 'ws';

const TEST_PORT = 8999; // Ensure this port is free
let wss: WebSocketServer | null = null;
let serverUrl: string;

// --- Server Setup & Teardown (using Vitest hooks) ---
beforeAll(async () => {
    console.log(`[Server Setup] Starting WebSocket server on port ${TEST_PORT}...`);
    try {
        wss = await new Promise<WebSocketServer>((resolve, reject) => {
            const server = new WebSocketServer({ port: TEST_PORT });

            server.on('listening', () => {
                const address = server.address();
                if (address && typeof address !== 'string') {
                    serverUrl = `ws://localhost:${address.port}`;
                    console.log(`[Server Setup] Server listening on ${serverUrl}`);
                    server.removeAllListeners('error'); // Remove error listener once listening
                    resolve(server);
                } else {
                    const errMsg = `[Server Setup] Failed to get server address. Address: ${address}`;
                    console.error(errMsg);
                    server.close(() => reject(new Error(errMsg)));
                }
            });

            server.on('error', (error) => {
                console.error(`[Server Setup] Server error during startup: ${error.message}`);
                server.removeAllListeners('listening'); // Remove listening listener on error
                server.close(() => reject(error)); // Ensure server is closed on error
            });

            server.on('connection', (wsClient) => {
                console.log('[Test Server] Client connected');
                wsClient.send('Hello from server!'); // Send message on connection

                wsClient.on('message', (message) => {
                    console.log(`[Test Server] Received message: ${message.toString()}`);
                    // Echo back the message for potential bi-directional tests later
                    wsClient.send(`Server received: ${message.toString()}`);
                });

                wsClient.on('close', (code, reason) =>
                    console.log(`[Test Server] Client disconnected (Code: ${code}, Reason: ${reason.toString()})`)
                );
                wsClient.on('error', (error) =>
                    console.error('[Test Server] Client error:', error)
                );
            });

            server.on('error', (error) => { // General server runtime error
                console.error(`[Test Server] Runtime error: ${error.message}`);
            });
        });
        console.log('[Server Setup] Server startup promise resolved.');
    } catch (error: any) {
        console.error(`[Server Setup] Failed to start server: ${error.message}`);
        wss = null; // Ensure wss is null if setup failed
        throw error; // Re-throw to fail the test suite setup
    }
}, 30000); // Increased timeout for server startup

afterAll(async () => {
    console.log("[Server Teardown] Starting cleanup...");
    if (!wss) {
        console.log('[Server Teardown] Server was not running or already cleaned up.');
        return;
    }

    const serverInstance = wss; // Capture instance before potentially setting to null
    wss = null; // Prevent reuse during teardown race conditions

    await new Promise<void>((resolve, reject) => {
        console.log('[Server Teardown] Closing Test WebSocket Server...');

        // Gracefully close connected clients first
        // let closedClients = 0; // TODO: Use for tracking closed clients
        const totalClients = serverInstance.clients.size;
        if (totalClients > 0) {
            console.log(`[Server Teardown] Closing ${totalClients} client(s)...`);
            serverInstance.clients.forEach((client) => {
                if (client.readyState === WebSocket.OPEN) {
                    client.close(1001, "Server shutting down"); // Close gracefully
                }
                // If not open, terminate might be needed, but graceful close is preferred
                // client.terminate(); // Force close if needed
            });
            // Give clients a moment to close gracefully (optional, adjust as needed)
            // await new Promise(resolveDelay => setTimeout(resolveDelay, 100));
        }


        // Now close the server
        serverInstance.close((err) => {
            if (err) {
                console.error("[Server Teardown] Error closing test server:", err);
                reject(err);
            } else {
                console.log("[Server Teardown] Test WebSocket Server closed successfully.");
                resolve();
            }
        });
    });
    console.log("[Server Teardown] Cleanup complete.");
}, 10000); // Timeout for server teardown

// --- Test Case ---
describe('WebSocket Server (Node.js Client Test)', () => {
    it('should connect a Node.js client, receive a message, and close gracefully', async () => {
        console.log("[Test Case] Running test...");
        expect(wss, "WebSocket server instance should exist").toBeDefined();
        expect(serverUrl, "Server URL should be defined").toBeDefined();

        const receivedMessage = await new Promise<string>((resolve, reject) => {
            console.log(`[Test Client] Connecting to ${serverUrl}...`);
            const wsClient = new WebSocket(serverUrl);

            const connectTimeout = setTimeout(() => {
                console.error('[Test Client] Connection timed out.');
                wsClient.terminate(); // Force close on timeout
                reject(new Error('WebSocket connection timed out'));
            }, 5000); // 5 second connection timeout

            const messageTimeout = setTimeout(() => {
                console.error('[Test Client] Message receive timed out.');
                wsClient.terminate(); // Force close on timeout
                reject(new Error('WebSocket message receive timed out'));
            }, 7000); // 7 second message timeout (includes connection time)


            wsClient.on('open', () => {
                clearTimeout(connectTimeout); // Clear connection timeout
                console.log('[Test Client] Connected successfully.');
                // Optional: Send a message to the server if needed for the test
                // wsClient.send('Hello from Node client!');
            });

            wsClient.on('message', (message: Buffer) => {
                clearTimeout(messageTimeout); // Clear message timeout
                const messageStr = message.toString();
                console.log(`[Test Client] Received message: "${messageStr}"`);
                wsClient.close(1000, 'Test complete'); // Close gracefully after receiving message
                console.log('[Test Client] Closing connection after receiving message.');
                resolve(messageStr); // Resolve the promise with the message
            });

            wsClient.on('error', (error) => {
                clearTimeout(connectTimeout);
                clearTimeout(messageTimeout);
                console.error('[Test Client] WebSocket error:', error);
                // Ensure termination if error occurs before open/message
                if (wsClient.readyState !== WebSocket.CLOSED && wsClient.readyState !== WebSocket.CLOSING) {
                    wsClient.terminate();
                }
                reject(error);
            });

            wsClient.on('close', (code, reason) => {
                clearTimeout(connectTimeout);
                clearTimeout(messageTimeout);
                console.log(`[Test Client] Connection closed (Code: ${code}, Reason: "${reason.toString()}")`);
                // If the promise hasn't resolved (i.e., no message received) and close wasn't clean (code 1000), reject.
                // Note: wsClient.close(1000) above triggers this, but resolve() already happened.
                if (code !== 1000 && code !== 1001 ) { // 1001 = Going Away (server shutdown)
                    // Check if resolve has already been called
                    // This check is tricky without extra state; rely on timeouts mainly
                    // reject(new Error(`WebSocket closed unexpectedly. Code: ${code}, Reason: ${reason.toString()}`));
                    console.warn(`[Test Client] Connection closed with unexpected code ${code}`)
                }
            });
        });

        console.log("[Test Case] Message promise resolved.");
        expect(receivedMessage).toBe('Hello from server!');
        console.log("[Test Case] Assertion passed.");
    }, 10000); // Test timeout
});
