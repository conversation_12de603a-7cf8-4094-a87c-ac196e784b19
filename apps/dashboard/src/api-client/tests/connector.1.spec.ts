// tests/connector-browser.spec.ts
import { describe, it, expect } from 'vitest';
import { Connector } from '../connector/connector.gemini';
import type { EngineCommandEnvelope } from '../connector/types/generated';

// Patch the handleBeforeUnload method to use code 1000 instead of 1001
// This avoids the InvalidAccessError in the browser environment
if (typeof window !== 'undefined') {
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function(type:any, listener:any, options:any) {
        if (type === 'beforeunload') {
            // Don't actually attach the beforeunload event in tests
            // as it can interfere with Vitest's browser management
            return;
        }
        return originalAddEventListener.call(this, type, listener, options);
    };
}

describe('Connector - Comprehensive Tests', () => {
    const WS_URL = import.meta.env.VITE_TEST_WS_URL || 'ws://localhost:9002';

    // Run one basic test to verify connectivity works
    it('should connect, send message, and receive response', async () => {
        // Start with clean state
        Connector.reset();

        // Create promise to track command receipt
        let commandReceived = false;

        // Initialize Connector
        Connector.create({
            url: `${WS_URL}/socket/`,
            session_id: 'test-session-id',
            show_connector_log: true,
            clientCommandHandlers: {
                onCommandSucceeded: () => {
                    commandReceived = true;
                }
            }
        });

        // Wait a moment for connection to establish
        await new Promise(resolve => setTimeout(resolve, 500));

        // Send test message
        const message: EngineCommandEnvelope = {
            session_id: 'test-session-id',
            simplename: 'SessionConnectCommand',
            classname: 'SessionConnectCommand',
            command: {
                browser_name: 'test',
                browser_os: 'test',
                browser_version: '1.0',
                sid: 'test',
                state: 'OPENED'
            }
        };

        Connector.publish(message);

        // Wait for response with timeout
        const startTime = Date.now();
        const timeout = 5000;

        while (!commandReceived && (Date.now() - startTime) < timeout) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Check if we received the command
        expect(commandReceived).toBe(true);

        // Verify connector state
        const state = Connector.getState();
        expect(state.isConnected).toBe(true);
        expect(state.messageCount).toBeGreaterThan(0);

        // Clean up
        Connector.reset();
    }, 10000);
});
