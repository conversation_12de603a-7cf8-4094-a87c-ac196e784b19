# Summary: Testing Web Workers with Vitest


1. In `beforeEach`, we replace the global `self` object with our mock:
   ```javascript
   globalThis.self = mockSelf
   ```

2. When we import the unzip-worker module:
   ```javascript
   import('@/unzip-worker')
   ```
   The worker's code runs and assigns its message handler to what it thinks is the global `self` (but is actually our mock):
   ```javascript
   self.onmessage = function (event) { ... }
   ```

3. When we call:
   ```javascript
   mockSelf.onmessage({ data: arrayBuffer } as MessageEvent<ArrayBuffer>)
   ```
   We're directly executing the worker's message handler function that was previously assigned to our mock when we 
   imported the worker file, because the worker assigs `self.onmessage` and `self.postMessage` to what it thinks 
   is the 
   global 
   `self`. but in reality it's now our mock.


4. Inside the worker's message handler, when it calls:
   ```javascript
   self.postMessage(json)
   ```
   It's actually calling our spy function `mockSelf.postMessage`, which lets us verify it was called with the correct parameters.

This approach effectively "flattens" the normally separate worker thread into your test's execution context, allowing you to test its logic directly without actually running a real web worker.
