// tests/unzip-worker.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import pako from 'pako'

// Define a type for the mocked self, matching the expected properties
// Adapt this interface based on what properties your worker actually uses from 'self'
interface MockWorkerGlobalScope {
    postMessage: ReturnType<typeof vi.fn>;
    onmessage: ((event: MessageEvent<ArrayBuffer>) => void) | null;
    onerror?: (event: ErrorEvent) => void;
    addEventListener: ReturnType<typeof vi.fn>; // Add if used
    removeEventListener: ReturnType<typeof vi.fn>; // Add if used
    // Add other WorkerGlobalScope properties if accessed by the worker
}

const mockSelf: MockWorkerGlobalScope = {
    postMessage: vi.fn(),
    onmessage: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
};

// Store the original 'self' if it exists (it might not in pure Node)
const originalSelf = globalThis.self;

describe('Unzip Worker', () => {
    beforeEach(async () => { // <-- Make the hook async
        vi.resetAllMocks()
        // Assign the mock. Use 'as any' for pragmatic type compatibility.
        globalThis.self = mockSelf as any;
        // Await the dynamic import to ensure the worker code runs and sets up 'onmessage'
        await import('../connector/unzip-worker'); // <-- Await the import, DO NOT return it
    })

    afterEach(() => {
        // Restore the original globalThis.self
        globalThis.self = originalSelf;
        // Reset modules if needed, ensures a clean state if the module has side effects or internal state
        vi.resetModules();
    })

    it('should correctly unzip and parse valid data', () => { // No async needed here unless awaiting async ops
        // Ensure the worker script successfully assigned the onmessage handler
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }

        const testObject = { test: 'data', number: 123 }
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode(JSON.stringify(testObject))
        const compressed = pako.deflate(encoded) // Use deflate, as worker uses inflate
        const arrayBuffer = compressed.buffer

        // Simulate receiving a message
        const mockEvent = { data: arrayBuffer } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent); // Directly call the assigned handler

        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(testObject)
    })

    it('should handle pako errors and post null', () => { // No async needed
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }

        // Create data that will cause pako.inflate to throw an error
        const invalidData = new ArrayBuffer(10);
        // Fill with some non-valid gzip/zlib data if necessary, though empty often suffices
        // new Uint8Array(invalidData).fill(1);

        const mockEvent = { data: invalidData } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent);

        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null) // Expect null on error
    })

    it('should handle JSON parsing errors and post null', () => { // No async needed
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }

        // Create data that decompresses but isn't valid JSON
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode("this is not json")
        const compressed = pako.deflate(encoded)
        const arrayBuffer = compressed.buffer

        const mockEvent = { data: arrayBuffer } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent);

        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null) // Expect null on error
    })
})
