// noinspection JSUnusedGlobalSymbols

import { proxy } from 'valtio';
import { devtools } from 'valtio/utils';
import type {} from '@redux-devtools/extension';
import {
    AuUserRole,
    ClientSocketState,
    PageName,
    type AuctionRowElement,
    type CompanyElement,
    type CounterpartyCreditElement,
    type DeAuctionValue,
    type SessionUserValue,
    type TimeValue,
    type UserElement
} from '../connector/types/generated';

interface DomainStore {
    auction_rows: AuctionRowElement[]
    companies: CompanyElement[]
    counterparty_credits: CounterpartyCreditElement[]
    de_auction: DeAuctionValue | null
    session_user: SessionUserValue | null
    time: TimeValue | null
    users: UserElement[]
    seconds_since_last_message_received: number

    

    // Methods
    setAuctionRows: (rows: AuctionRowElement[]) => void
    setCompanies: (companies: CompanyElement[]) => void
    setCounterpartyCreditElements: (credits: CounterpartyCreditElement[]) => void
    setDeAuction: (auction: DeAuctionValue | null) => void
    setSessionUser: (user: SessionUserValue | null) => void
    toggleSessionUserRole: () => void
    setCurrentPage: (page: PageName) => void
    setTime: (time: TimeValue | null) => void
    setUsers: (users: UserElement[]) => void
    reset: () => void
    patch: (updates: Partial<DomainStore>) => void

}

const createStore = (initialState: Partial<DomainStore> = {}) => {
    const store = proxy<DomainStore>({
        // Default state
        auction_rows: [],
        companies: [],
        counterparty_credits: [],
        de_auction: null,
        session_user: null,
        time: null,
        users: [],
        seconds_since_last_message_received: 0,

        // Methods
        setAuctionRows(rows) {
            this.auction_rows = rows;
        },

        setCompanies(companies) {
            this.companies = companies;
        },

        setCounterpartyCreditElements(credits) {
            this.counterparty_credits = credits;
        },

        setDeAuction(auction) {
            this.de_auction = auction;
        },

        setSessionUser(user) {
            this.session_user = user;
            // Set default page to LOGIN_PAGE if no session user
            if (!user) {
                this.session_user = {
                    current_page: PageName.LOGIN_PAGE,
                    session_id: '',
                    company_id: '',
                    company_longname: '',
                    company_shortname: '',
                    current_auction_id: "",
                    isAuctioneer: false,
                    isOnline: false,
                    role: null,
                    socket_state: ClientSocketState.OPENED,
                    user_id: '',
                    username: ''
                };
            }
        },

        setCurrentPage(page) {
            if (this.session_user) {
                this.session_user.current_page = page;
            }
        },

        toggleSessionUserRole(){
            if (this.session_user) {
                this.session_user.isAuctioneer = !this.session_user.isAuctioneer;
                if(this.session_user.role == AuUserRole.AUCTIONEER){
                    this.session_user.role = AuUserRole.TRADER;
                } else {
                    this.session_user.role = AuUserRole.AUCTIONEER
                }
            }
        },

        setTime(time) {
            this.time = time;
        },

        setUsers(users) {
            this.users = users;
        },

        reset() {
            this.auction_rows = [];
            this.companies = [];
            this.counterparty_credits = [];
            this.de_auction = null;
            this.session_user = null;
            this.time = null;
            this.users = [];
            this.seconds_since_last_message_received = 0;
        },

        patch(updates) {
            Object.assign(this, updates);
        },

        // Override with any initial state
        ...initialState
    });

    // Register this instance with devtools
    devtools(store, { name: 'Domain Store' });

    return store;
};

// Create the real store
const realStore = createStore();

// Mock store handling
let mockStore: DomainStore | null = null;

export const setMockDomainStore = (mockState: Partial<DomainStore>) => {
    mockStore = createStore(mockState);
};

export const clearMockDomainStore = () => {
    mockStore = null;
};

export const getDomainStore = () => mockStore || realStore;

// Export type
export type { DomainStore };
