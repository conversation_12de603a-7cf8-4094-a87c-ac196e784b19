// src/unzip-worker.ts
import pako from 'pako';

const debug = false; // Toggle as needed
const ctx: Worker = self as any;
if (debug) console.log('Worker starting...');

ctx.onmessage = function (event: MessageEvent<ArrayBuffer>) {
	try {
		if (debug) console.log('Received message in worker:', `ArrayBuffer (${event.data.byteLength} bytes)`);
		const arrayBuffer = event.data;
		const unzipped = pako.inflate(new Uint8Array(arrayBuffer));
		const decoded = new TextDecoder().decode(unzipped);
		const json = JSON.parse(decoded);
		if (debug) console.log('Parsed message in worker:', json);
		ctx.postMessage(json);
	} catch (e: any) {
		if (debug) console.error('Error in unzip-worker:', e.message || e);
		//ctx.postMessage({ error: e.message || e });
		ctx.postMessage(null); // Changed to null to match test expectations
	}
};
