import {ClientCommand, CommandType, EngineCommandEnvelope} from "./types/generated";
import InlineWorker from './unzip-worker.ts?worker&inline';

// Define the IConnector interface
export interface IConnector {
    create(options: ConnectorOptions): IConnector;
    publish(envelope: EngineCommandEnvelope): void;
    reset(): void;
    close(): void;
    getState():ConnectorState;
    getQueueSize(): number;
}

export interface ConnectorOptions {
    url?: string;
    session_id?: string;
    show_connector_log?: boolean;
    clientCommandHandlers?: {
        // Dynamically create keys like 'onCommandSucceeded', 'onShowMessage', etc.
        [K in keyof typeof CommandType as `on${K}`]?: (cmd: ClientCommand & { command: CommandType }) => void;
    };
    onSecondsSinceLastMessage?: (seconds: number) => void;
    onTerminate?: (reason: { reason: string }) => void;
    onError?: (error: Error) => void;
    onOpen?: (event: Event) => void;
    onClose?: (event: CloseEvent) => void;
    onMessage?: (data: any) => void; // Callback for raw message data
    onStateChange?: (state: ConnectorState) => void; // Callback for state changes
}

export interface ConnectorState {
    isConnected: boolean;
    messageCount: number;
    lastMessageTime: number;
    reconnectAttempts: number;
    secondsSinceLastMessage: number;
}


// --- Module State ---
let _options: ConnectorOptions | null = null;
let _outgoingQueue: EngineCommandEnvelope[] = [];
let _ws: WebSocket | null = null;
let _worker: Worker | null = null;
let _messageTrackingInterval: number | null = null;
let _reconnectTimeoutId: number | null = null; // Use Timeout ID for clarity
let _state: ConnectorState = _getInitialState();
let _isInitialized = false; // Tracks if create() has been called and not reset()
let _isAttemptingConnection = false; // Prevents concurrent connection attempts

// --- Constants ---
const MAX_RECONNECTION_ATTEMPTS = 10;
const MAX_RECONNECTION_DELAY = 30000; // 30 seconds
const INITIAL_RECONNECTION_DELAY = 1000; // 1 second
// Define QUIET_COMMANDS based on CommandType enum keys
const QUIET_COMMANDS: string[] = [CommandType.SetLiveStore, CommandType.AddElements];

function getDefaultWebSocketUrl(): string {
    // Check for Vite environment variable first
    if (import.meta.env.VITE_WEBSOCKET_URL) {
        return import.meta.env.VITE_WEBSOCKET_URL;
    } else {
        // In development (e.g., using Vite dev server), explicitly use localhost:4040
        if (import.meta.env.DEV) {
            return 'ws://localhost:4040/socket/'; // Default dev URL
        }
        // In production, derive from current window location
        const location = window.location;
        // Ensure ws:// or wss:// based on http:// or https://
        const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
        return `${protocol}//${location.host}/socket/`; // Default prod URL derived from host
    }
}

// Generate a random session ID once per module load if needed as a fallback
const random_session_id: string = crypto.randomUUID();


// --- Internal Helper Functions ---

/** Creates the initial state object */
function _getInitialState(): ConnectorState {
    return {
        isConnected: false,
        messageCount: 0,
        lastMessageTime: Date.now(), // Initialize with current time
        reconnectAttempts: 0,
        secondsSinceLastMessage: 0,
    };
}

/** Logs messages conditionally based on options */
function _log(level: 'log' | 'warn' | 'error', ...args: any[]): void {
    if (level === 'error' || level === 'warn' || _options?.show_connector_log) {
        console[level]('Connector:', ...args);
    }
}

/** Converts WebSocket readyState number to string */
function _getReadyStateString(readyState: number | undefined): string {
    if (readyState === undefined) return 'UNKNOWN (-1)';
    switch (readyState) {
        case WebSocket.CONNECTING: return 'CONNECTING (0)';
        case WebSocket.OPEN: return 'OPEN (1)';
        case WebSocket.CLOSING: return 'CLOSING (2)';
        case WebSocket.CLOSED: return 'CLOSED (3)';
        default: return `UNKNOWN (${readyState})`;
    }
}

/** Safely calls user-provided callbacks from ConnectorOptions */
function _callUserCallback<T extends (...args: any[]) => void>(callback: T | undefined, ...args: Parameters<T>): void {
    if (typeof callback === 'function') {
        try {
            callback(...args);
        } catch (error) {
            _log('error', `Error in user-provided callback (${callback.name || 'anonymous'}):`, error);
            // Optionally call onError callback for errors within other callbacks?
            // _callUserCallback(_options?.onError, new Error(`Error in user-provided callback: ${error}`));
        }
    }
}

/** Notifies subscribers about state changes */
function _notifyStateChange(): void {
    // Avoid notifying if not initialized
    if (!_isInitialized) return;
    // Pass a *copy* of the state to the callback
    _callUserCallback(_options?.onStateChange, { ..._state });
}

/** Clears all active timers (reconnect, message tracking) */
function _clearTimers(): void {
    if (_messageTrackingInterval !== null) {
        window.clearInterval(_messageTrackingInterval);
        _messageTrackingInterval = null;
    }
    _stopReconnectionTimer(); // Use specific function
}

/** Stops the reconnection timer if it's active */
function _stopReconnectionTimer(): void {
    if (_reconnectTimeoutId !== null) {
        window.clearTimeout(_reconnectTimeoutId);
        _reconnectTimeoutId = null;
        // _log('log', "Reconnection timer stopped."); // Usually too verbose
    }
}

/** Performs cleanup of all resources (WebSocket, Worker, Timers) */
function _cleanup(): void {
    _log('log', "Running cleanup...");
    _clearTimers();

    if (_worker) {
        _worker.terminate();
        _worker = null;
        _log('log', "Worker terminated.");
    }

    if (_ws) {
        // Remove listeners before closing to prevent triggering reconnect logic during cleanup
        _ws.onopen = null;
        _ws.onmessage = null;
        _ws.onerror = null;
        _ws.onclose = null;
        const currentState = _ws.readyState;
        if (currentState === WebSocket.OPEN || currentState === WebSocket.CONNECTING) {
            _log('log', `Closing WebSocket (state: ${_getReadyStateString(currentState)})...`);
            _ws.close(1000, "Client initiated cleanup"); // Use code 1000 for normal closure
        } else {
            _log('log', `WebSocket already closing/closed (state: ${_getReadyStateString(currentState)}).`);
        }
        _ws = null;
    }

    // Reset state *after* potential event emissions during cleanup
    const previousState = _state;
    _state = _getInitialState();
    _outgoingQueue = [];
    _isInitialized = false; // Mark as uninitialized
    _isAttemptingConnection = false;
    _options = null; // Clear options

    if (typeof window !== 'undefined') {
        window.removeEventListener('beforeunload', _handleBeforeUnload);
    }

    // Notify state change after reset if state actually changed
    if (previousState.isConnected !== _state.isConnected) {
        _notifyStateChange();
    }
    _log('log', "Cleanup complete.");
}

/** Handles incoming commands by finding and executing the appropriate handler from options */
function _handleCommand(cmd: ClientCommand): void {
    // Ensure options and handlers exist before proceeding
    if (!cmd?.command || !_options?.clientCommandHandlers) {
        if (!cmd?.command) _log('warn', "Received message without command property:", cmd);
        return;
    }

    // Construct the handler key, e.g., 'onShowMessage'
    const handlerKey = `on${cmd.command}` as keyof typeof _options.clientCommandHandlers;
    const handler = _options.clientCommandHandlers[handlerKey];

    if (typeof handler === 'function') {
        try {
            // Type assertion needed because handler is dynamically accessed
            (handler as (c: ClientCommand) => void)(cmd);
        } catch (error) {
            _log('error', `Error executing handler for command ${cmd.command}:`, error);
            _callUserCallback(_options?.onError, new Error(`Handler error for ${cmd.command}: ${error}`));
        }
    } else {
        // Only warn if it's not a quiet command or if logging is enabled
        if (_options?.show_connector_log && !QUIET_COMMANDS.includes(cmd.command)) {
            _log('warn', 'No handler found for command:', cmd.command);
        }
    }

    if (_options?.show_connector_log && !QUIET_COMMANDS.includes(cmd.command)) {
        _log('log', 'WS <<<', cmd.command /*, cmd */); // Avoid logging full command data unless needed
    }
}

/** Sets up the Web Worker for processing binary data */
function _setupWorker(): boolean {
    if (_worker) {
        _log('warn', "Worker already exists. Terminating old one before creating new.");
        _worker.terminate();
        _worker = null;
    }
    try {
        // Use InlineWorker imported at the top
        _worker = new InlineWorker();
        _log('log', 'InlineWorker created successfully');

        _worker.onmessage = (event: MessageEvent) => {
            const cmd = event.data;
            // Basic validation of the worker response
            if (cmd === null || typeof cmd !== 'object' || !cmd.command) {
                _log('error', 'Worker returned invalid/null message:', cmd);
                const err = new Error('Worker failed to process binary message or returned invalid data');
                _callUserCallback(_options?.onError, err);
            } else {
                // _log('log', 'Worker processed data for command:', cmd.command); // Usually too verbose
                _handleCommand(cmd as ClientCommand);
            }
        };

        _worker.onerror = (error: ErrorEvent) => {
            _log('error', 'Worker error:', error);
            const errorMessage = `Worker error: ${error.message} at ${error.filename}:${error.lineno}`;
            const err = new Error(errorMessage);
            _callUserCallback(_options?.onError, err);
            // Consider if a worker error should trigger cleanup/reset
            // reset();
        };
        return true;
    } catch (error) {
        _log('error', 'Failed to create InlineWorker:', error);
        _worker = null;
        const err = error instanceof Error ? error : new Error(`Failed to create worker: ${error}`);
        _callUserCallback(_options?.onError, err);
        return false;
    }
}

/** Sends queued messages if connected */
function _flushQueue(): void {
    if (!_state.isConnected || _ws?.readyState !== WebSocket.OPEN) {
        // Don't log warning here, it's normal to be called when not ready
        return;
    }

    if (_outgoingQueue.length > 0) {
        _log('log', `Flushing ${_outgoingQueue.length} queued messages.`);
        // Use a temporary queue to avoid issues if sending triggers more publishes
        const queueToFlush = [..._outgoingQueue];
        _outgoingQueue = []; // Clear original queue immediately

        queueToFlush.forEach(envelope => {
            // Directly send here, as we've already checked connection state
            try {
                _ws!.send(JSON.stringify(envelope)); // Use non-null assertion as we checked state
                if (_options?.show_connector_log) {
                    _log('log', '>>> flushed:', envelope.simplename /*, envelope */);
                }
            } catch (error) {
                const err = error instanceof Error ? error : new Error(`Failed to send flushed message: ${error}`);
                _log('error', "Error sending flushed message:", err, envelope);
                _callUserCallback(_options?.onError, err);
                // Re-queue the failed message? Or rely on connection error handling?
                _outgoingQueue.unshift(envelope); // Put back at the front
            }
        });
    }
}

/** Updates the time since the last message */
function _updateMessageTracking(): void {
    if (!_isInitialized) return; // Don't track if not initialized
    const now = Date.now();
    // Ensure lastMessageTime is valid before calculating difference
    const lastMsgTime = (_state.lastMessageTime && _state.lastMessageTime <= now) ? _state.lastMessageTime : now;
    const seconds = Math.floor((now - lastMsgTime) / 1000);

    // Only update and notify if the value changed
    if (seconds !== _state.secondsSinceLastMessage) {
        _state.secondsSinceLastMessage = seconds;
        _callUserCallback(_options?.onSecondsSinceLastMessage, _state.secondsSinceLastMessage);
        _notifyStateChange(); // Notify general state change as well
    }
}

/** Starts the interval timer for message tracking */
function _startMessageTracking(): void {
    if (_messageTrackingInterval !== null) {
        window.clearInterval(_messageTrackingInterval);
    }
    _updateMessageTracking(); // Initial update
    _messageTrackingInterval = window.setInterval(_updateMessageTracking, 1000);
}

/** Calculates the delay for the next reconnection attempt with exponential backoff and jitter */
function _calculateReconnectDelay(): number {
    const baseDelay = INITIAL_RECONNECTION_DELAY;
    // Exponential backoff: initialDelay * 2^(attemptCount - 1)
    const attemptFactor = Math.max(0, _state.reconnectAttempts - 1);
    const exponentialDelay = baseDelay * Math.pow(2, attemptFactor);
    // Cap the maximum delay
    const cappedDelay = Math.min(exponentialDelay, MAX_RECONNECTION_DELAY);
    // Add jitter (±12.5% of the capped delay)
    const jitterFactor = 0.25;
    const jitterRange = cappedDelay * jitterFactor;
    const jitter = (Math.random() * jitterRange) - (jitterRange / 2);
    // Ensure delay is at least the base delay
    return Math.max(baseDelay, Math.floor(cappedDelay + jitter));
}

/** Schedules the next reconnection attempt */
function _scheduleReconnection(): void {
    _stopReconnectionTimer(); // Ensure no existing timer is running

    // Check initialization status *before* proceeding
    if (!_isInitialized) {
        _log('log', "Reconnection scheduling aborted: Connector not initialized.");
        return;
    }

    // Update state to reflect disconnection if not already set
    const needsStateUpdate = _state.isConnected;
    if (needsStateUpdate) {
        _state.isConnected = false;
    }
    // Update attempts *before* checking max attempts
    _state.reconnectAttempts++;

    if (_state.reconnectAttempts > MAX_RECONNECTION_ATTEMPTS) {
        const errorMsg = `Failed to reconnect after ${MAX_RECONNECTION_ATTEMPTS} attempts. Giving up.`;
        _log('warn', errorMsg);
        const err = new Error(errorMsg);
        _callUserCallback(_options?.onError, err);
        // Optionally call onTerminate or a specific 'reconnectFailed' callback if added to options
        // _callUserCallback(_options?.onTerminate, { reason: 'RECONNECT_FAILED' });
        if (needsStateUpdate) _notifyStateChange(); // Notify final disconnected state
        return; // Stop trying
    }

    const delay = _calculateReconnectDelay();

    _log('log', `Scheduling reconnection attempt ${_state.reconnectAttempts}/${MAX_RECONNECTION_ATTEMPTS} in ${delay}ms...`);

    // Notify state change *after* updating attempts and isConnected
    if (needsStateUpdate) _notifyStateChange();

    _reconnectTimeoutId = window.setTimeout(() => {
        _reconnectTimeoutId = null; // Clear the ID *before* attempting connection
        // Double-check state before connecting
        if (_isInitialized && !_state.isConnected && !_isAttemptingConnection) {
            _log('log', `Executing reconnection attempt ${_state.reconnectAttempts}/${MAX_RECONNECTION_ATTEMPTS}...`);
            _connectInternal().catch(error => {
                // Error is logged within _connectInternal's handlers
                _log('error', `Reconnection attempt ${_state.reconnectAttempts} failed:`, error);
                // Setup the next attempt if still relevant
                if (_isInitialized && !_state.isConnected) {
                    _scheduleReconnection(); // Schedule the next attempt
                }
            });
        } else {
            _log('log', `Reconnection attempt ${_state.reconnectAttempts} aborted (already connected, connecting, or not initialized).`);
        }
    }, delay);
}

/** Handles the browser's beforeunload event */
function _handleBeforeUnload(): void {
    _log('log', "Browser unloading detected.");
    _callUserCallback(_options?.onTerminate, { reason: 'BROWSER_UNLOADED' });
    // Perform minimal cleanup, browser is closing anyway
    _isInitialized = false; // Prevent further actions
    _ws?.close(1001, "Browser unloading"); // 1001 = Going Away
    _worker?.terminate();
}

/** Sets up the beforeunload listener */
function _setupBrowserEvents(): void {
    if (typeof window !== 'undefined') {
        // Remove existing listener first to prevent duplicates
        window.removeEventListener('beforeunload', _handleBeforeUnload);
        window.addEventListener('beforeunload', _handleBeforeUnload);
    }
}

/** Internal connection logic, establishes WebSocket connection and sets up handlers */
function _connectInternal(): Promise<void> {
    // Prevent concurrent connection attempts
    if (_isAttemptingConnection) {
        _log('warn', "Connection attempt already in progress. Skipping.");
        return Promise.reject(new Error("Connection attempt already in progress."));
    }
    if (!_isInitialized || !_options || !_options.url || !_options.session_id) {
        const errMsg = "Cannot connect. Connector not initialized or missing options/URL/session_id.";
        _log('error', errMsg);
        return Promise.reject(new Error(errMsg));
    }

    _isAttemptingConnection = true;
    // Notify state change if entering connecting state (isConnected is false, attempts might be > 0)
    _notifyStateChange();

    return new Promise((resolve, reject) => {
        // Close existing socket if it's not already closed
        if (_ws && _ws.readyState !== WebSocket.CLOSED) {
            _log('log', `Closing previous WebSocket (state: ${_getReadyStateString(_ws.readyState)}) before new attempt.`);
            // Ensure listeners are removed to avoid triggering old handlers or reconnection loops
            _ws.onopen = null;
            _ws.onmessage = null;
            _ws.onerror = null;
            _ws.onclose = null; // Crucial to prevent immediate reconnection trigger
            _ws.close(1000, "Client initiated new connection");
            _ws = null; // Nullify immediately
        }

        // @ts-ignore
        const url = _options!.url + _options!.session_id; // Add browser agent if needed
        _log('log', `Attempting WebSocket connection to: ${url}`);

        try {
            _ws = new WebSocket(url);
            _ws.binaryType = 'arraybuffer';
            _log('log', `WebSocket initial state: ${_getReadyStateString(_ws.readyState)}`);

            // --- WebSocket Event Handlers ---

            _ws.onopen = (event) => {
                if (!_ws || _ws.readyState !== WebSocket.OPEN || !_isInitialized) return; // Guard against stale/late events
                _log('log', 'WebSocket connected:', _options?.url);
                _state.isConnected = true;
                _state.reconnectAttempts = 0; // Reset counter on successful connection
                _stopReconnectionTimer(); // Stop any scheduled reconnections
                _isAttemptingConnection = false; // Connection attempt finished

                _notifyStateChange(); // Notify connected state
                _callUserCallback(_options?.onOpen, event);
                _flushQueue(); // Send any queued messages *after* onOpen and state update
                resolve(); // Resolve the promise on successful connection
            };

            _ws.onmessage = (event) => {
                if (!_ws || !_isInitialized) return; // Ignore messages if not ready

                _state.lastMessageTime = Date.now();
                _state.messageCount++;
                // secondsSinceLastMessage is updated by the timer

                // Call general onMessage callback first
                _callUserCallback(_options?.onMessage, event.data);

                // Then process specific command types
                if (event.data instanceof ArrayBuffer) {
                    if (!_worker) {
                        const errMsg = 'Worker not available; cannot process binary message';
                        _log('error', errMsg);
                        _callUserCallback(_options?.onError, new Error(errMsg));
                        return;
                    }
                    try {
                        _worker.postMessage(event.data, [event.data]);
                    } catch (error) {
                        const err = error instanceof Error ? error : new Error(`Failed to post message to worker: ${error}`);
                        _log('error', 'Error posting message to worker:', err);
                        _callUserCallback(_options?.onError, err);
                    }
                } else if (typeof event.data === 'string') {
                    try {
                        const jsonData = JSON.parse(event.data);
                        if (jsonData) {
                            _handleCommand(jsonData as ClientCommand);
                        } else {
                            _log('warn', "Received empty or non-JSON text message.");
                        }
                    } catch (error) {
                        const err = error instanceof Error ? error : new Error(`Error parsing text message: ${error}`);
                        _log('error', 'Error parsing text message:', err);
                        _callUserCallback(_options?.onError, err);
                    }
                } else {
                    _log('warn', "Received message of unknown type:", typeof event.data);
                }
                _notifyStateChange(); // Notify state change (message count updated)
            };

            _ws.onclose = (event) => {
                // Check if this close event is for the *current* ws instance
                // and if the connector is still supposed to be active.
                if (!_ws || event.target !== _ws || !_isInitialized) {
                    _log('log', `Ignoring stale onclose event or close after reset.`);
                    _isAttemptingConnection = false; // Ensure flag is reset if close happens during attempt
                    return;
                }

                _log('warn', `WebSocket closed. Code: ${event.code}, Reason: ${event.reason || 'No reason given'}, WasClean: ${event.wasClean}`);
                const previouslyConnected = _state.isConnected;
                _state.isConnected = false;
                _isAttemptingConnection = false; // Connection attempt finished (unsuccessfully)

                // Call onClose callback *before* scheduling reconnection
                _callUserCallback(_options?.onClose, event);

                // Notify state change if we were previously connected
                if (previouslyConnected) {
                    _notifyStateChange();
                }

                // Don't reject the promise here, onClose is expected. Schedule reconnection instead.
                _scheduleReconnection(); // Trigger reconnection logic
            };

            _ws.onerror = (event) => { // 'event' here is just a generic Event
                // Check if this error event is for the *current* ws instance
                if (!_ws || event.target !== _ws || !_isInitialized) {
                    _log('log', `Ignoring stale onerror event or error after reset.`);
                    return;
                }

                const errorMsg = 'WebSocket error occurred. Connection will likely close.';
                _log('error', errorMsg, event); // Log the raw event for details
                const err = new Error(errorMsg); // Create a generic error object

                // Call onError callback
                _callUserCallback(_options?.onError, err);

                // Don't reject the promise here. onerror is often (but not always)
                // followed by onclose, which handles state and reconnection.
                // If onerror occurs *during* connection attempt, onclose should handle reject/reconnect.
                // If onerror occurs *after* connection, onclose handles reconnect.
                _isAttemptingConnection = false; // Ensure flag is reset if error happens during attempt
            };

        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            _log('error', 'WebSocket creation failed:', err);
            _callUserCallback(_options?.onError, err);
            _isAttemptingConnection = false; // Connection attempt failed
            // If creation fails outright, schedule reconnection attempts
            _scheduleReconnection();
            reject(err); // Reject the promise if WebSocket constructor throws
        }
    });
}


// --- Public API Functions ---

/**
 * Initializes or re-initializes the Connector module.
 * Must be called before `publish` or other interactions.
 * If called while already initialized, it will first reset the existing connection.
 *
 * @param opts Configuration options for the connector.
 * @throws {Error} if essential options like url or session_id are invalid after processing defaults.
 */
function create(opts: ConnectorOptions): void {
    if (_isInitialized) {
        _log('warn', 'Connector already initialized. Resetting before creating anew.');
        reset(); // Clean up existing instance first
    }

    _log('log', 'Initializing...');

    // --- Options Processing ---
    if (!opts) {
        throw new Error("Connector options must be provided.");
    }

    let url = opts.url || getDefaultWebSocketUrl();
    if (!url) {
        throw new Error("WebSocket URL is required (either in options.url or via getDefaultWebSocketUrl).");
    }
    // Ensure URL ends with a single slash
    url = url.replace(/\/+$/, '') + '/';

    if (!url.endsWith('socket/')) {
        _log('warn', 'WebSocket URL does not end with socket/. Ensure this is correct for your server.');
        // Consider if this should be an error based on requirements
        // throw new Error('WebSocket URL should end with socket/');
    }

    let session_id = opts.session_id;
    if (!session_id) {
        _log('warn', "session_id not provided in options, using default random ID.");
        session_id = random_session_id; // Use the module-level random ID
        if (!session_id) { // Should not happen with crypto.randomUUID
            throw new Error("session_id is required and could not be generated.");
        }
    }

    // Store processed options
    _options = {
        ...opts,
        url,
        session_id,
        show_connector_log: opts.show_connector_log ?? false,
    };

    // --- Initialization ---
    _state = _getInitialState();
    _outgoingQueue = [];
    _isInitialized = true; // Mark as initialized *before* async operations
    _isAttemptingConnection = false; // Reset connection attempt flag

    _log('log', 'Options processed:', { url: _options.url, session_id: _options.session_id, show_connector_log: _options.show_connector_log });

    if (!_setupWorker()) {
        // Worker setup failed, potentially critical error
        _log('error', "Worker setup failed. Connector might not function correctly for binary data.");
        // Decide if initialization should fail completely or just warn
        // _isInitialized = false; throw new Error("Worker initialization failed.");
    }

    _setupBrowserEvents();
    _startMessageTracking();
    _notifyStateChange(); // Notify initial state

    // --- Initial Connection Attempt ---
    _log('log', "Attempting initial connection...");
    _connectInternal().catch(error => {
        _log('error', "Initial connection failed.", error);
        // _scheduleReconnection will likely be triggered by onclose/onerror handlers within _connectInternal
    });
}

/**
 * Sends a command envelope to the server.
 * If the connection is not currently open, the message will be queued.
 * Throws an error if the connector has not been initialized via `create()`.
 *
 * @param envelope The command envelope to send.
 */
function publish(envelope: EngineCommandEnvelope): void {
    if (!_isInitialized || !_options) {
        // Throw or log error? Throwing is stricter for API misuse.
        throw new Error('Connector not initialized. Call create() first.');
    }
    if (!envelope || typeof envelope !== 'object' || !envelope.simplename) {
        _log('error', "Publish called with invalid envelope:", envelope);
        return; // Don't throw for bad data, just log and ignore
    }

    // Ensure session_id is attached from the initialized options
    // @ts-ignore
    envelope.session_id = _options.session_id;

    if (_state.isConnected && _ws?.readyState === WebSocket.OPEN) {
        try {
            _ws.send(JSON.stringify(envelope));
            if (_options?.show_connector_log && !QUIET_COMMANDS.includes(envelope.simplename)) {
                _log('log', '>>> sending:', envelope.simplename /*, envelope */);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(`Failed to send message: ${error}`);
            _log('error', "Error sending message:", err, envelope);
            _callUserCallback(_options?.onError, err);
            // Queue the message if send fails, assuming it's a temporary issue
            _outgoingQueue.push(envelope);
            _log('warn', `Queued message ${envelope.simplename} after send error.`);
            // Consider triggering a connection check or relying on onerror/onclose
        }
    } else {
        _outgoingQueue.push(envelope);
        if (_options?.show_connector_log && !QUIET_COMMANDS.includes(envelope.simplename)) {
            _log('log', 'queuing:', envelope.simplename /*, envelope */);
        }
        // If disconnected, ensure a connection/reconnection attempt is triggered if not already happening
        if (!_isAttemptingConnection && !_state.isConnected && _reconnectTimeoutId === null) {
            _log('log', "Queued message while disconnected; initiating connection attempt.");
            // Don't wait for reconnect timer, try connecting now
            _connectInternal().catch(()=>{/* error handled internally */});
        }
    }
}

/**
 * Closes the connection, terminates the worker, clears timers, and resets the module state.
 * Call `create()` again to re-initialize and connect.
 */
function reset(): void {
    if (!_isInitialized) {
        _log('log', "Reset called but not initialized.");
        return;
    }
    _log('log', "Resetting connector...");
    _cleanup(); // cleanup resets _isInitialized and _options
    // No explicit 'reset' event needed as state changes will be notified
}

/**
 * Alias for reset(). Closes the connection and cleans up resources.
 */
function close(): void {
    reset();
}

/**
 * Gets a snapshot of the current internal state (read-only).
 * Note: State can change rapidly. Use `onStateChange` callback for reacting to changes.
 * @returns A copy of the internal state object.
 */
function getState(): Readonly<ConnectorState> {
    // Return a copy to prevent external modification
    return { ..._state };
}


// --- Export Public API ---

export const Connector = {
    /** Initializes or re-initializes the Connector. Must be called first. */
    create,
    /** Sends a command envelope to the server, queuing if not connected. */
    publish,
    /** Closes the connection, cleans up resources, and resets the module state. */
    reset,
    /** Alias for reset(). Closes the connection and cleans up resources. */
    close,
    /** Gets a read-only snapshot of the current connector state. */
    getState,
    /** Gets a read-only length of the queue for testing. */
    getQueueSize: () => _outgoingQueue.length,
} as IConnector;

// Optional: Default export if preferred
// export default Connector;
