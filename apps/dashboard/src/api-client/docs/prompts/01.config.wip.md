You are an expert senior software developer, skilled in typescript, javascript, nodejs, playwright, testing, and any 
other relevant skills. 
Your task is to create a test suite for a typescript package consisting of two files:
- unzip-worker.ts which is a web worker that decompresses data using pako.
- connector.ts which is a websocket client that uses the web worker to decompress data.
IMPORTANT: I want to test the connector in a real browser with playwright, agains a real websocket server.
- I do not want to mock the browser or the websocket server because I want to see how it responds to real network 
  failures. Abstracting the network is one of the great falacies of software development as you know.

Here is the unzip-worker file:
```ts
// src/unzip-worker.ts
import pako from "pako"
self.onmessage = function (event: MessageEvent<ArrayBuffer>) {
	try {
		console.log('received message in worker:' + event.data)
		const arrayBuffer = event.data
		const unzipped = pako.inflate(new Uint8Array(arrayBuffer))
		const decoded = new TextDecoder().decode(unzipped)
		const json = JSON.parse(decoded)
		self.postMessage(json)
	} catch (e) {
		console.error("Error in unzip-worker:", e)
    	self.postMessage(null)
	}
}
````
and here is the connector file:
```ts
// src/connector.ts
import {v4 as uuid} from 'uuid'
import {ClientCommand, CommandType, EngineCommandEnvelope} from '@/types/generated'
import UnzipWorker from './unzip-worker?worker'
function getDefaultWebSocketUrl(): string {
  const location = window.location
  return import.meta.env.VITE_WEBSOCKET_URL
    ? `${import.meta.env.VITE_WEBSOCKET_URL}/`
    : `${location.protocol.replace('http', 'ws')}//${location.host}/socket/`
}
const random_session_id = uuid()
export interface ConnectorOptions {
  url?: string
  session_id?: string
  show_connector_log?: boolean
  clientCommandHandlers?: {
    [K in keyof typeof CommandType as `on${K}`]?: (cmd: ClientCommand & { command: CommandType }) => void
  }
  onSecondsSinceLastMessage?: (seconds: number) => void
  onTerminate?: (reason: { reason: string }) => void
  onError?: (error: Error) => void
}
export interface ConnectorState {
  isConnected: boolean
  messageCount: number
  lastMessageTime: number
  reconnectAttempts: number
  secondsSinceLastMessage: number
}
const MAX_RECONNECT_DELAY = 10000
const QUIET_COMMANDS = ['SetLiveStore', 'AddElements']
export class Connector {
  private static instance: Connector | null = null
  private readonly options: ConnectorOptions
  private readonly outgoingQueue: EngineCommandEnvelope[] = []
  private ws: WebSocket | null = null
  private worker: Worker | null = null
  private messageTrackingInterval: number | null = null
  private reconnectInterval: number | null = null
  private state: ConnectorState
  private constructor(options: ConnectorOptions) {
    this.options = options
    this.state = this.getInitialState()
    this.setupWorker()
  }
  public static create(options: ConnectorOptions): Connector {
    if (!Connector.instance) {
      const url = options.url || getDefaultWebSocketUrl()
      Connector.instance = new Connector({ ...options, url })
      Connector.instance.connect()
    }
    return Connector.instance
  }
  public static reset(options?: ConnectorOptions): Connector | null {
    if (Connector.instance) {
      Connector.instance.cleanup()
    }
    Connector.instance = null
    return options ? Connector.create(options) : null
  }
  public connect(): Promise<void> {
    return new Promise((resolve) => {
      if (this.ws?.readyState === WebSocket.CONNECTING ||
        this.ws?.readyState === WebSocket.OPEN) {
        this.ws.close()
      }
      this.ws = new WebSocket(this.ensureTrailingSlash(this.options.url!))
      this.ws.binaryType = 'arraybuffer'
      this.ws.onopen = () => {
        if (this.options.show_connector_log) {
          console.log('WebSocket connected:', this.options.url)
        }
        this.state.isConnected = true
        this.stopReconnection()
        this.state.reconnectAttempts = 0
        this.flushQueue()
        resolve()
      }
      this.ws.onmessage = (event) => {
        this.state.lastMessageTime = Date.now()
        this.state.secondsSinceLastMessage = 0
        if (event.data instanceof ArrayBuffer) {
          this.worker?.postMessage(event.data, [event.data])
        }
        this.state.messageCount++
      }
      this.ws.onclose = () => {
        this.state.isConnected = false
        this.startReconnection()
      }
      this.ws.onerror = (error) => {
        if (this.options.show_connector_log) {
          console.error('WebSocket error:', error)
        }
        if (this.options.onError) {
          this.options.onError(new Error('WebSocket error: ' + error))
        }
        this.ws?.close()
      }
      this.setupBrowserEvents()
      this.startMessageTracking()
    })
  }
  public close(): void {
    this.cleanup()
  }
  public publish(envelope: EngineCommandEnvelope): void {
    envelope.session_id = this.options.session_id || random_session_id
    if (this.state.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(envelope))
      if (this.options.show_connector_log) {
        console.log('>>> sending:', envelope)
      }
    } else {
      this.outgoingQueue.push(envelope)
      if (this.options.show_connector_log) {
        console.log('queuing:', envelope)
      }
    }
  }
  public getState(): ConnectorState {
    return { ...this.state }
  }
  public isConnected(): boolean {
    return this.state.isConnected
  }
  public getMessageCount(): number {
    return this.state.messageCount
  }
  public getQueue(): EngineCommandEnvelope[] {
    return [...this.outgoingQueue]
  }
  private cleanup(): void {
    this.clearTimers()
    this.worker?.terminate()
    this.worker = null
    this.ws?.close()
    this.ws = null
    this.state = this.getInitialState()
    this.outgoingQueue.length = 0
    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private getInitialState(): ConnectorState {
    return {
      isConnected: false,
      messageCount: 0,
      lastMessageTime: Date.now(),
      reconnectAttempts: 0,
      secondsSinceLastMessage: 0
    }
  }
  private setupWorker(): void {
    this.worker = new UnzipWorker()
    this.worker.onmessage = (event) => {
      const cmd = event.data
      if (cmd) {
        this.handleCommand(cmd)
      }
    }
    this.worker.onerror = (error) => {
      if (this.options.onError) {
        this.options.onError(error instanceof Error ? error : new Error('Worker error'))
      }
    }
  }
  private handleCommand(cmd: ClientCommand): void {
    if (!cmd?.command || !this.options.clientCommandHandlers) return
    const handler = this.options.clientCommandHandlers[`on${cmd.command}`]
    if (handler) {
      handler(cmd)
    } else {
      console.error('No handler for command:', cmd.command)
    }
    if (this.options.show_connector_log && !QUIET_COMMANDS.includes(cmd.command)) {
      console.log('WS <<< ', cmd.command)
    }
  }
  private flushQueue(): void {
    while (this.outgoingQueue.length > 0) {
      const msg = this.outgoingQueue.shift()
      if (msg) this.publish(msg)
    }
  }
  private startMessageTracking(): void {
    this.updateMessageTracking()
    this.messageTrackingInterval = window.setInterval(() => {
      this.updateMessageTracking()
    }, 1000)
  }
  private updateMessageTracking(): void {
    this.state.secondsSinceLastMessage = Math.floor(
      (Date.now() - this.state.lastMessageTime) / 1000
    )
    if (this.options.onSecondsSinceLastMessage) {
      this.options.onSecondsSinceLastMessage(this.state.secondsSinceLastMessage)
    }
  }
  private handleBeforeUnload = () => {
    if (this.options.onTerminate) {
      this.options.onTerminate({ reason: 'BROWSER_UNLOADED' })
    }
  }
  private setupBrowserEvents(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private startReconnection(): void {
    this.clearTimers()
    const delay = this.calculateReconnectDelay()
    this.state.reconnectAttempts++
    if (this.options.show_connector_log) {
      console.log(`WebSocket closed. Reconnecting in ${delay}ms... (attempt ${this.state.reconnectAttempts})`)
    }
    setTimeout(() => this.connect(), 100)
    this.reconnectInterval = window.setInterval(() => {
      if (!this.state.isConnected) {
        if (this.options.show_connector_log) {
          console.log(`Attempting reconnection ${this.state.reconnectAttempts}...`)
        }
        this.connect()
      }
    }, delay)
  }
  private stopReconnection(): void {
    if (this.reconnectInterval !== null) {
      window.clearInterval(this.reconnectInterval)
      this.reconnectInterval = null
    }
  }
  private clearTimers(): void {
    if (this.messageTrackingInterval !== null) {
      window.clearInterval(this.messageTrackingInterval)
      this.messageTrackingInterval = null
    }
    this.stopReconnection()
  }
  private calculateReconnectDelay(): number {
    const baseDelay = Math.min(
      100 * Math.pow(2, this.state.reconnectAttempts),
      MAX_RECONNECT_DELAY
    )
    return baseDelay + Math.random() * 1000
  }
  private ensureTrailingSlash(url: string): string {
    if (!url.endsWith('/')) {
      url += '/'
    }
    if (!url.endsWith('socket/')) {
      console.warn('WebSocket URL should end with socket/')
    }
    return url
  }
}
export function createClientConnector(options: ConnectorOptions) {
  const connector = Connector.create(options)
  return {
    publish: (env: EngineCommandEnvelope) => connector.publish(env),
    close: () => connector.close()
  }
}
if (import.meta.hot) {
  import.meta.hot.accept()
}
```

the unzip-worker.ts passes this test, when running the file from intellij, by clicking on the green arrow:
```ts
// test/unzip-worker.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import pako from 'pako'
const mockSelf = {
    postMessage: vi.fn(),
    onmessage: null
}
const originalSelf = globalThis.self
describe('Unzip Worker', () => {
    beforeEach(() => {
        vi.resetAllMocks()
        globalThis.self = mockSelf
        return import('@/unzip-worker')
    })
    afterEach(() => {
        globalThis.self = originalSelf
    })
    it('should correctly unzip and parse valid data', async () => {
        if(mockSelf.onmessage == null) throw new Error("mockSelf.onmessage is null")
        const testObject = { test: 'data', number: 123 }
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode(JSON.stringify(testObject))
        const compressed = pako.deflate(encoded)
        const arrayBuffer = compressed.buffer
        mockSelf.onmessage({ data: arrayBuffer } as MessageEvent<ArrayBuffer>)
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(testObject)
    })
    it('should handle errors and return null', async () => {
        if(mockSelf.onmessage == null) throw new Error("mockSelf.onmessage is null")
        const invalidData = new ArrayBuffer(10)
        mockSelf.onmessage({ data: invalidData } as MessageEvent<ArrayBuffer>)
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null)
    })
})
```

Here is a test which proves that it's possible to create a real websocket server and test it.
This test also passes when running from intellij:
```ts
// test/websocket-test.spec.ts
import { test, expect } from '@playwright/test';
import { WebSocketServer, WebSocket } from 'ws';
const TEST_PORT = 8999;
let wss: WebSocketServer | null = null;
let serverUrl: string;
test.beforeAll(async () => {
    console.log(`beforeAll: Attempting to start server on port ${TEST_PORT}...`);
    await new Promise<void>((resolve, reject) => {
        wss = new WebSocketServer({ port: TEST_PORT });
        const onError = (error: Error) => {
            console.error(`beforeAll: Server error during startup: ${error.message}`);
            wss?.off('listening', onListening);
            wss?.off('error', onError);
            wss = null;
            reject(error);
        };
        const onListening = () => {
            const address = wss?.address();
            if (address && typeof address !== 'string') {
                serverUrl = `ws://localhost:${address.port}`;
                console.log(`beforeAll: Test WebSocket Server listening on ${serverUrl}`);
                wss?.off('error', onError);
                resolve();
            } else {
                const errMsg = 'beforeAll: Failed to get server address after listening.';
                console.error(errMsg);
                wss?.close();
                wss = null;
                reject(new Error(errMsg));
            }
        };
        wss.once('listening', onListening);
        wss.once('error', onError);
        wss.on('connection', (wsClient) => {
            console.log('Test Server: Client connected');
            wsClient.send('Hello from server!');
            wsClient.on('close', () => console.log('Test Server: Client disconnected'));
            wsClient.on('error', (error) => console.error('Test Server: Client error:', error));
        });
        wss.on('error', (error) => {
            console.error(`Test Server: Runtime error: ${error.message}`);
        });
    });
    console.log("beforeAll: Server startup promise resolved.");
});
test.afterAll(async () => {
    console.log("afterAll: Starting cleanup...");
    if (!wss) {
        console.log('afterAll: Server was not running or already cleaned up, skipping cleanup.');
        return;
    }
    await new Promise<void>((resolve, reject) => {
        console.log('afterAll: Closing Test WebSocket Server...');
        let clientCount = 0;
        for (const client of Array.from(wss!.clients)) {
            if (client.readyState === WebSocket.OPEN) {
                clientCount++;
                client.terminate();
            }
        }
        if (clientCount > 0) {
            console.log(`afterAll: Terminated ${clientCount} connected client(s).`);
        }
        wss!.close((err) => {
            if (err) {
                console.error("afterAll: Error closing test server:", err);
                reject(err);
            } else {
                console.log("afterAll: Test WebSocket Server closed successfully.");
                resolve();
            }
            wss = null;
        });
    });
    console.log("afterAll: Cleanup complete.");
});
test('should connect to the WebSocket server and receive a message', async ({}, testInfo) => {
    console.log("Test: Running test case...");
    expect(wss).not.toBeNull();
    expect(serverUrl).toBeDefined();
    const receivedMessage = await new Promise<string>((resolve, reject) => {
        console.log(`Test Client: Connecting to ${serverUrl}...`);
        const wsClient = new WebSocket(serverUrl);
        let received = false;
        let connected = false;
        const timeoutDuration = testInfo.timeout - 2000;
        console.log(`Test Client: Setting timeout for ${timeoutDuration}ms`);
        const timeoutId = setTimeout(() => {
            cleanupListeners();
            if (wsClient.readyState === WebSocket.OPEN || wsClient.readyState === WebSocket.CONNECTING) {
                console.error(`Test Client: Timing out after ${timeoutDuration}ms. State: ${wsClient.readyState}`);
                wsClient.terminate();
            }
            reject(new Error(`WebSocket test timed out after ${timeoutDuration}ms`));
        }, timeoutDuration);
        const cleanupListeners = () => {
            clearTimeout(timeoutId);
            wsClient.off('open', onOpen);
            wsClient.off('message', onMessage);
            wsClient.off('error', onError);
            wsClient.off('close', onClose);
            console.log("Test Client: Listeners cleaned up.");
        };
        const onOpen = () => {
            connected = true;
            console.log('Test Client: Connected successfully.');
        };
        const onMessage = (message: Buffer) => {
            const messageStr = message.toString();
            console.log(`Test Client: Received message: "${messageStr}"`);
            received = true;
            cleanupListeners();
            wsClient.close(1000, 'Test complete');
            console.log('Test Client: Closing connection after receiving message.');
            resolve(messageStr);
        };
        const onError = (error: Error) => {
            console.error('Test Client: WebSocket error:', error);
            cleanupListeners();
            if (wsClient.readyState !== WebSocket.CLOSED && wsClient.readyState !== WebSocket.CLOSING) {
                wsClient.terminate();
            }
            reject(error);
        };
        const onClose = (code: number, reason: Buffer) => {
            console.log(`Test Client: Connection closed (Code: ${code}, Reason: "${reason.toString()}", Received Msg: ${received}, Connected: ${connected})`);
            cleanupListeners();
            if (!received && code !== 1000) {
                reject(new Error(`WebSocket closed unexpectedly before receiving message. Code: ${code}, Reason: ${reason.toString()}`));
            }
        };
        wsClient.on('open', onOpen);
        wsClient.on('message', onMessage);
        wsClient.on('error', onError);
        wsClient.on('close', onClose);
    });
    console.log("Test: Message promise resolved.");
    expect(receivedMessage).toBe('Hello from server!');
    console.log("Test: Assertion passed.");
});
```


```
This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

================================================================
Directory Structure
================================================================
src/
  types/
    generated.ts
  vite-env.d.ts
test/
  connector-browser.spec.ts
  connector.spec.ts
vite.config.ts
vitest.config.ts

================================================================
Files
================================================================

================
File: src/types/generated.ts
================
export enum ActivityRule {
	ABSOLUTE = 'ABSOLUTE',
	RATIO = 'RATIO'
}
export enum AuMessageType {
	AUCTIONEER_BROADCAST = 'AUCTIONEER_BROADCAST',
	AUCTIONEER_TO_TRADER = 'AUCTIONEER_TO_TRADER',
	TRADER_TO_AUCTIONEER = 'TRADER_TO_AUCTIONEER',
	SYSTEM_BROADCAST = 'SYSTEM_BROADCAST',
	SYSTEM_TO_TRADER = 'SYSTEM_TO_TRADER',
	SYSTEM_TO_AUCTIONEER = 'SYSTEM_TO_AUCTIONEER'
}
export enum ClientSocketState {
	OPENED = 'OPENED',
	CLOSED = 'CLOSED'
}
export enum SessionTerminationReason {
	BROWSER_UNLOADED = 'BROWSER_UNLOADED',
	COMPANY_DELETED = 'COMPANY_DELETED',
	COMPANY_NAME_EDITED = 'COMPANY_NAME_EDITED',
	FORCED_OFF = 'FORCED_OFF',
	LOGIN_FROM_ANOTHER_BROWSER = 'LOGIN_FROM_ANOTHER_BROWSER',
	SERVER_REBOOT = 'SERVER_REBOOT',
	SERVER_SWEPT_STALE_SESSION = 'SERVER_SWEPT_STALE_SESSION',
	SIGNED_OFF = 'SIGNED_OFF',
	USER_EDITED = 'USER_EDITED',
	USER_DELETED = 'USER_DELETED'
}
export enum AuUserRole {
	AUCTIONEER = 'AUCTIONEER',
	TRADER = 'TRADER'
}
export enum AuctionInstruction {
	HIDE = 'HIDE',
	UNHIDE = 'UNHIDE',
	DELETE = 'DELETE'
}
export enum AutopilotMode {
	DISENGAGED = 'DISENGAGED',
	ENGAGED = 'ENGAGED'
}
export enum Crud {
	CREATE = 'CREATE',
	READ = 'READ',
	UPDATE = 'UPDATE',
	DELETE = 'DELETE',
	ADD = 'ADD',
	REMOVE = 'REMOVE',
	CLEAR = 'CLEAR'
}
export enum Operator {
	label = 'label',
	GT = 'GT',
	GE = 'GE'
}
export enum OrderSubmissionType {
	MANUAL = 'MANUAL',
	DEFAULT = 'DEFAULT',
	MANDATORY = 'MANDATORY'
}
export enum OrderType {
	BUY = 'BUY',
	SELL = 'SELL',
	NONE = 'NONE'
}
export enum PriceDirection {
	UP = 'UP',
	DOWN = 'DOWN'
}
export enum StopMode {
	LT = 'LT',
	LE = 'LE',
	NONE = 'NONE'
}
export enum Visibility {
	ALL = 'ALL',
	FIRST_ROUND = 'FIRST_ROUND',
	ELIGIBILITY = 'ELIGIBILITY'
}
export enum ResultType {
	ALERT = 'ALERT',
	OBJECT = 'OBJECT',
	ARRAY_ITEM = 'ARRAY_ITEM'
}
export enum DeAuctioneerInfoLevel {
	NORMAL = 'NORMAL',
	WARNING = 'WARNING',
	ERROR = 'ERROR'
}
export enum DeAuctioneerState {
	STARTING_PRICE_NOT_SET = 'STARTING_PRICE_NOT_SET',
	STARTING_PRICE_SET = 'STARTING_PRICE_SET',
	STARTING_PRICE_ANNOUNCED = 'STARTING_PRICE_ANNOUNCED',
	ROUND_OPEN_ALL_ORDERS_NOT_IN = 'ROUND_OPEN_ALL_ORDERS_NOT_IN',
	ROUND_OPEN_ALL_ORDERS_IN = 'ROUND_OPEN_ALL_ORDERS_IN',
	ROUND_CLOSED_NOT_AWARDABLE = 'ROUND_CLOSED_NOT_AWARDABLE',
	ROUND_CLOSED_AWARDABLE = 'ROUND_CLOSED_AWARDABLE',
	AUCTION_CLOSED = 'AUCTION_CLOSED'
}
export enum DeCommonState {
	SETUP = 'SETUP',
	STARTING_PRICE_ANNOUNCED = 'STARTING_PRICE_ANNOUNCED',
	ROUND_OPEN = 'ROUND_OPEN',
	ROUND_CLOSED = 'ROUND_CLOSED',
	AUCTION_CLOSED = 'AUCTION_CLOSED'
}
export enum DeCreditSetMode {
	MANUAL = 'MANUAL',
	MINIMUM = 'MINIMUM'
}
export enum DeFlowControlType {
	HEARTBEAT = 'HEARTBEAT',
	SET_STARTING_PRICE = 'SET_STARTING_PRICE',
	ANNOUNCE_STARTING_PRICE = 'ANNOUNCE_STARTING_PRICE',
	START_AUCTION = 'START_AUCTION',
	CLOSE_ROUND = 'CLOSE_ROUND',
	REOPEN_ROUND = 'REOPEN_ROUND',
	NEXT_ROUND = 'NEXT_ROUND',
	AWARD_AUCTION = 'AWARD_AUCTION'
}
export enum DeRoundOpenState {
	GREEN = 'GREEN',
	ORANGE = 'ORANGE',
	RED = 'RED'
}
export enum DeRoundState {
	NOT_OPEN = 'NOT_OPEN',
	GREEN = 'GREEN',
	ORANGE = 'ORANGE',
	RED = 'RED'
}
export enum DeTimeState {
	BEFORE_ANNOUNCE_TIME = 'BEFORE_ANNOUNCE_TIME',
	BEFORE_START_TIME = 'BEFORE_START_TIME',
	AUCTION_HAS_STARTED = 'AUCTION_HAS_STARTED'
}
export enum SampleOrderMove {
	INCREASE = 'INCREASE',
	DECREASE = 'DECREASE'
}
export enum PageName {
	CREDITOR_AUCTIONEER_PAGE = 'CREDITOR_AUCTIONEER_PAGE',
	CREDITOR_TRADER_PAGE = 'CREDITOR_TRADER_PAGE',
	HOME_PAGE = 'HOME_PAGE',
	LOGIN_PAGE = 'LOGIN_PAGE',
	SESSION_PAGE = 'SESSION_PAGE',
	USER_PAGE = 'USER_PAGE',
	BH_AUCTIONEER_PAGE = 'BH_AUCTIONEER_PAGE',
	BH_SETUP_PAGE = 'BH_SETUP_PAGE',
	BH_TRADER_PAGE = 'BH_TRADER_PAGE',
	DE_AUCTIONEER_PAGE = 'DE_AUCTIONEER_PAGE',
	DE_SETUP_PAGE = 'DE_SETUP_PAGE',
	DE_TRADER_PAGE = 'DE_TRADER_PAGE',
	MR_AUCTIONEER_PAGE = 'MR_AUCTIONEER_PAGE',
	MR_SETUP_PAGE = 'MR_SETUP_PAGE',
	MR_TRADER_PAGE = 'MR_TRADER_PAGE',
	TE_AUCTIONEER_PAGE = 'TE_AUCTIONEER_PAGE',
	TE_SETUP_PAGE = 'TE_SETUP_PAGE',
	TE_TRADER_PAGE = 'TE_TRADER_PAGE',
	TO_AUCTIONEER_PAGE = 'TO_AUCTIONEER_PAGE',
	TO_SETUP_PAGE = 'TO_SETUP_PAGE',
	TO_TRADER_PAGE = 'TO_TRADER_PAGE'
}
export enum BrowserMessageIcon {
	SUCCESS = 'SUCCESS',
	INFO = 'INFO',
	WARNING = 'WARNING',
	AUCTIONEER_MESSAGE = 'AUCTIONEER_MESSAGE',
	TRADER_MESSAGE = 'TRADER_MESSAGE',
	SYSTEM_MESSAGE = 'SYSTEM_MESSAGE',
	ORDER_CONFIRMATION = 'ORDER_CONFIRMATION'
}
export enum BrowserMessageKind {
	ALERT = 'ALERT',
	NOTIFICATION = 'NOTIFICATION'
}
export enum CommandType {
	CommandSucceeded = 'CommandSucceeded',
	ShowMessage = 'ShowMessage',
	TerminateSession = 'TerminateSession',
	NetworkDown = 'NetworkDown',
	NetworkUp = 'NetworkUp',
	SetLiveStore = 'SetLiveStore',
	AddElements = 'AddElements'
}
export interface EngineCommandEnvelope {
    session_id: string
    readonly simplename: string
    readonly classname: string
    readonly command: EngineCommand
}
function create_command<T extends EngineCommand>(
    simplename: string,
    classname: string,
    command: T
): EngineCommandEnvelope {
    return {
        session_id: "",
        simplename,
        classname,
        command
    } as EngineCommandEnvelope
}
export const auction_row_command = (req: AuctionRowCommand) => create_command('AuctionRowCommand', 'au21.engine.domain.common.commands.AuctionRowCommand', req)
export const auction_select_command = (req: AuctionSelectCommand) => create_command('AuctionSelectCommand', 'au21.engine.domain.common.commands.AuctionSelectCommand', req)
export const client_socket_command = (req: ClientSocketCommand) => create_command('ClientSocketCommand', 'au21.engine.domain.common.commands.ClientSocketCommand', req)
export const company_delete_command = (req: CompanyDeleteCommand) => create_command('CompanyDeleteCommand', 'au21.engine.domain.common.commands.CompanyDeleteCommand', req)
export const company_save_command = (req: CompanySaveCommand) => create_command('CompanySaveCommand', 'au21.engine.domain.common.commands.CompanySaveCommand', req)
export const db_delete_auctions_command = () => create_command('DbDeleteAuctionsCommand', 'au21.engine.domain.common.commands.DbDeleteAuctionsCommand', {})
export const db_init_command = () => create_command('DbInitCommand', 'au21.engine.domain.common.commands.DbInitCommand', {})
export const errors_send_command = (req: ErrorsSendCommand) => create_command('ErrorsSendCommand', 'au21.engine.domain.common.commands.ErrorsSendCommand', req)
export const login_command = (req: LoginCommand) => create_command('LoginCommand', 'au21.engine.domain.common.commands.LoginCommand', req)
export const message_send_command = (req: MessageSendCommand) => create_command('MessageSendCommand', 'au21.engine.domain.common.commands.MessageSendCommand', req)
export const notice_save_command = (req: NoticeSaveCommand) => create_command('NoticeSaveCommand', 'au21.engine.domain.common.commands.NoticeSaveCommand', req)
export const page_set_command = (req: PageSetCommand) => create_command('PageSetCommand', 'au21.engine.domain.common.commands.PageSetCommand', req)
export const session_terminate_command = (req: SessionTerminateCommand) => create_command('SessionTerminateCommand', 'au21.engine.domain.common.commands.SessionTerminateCommand', req)
export const user_delete_command = (req: UserDeleteCommand) => create_command('UserDeleteCommand', 'au21.engine.domain.common.commands.UserDeleteCommand', req)
export const user_save_command = (req: UserSaveCommand) => create_command('UserSaveCommand', 'au21.engine.domain.common.commands.UserSaveCommand', req)
export const de_auction_award_command = (req: DeAuctionAwardCommand) => create_command('DeAuctionAwardCommand', 'au21.engine.domain.de.commands.DeAuctionAwardCommand', req)
export const de_auction_save_command = (req: DeAuctionSaveCommand) => create_command('DeAuctionSaveCommand', 'au21.engine.domain.de.commands.DeAuctionSaveCommand', req)
export const de_auctions_tick_command = () => create_command('DeAuctionsTickCommand', 'au21.engine.domain.de.commands.DeAuctionsTickCommand', {})
export const de_create_sample_db_command = (req: DeCreateSampleDbCommand) => create_command('DeCreateSampleDbCommand', 'au21.engine.domain.de.commands.DeCreateSampleDbCommand', req)
export const de_credit_set_command = (req: DeCreditSetCommand) => create_command('DeCreditSetCommand', 'au21.engine.domain.de.commands.DeCreditSetCommand', req)
export const de_flow_control_command = (req: DeFlowControlCommand) => create_command('DeFlowControlCommand', 'au21.engine.domain.de.commands.DeFlowControlCommand', req)
export const de_order_submit_command = (req: DeOrderSubmitCommand) => create_command('DeOrderSubmitCommand', 'au21.engine.domain.de.commands.DeOrderSubmitCommand', req)
export const de_round_history_command = (req: DeRoundHistoryCommand) => create_command('DeRoundHistoryCommand', 'au21.engine.domain.de.commands.DeRoundHistoryCommand', req)
export const de_template_delete_command = (req: DeTemplateDeleteCommand) => create_command('DeTemplateDeleteCommand', 'au21.engine.domain.de.commands.DeTemplateDeleteCommand', req)
export const de_template_save_command = () => create_command('DeTemplateSaveCommand', 'au21.engine.domain.de.commands.DeTemplateSaveCommand', {})
export const de_trader_limits_command = (req: DeTraderLimitsCommand) => create_command('DeTraderLimitsCommand', 'au21.engine.domain.de.commands.DeTraderLimitsCommand', req)
export const de_traders_add_command = (req: DeTradersAddCommand) => create_command('DeTradersAddCommand', 'au21.engine.domain.de.commands.DeTradersAddCommand', req)
export const de_traders_remove_command = (req: DeTradersRemoveCommand) => create_command('DeTradersRemoveCommand', 'au21.engine.domain.de.commands.DeTradersRemoveCommand', req)
export const heartbeat_command = () => create_command('HeartbeatCommand', 'au21.engine.framework.commands.HeartbeatCommand', {})
export interface AuctionRowCommand extends EngineCommand {
    auction_id: string;
    instruction: AuctionInstruction;
}
export interface AuctionSelectCommand extends EngineCommand {
    auction_id: string;
}
export interface ClientSocketCommand extends EngineCommand {
    browser_name: string | null;
    browser_os: string | null;
    browser_version: string | null;
    sid: string;
    state: ClientSocketState;
}
export interface CompanyDeleteCommand extends EngineCommand {
    company_id: string;
}
export interface CompanySaveCommand extends EngineCommand {
    company_id: string;
    company_longname: string;
    company_shortname: string;
}
export interface DbDeleteAuctionsCommand extends EngineCommand {
}
export interface DbInitCommand extends EngineCommand {
}
export interface DeAuctionAwardCommand extends EngineCommand {
    auction_id: string;
    round_number: string;
}
export interface DeAuctionSaveCommand extends EngineCommand {
    auction_id: string;
    auction_name: string;
    cost_multiplier: string;
    excess_level_0_label: string;
    excess_level_1_label: string;
    excess_level_1_quantity: string;
    excess_level_2_label: string;
    excess_level_2_quantity: string;
    excess_level_3_label: string;
    excess_level_3_quantity: string;
    excess_level_4_label: string;
    excess_level_4_quantity: string;
    month_is_1_based: boolean;
    price_change_initial: string;
    price_change_post_reversal: string;
    price_decimal_places: string;
    price_label: string;
    quantity_label: string;
    quantity_minimum: string;
    quantity_step: string;
    round_closed_min_secs: string;
    round_open_min_seconds: string;
    round_orange_secs: string;
    round_red_secs: string;
    starting_day: string;
    starting_hour: string;
    starting_mins: string;
    starting_month: string;
    starting_price_announcement_mins: string;
    starting_year: string;
    use_counterparty_credits: string;
}
export interface DeAuctionsTickCommand extends EngineCommand {
}
export interface DeCreateSampleDbCommand extends EngineCommand {
    auction_count: number;
    auctioneer_count: number;
    close_last_round: boolean;
    round_count: number;
    trader_count: number;
    use_counterparty_credits: boolean;
}
export interface DeCreditSetCommand extends EngineCommand {
    auction_id: string;
    borrower_id: string;
    credit_limit: string;
    lender_id: string;
}
export interface DeFlowControlCommand extends EngineCommand {
    auction_id: string;
    control: DeFlowControlType;
    starting_price: string | null;
}
export interface DeOrderSubmitCommand extends EngineCommand {
    auction_id: string;
    company_id: string;
    order_type: OrderType;
    quantity: string;
    round: string;
}
export interface DeRoundHistoryCommand extends EngineCommand {
    auction_id: string;
    round_number: string;
}
export interface DeTemplateDeleteCommand extends EngineCommand {
    template_id: string;
}
export interface DeTemplateSaveCommand extends EngineCommand {
}
export interface DeTraderLimitsCommand extends EngineCommand {
    auction_id: string;
    buying_cost_limit: string;
    company_id: string;
    selling_quantity_limit: string;
}
export interface DeTradersAddCommand extends EngineCommand {
    auction_id: string;
    company_ids: string[];
}
export interface DeTradersRemoveCommand extends EngineCommand {
    auction_id: string;
    company_ids: string[];
}
export interface EngineCommand {
}
export interface ErrorsSendCommand extends EngineCommand {
    auction_id: string;
    error: string;
    trader_session_id: string;
}
export interface HeartbeatCommand extends EngineCommand {
}
export interface LoginCommand extends EngineCommand {
    password: string;
    username: string;
}
export interface MessageSendCommand extends EngineCommand {
    auction_id: string;
    message: string;
}
export interface NoticeSaveCommand extends EngineCommand {
    auction_id: string;
    notice: string;
}
export interface PageSetCommand extends EngineCommand {
    page: PageName;
}
export interface SessionTerminateCommand extends EngineCommand {
    reason: SessionTerminationReason;
}
export interface UserDeleteCommand extends EngineCommand {
    user_id: string;
}
export interface UserSaveCommand extends EngineCommand {
    company_id: string;
    email: string;
    password: string;
    phone: string;
    role: AuUserRole;
    user_id: string;
    username: string;
}
export interface AddElements extends StoreCommand {
    command: CommandType.AddElements;
    elements: StoreElement[] | null;
    path: string;
}
export interface AuctionRowElement extends StoreElement {
    auction_design: string;
    auction_id: string;
    auction_name: string;
    common_state_text: string;
    id: string;
    isClosed: boolean;
    isHidden: boolean;
    starting_time_text: string;
}
export interface ClientCommand {
    command: CommandType;
}
export interface CommandSucceeded extends ClientCommand {
    command: CommandType.CommandSucceeded;
}
export interface CompanyElement extends StoreElement {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    id: string;
}
export interface CounterpartyCreditElement extends StoreElement {
    buyer_id: string;
    buyer_longname: string;
    buyer_shortname: string;
    id: string;
    limit_str: string;
    seller_id: string;
    seller_longname: string;
    seller_shortname: string;
}
export interface Date {
}
export interface DateTimeValue extends StoreValue {
    day_of_month: number;
    day_of_week: number;
    hour: number;
    minutes: number;
    month: number;
    seconds: number;
    year: number;
}
export interface DeAuctionValue extends StoreValue {
    auction_counterparty_credits: CounterpartyCreditElement[];
    auction_id: string | null;
    auctioneer_info: DeAuctioneerInfoValue | null;
    auctioneer_status: DeAuctioneerStatusValue | null;
    award_value: DeAwardValue | null;
    blotter: DeBlotter | null;
    common_status: DeCommonStatusValue | null;
    matrix_last_round: DeMatrixRoundElement | null;
    messages: MessageElement[];
    notice: string;
    settings: DeSettingsValue | null;
    trader_history_rows: DeTraderHistoryRowElement[];
    trader_info: DeTraderInfoValue | null;
    users_that_have_seen_auction: string[];
}
export interface DeAuctioneerInfoValue extends StoreValue {
    allow_credit_editing: boolean;
    last_buyers: string;
    last_excess: string;
    last_match: string;
    last_round: number;
    last_sell_dec: string;
    last_sellers: string;
    last_total_buy: string;
    last_total_sell: string;
    pen_buyers: string;
    pen_excess: string;
    pen_match: string;
    pen_round: string;
    pen_sell_dec: string;
    pen_sellers: string;
    pen_total_buy: string;
    pen_total_sell: string;
    potential: string;
}
export interface DeAuctioneerStatusValue extends StoreValue {
    announced: boolean;
    auctioneer_state: DeAuctioneerState;
    auctioneer_state_text: string;
    autopilot: AutopilotMode;
    awardable: boolean;
    controls: { [key in DeFlowControlType]: boolean };
    excess_level: string;
    excess_side: OrderType;
    price_has_overshot: boolean;
    round_open_min_secs: number | null;
    starting_price: string;
    time_state: DeTimeState | null;
}
export interface DeAwardValue extends StoreValue {
    round_results: DeRoundResultVM[];
}
export interface DeBidConstraints {
    max_buy_quantity: number;
    max_sell_quantity: number;
    min_buy_quantity: number;
    min_sell_quantity: number;
}
export interface DeBlotter {
    round_traders: DeRoundTraderElement[];
    rounds: DeRoundElement[];
    traders: DeTraderElement[];
}
export interface DeCommonStatusValue extends StoreValue {
    common_state: DeCommonState;
    common_state_text: string;
    isClosed: boolean;
    price_direction: PriceDirection | null;
    price_has_reversed: boolean;
    round_number: number;
    round_price: string;
    round_seconds: number;
    starting_price_announced: boolean;
    starting_time_text: string;
}
export interface DeInitialLimits {
    initial_buying_cost_limit: number;
    initial_buying_cost_limit_str: string;
    initial_selling_quantity_limit: number;
    initial_selling_quantity_limit_str: string;
}
export interface DeMatrixEdgeElement extends StoreElement {
    buy_quantity_limit: number;
    buyer_cid: string;
    buyer_shortname: string;
    capacity: number;
    credit_quantity_limit: number;
    credit_str: string;
    id: string;
    match: number;
    r: number;
    seller_cid: string;
    seller_shortname: string;
    selling_quantity_limit: number;
    value: number;
    value_str: string;
}
export interface DeMatrixNodeElement extends StoreElement {
    buy_match: number;
    buy_max: number;
    buy_min: number;
    buy_quantity: number;
    cid: string;
    cost: number;
    cost_str: string;
    id: string;
    round: number;
    sell_match: number;
    sell_max: number;
    sell_min: number;
    sell_quantity: number;
    shortname: string;
    side: OrderType;
}
export interface DeMatrixRoundElement extends StoreElement {
    edges: DeMatrixEdgeElement[];
    id: string;
    nodes: DeMatrixNodeElement[];
    round_number: number;
}
export interface DeRoundElement extends StoreElement {
    all_orders_in_next_round_will_be_mandatory: boolean;
    buy_quantity: number;
    buyer_count: number;
    excess_indicator: string;
    excess_quantity: number;
    excess_side: OrderType;
    has_reversed: boolean;
    id: string;
    match_quantity_changed: number;
    matched: number;
    potential: number;
    potential_changed: number;
    raw_matched: number;
    round_direction: PriceDirection | null;
    round_duration: string;
    round_number: number;
    round_price: number | null;
    round_price_str: string;
    sell_quantity: number;
    sell_quantity_change: number;
    seller_count: number;
}
export interface DeRoundResultVM {
    buy_total: string;
    match_total: string;
    matches: DeScenarioMatchVM[];
    round_number: number;
    round_price: string;
    sell_total: string;
    trader_flows: DeTraderFlowVM[];
}
export interface DeRoundTraderElement extends StoreElement {
    bid_while_closed: boolean;
    buyer_credit_limit: number;
    buyer_credit_limit_str: string;
    changed: boolean;
    cid: string;
    company_shortname: string;
    constraints: DeBidConstraints;
    id: string;
    match: number;
    order_submission_type: OrderSubmissionType;
    order_submitted_by: string;
    order_type: OrderType;
    quantity_int: number;
    quantity_str: string;
    round: number;
    timestamp_formatted: string;
}
export interface DeScenarioMatchVM {
    actual_match: number;
    actual_match_str: string;
    buyer_id: string;
    buyer_shortname: string;
    round_number: number;
    seller_id: string;
    seller_shortname: string;
}
export interface DeSettingsValue extends StoreValue {
    auction_name: string;
    cost_multiplier: string;
    excess_level_0_label: string;
    excess_level_1_label: string;
    excess_level_1_quantity: string;
    excess_level_2_label: string;
    excess_level_2_quantity: string;
    excess_level_3_label: string;
    excess_level_3_quantity: string;
    excess_level_4_label: string;
    excess_level_4_quantity: string;
    price_change_initial: string;
    price_change_post_reversal: string;
    price_decimal_places: number;
    price_label: string;
    quantity_label: string;
    quantity_minimum: string;
    quantity_step: string;
    round_closed_min_secs: number;
    round_open_min_secs: number;
    round_orange_secs: number;
    round_red_secs: number;
    starting_price_announcement_mins: number;
    starting_time: DateTimeValue | null;
    use_counterparty_credits: boolean;
}
export interface DeTraderElement extends StoreElement {
    company_id: string;
    has_seen_auction: boolean;
    id: string;
    rank: number | null;
    shortname: string;
}
export interface DeTraderFlowVM {
    company_id: string;
    company_shortname: string;
    order_type: OrderType;
    quantity: string;
}
export interface DeTraderHistoryRowElement extends StoreElement {
    auction_id: string;
    bid_constraints: DeBidConstraints | null;
    company_id: string;
    excess_level: string;
    excess_side: OrderType | null;
    id: string;
    order_submission_type: OrderSubmissionType;
    order_submitted_by: string;
    order_type: OrderType | null;
    price_direction: PriceDirection | null;
    price_has_reversed: boolean;
    price_suffix: string;
    quantity: string;
    round_number: string;
    round_price: string;
    value: string;
}
export interface DeTraderInfoValue extends StoreValue {
    auction_id: string;
    award_direction: string;
    award_line: string | null;
    awarded_price: string;
    awarded_quantity: string;
    awarded_round_number: string;
    awarded_value: string;
    bid_constraints: DeBidConstraints;
    company_id: string;
    initial_limits: DeInitialLimits;
    order_quantity: number;
    order_submission_type: OrderSubmissionType;
    order_type: OrderType;
    price_label: string;
    quantity_label: string;
    round_number: number;
    round_price: string;
    value: string;
}
export interface MessageElement extends StoreElement {
    from: string;
    id: string;
    message: string;
    message_type: AuMessageType;
    message_type_label: string;
    timestamp: number;
    timestamp_label: string;
    to: string;
}
export interface NetworkDown extends ClientCommand {
    command: CommandType.NetworkDown;
}
export interface NetworkUp extends ClientCommand {
    command: CommandType.NetworkUp;
}
export interface SessionUserValue extends StoreValue {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    current_auction_id: string;
    current_page: PageName;
    isAuctioneer: boolean;
    isOnline: boolean;
    role: AuUserRole | null;
    session_id: string;
    socket_state: ClientSocketState;
    user_id: string;
    username: string;
}
export interface SetLiveStore extends StoreCommand {
    command: CommandType.SetLiveStore;
    store: LiveClientStore;
}
export interface ShowMessage extends ClientCommand {
    browser_message_kind: BrowserMessageKind;
    command: CommandType.ShowMessage;
    message: string[];
}
export interface StoreCommand extends ClientCommand {
    command: CommandType;
}
export interface StoreElement {
    id: string;
}
export interface StoreValue {
}
export interface TerminateSession extends ClientCommand {
    command: CommandType.TerminateSession;
    message: string | null;
}
export interface TimeValue extends StoreValue {
    city: string;
    date_time: DateTimeValue;
}
export interface UserElement extends StoreElement {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    current_auction_id: string | null;
    email: string;
    has_connection_problem: boolean;
    id: string;
    isAuctioneer: boolean;
    isObserver: boolean;
    isOnline: boolean;
    isTester: boolean;
    password: string;
    phone: string;
    role: AuUserRole;
    socket_state: ClientSocketState | null;
    socket_state_last_closed: Date | null;
    termination_reason: SessionTerminationReason | null;
    user_id: string;
    username: string;
}
export class AuStore{
	time: TimeValue | null = null
	seconds_since_last_message_received: number = 0
}
export class LiveClientStore extends AuStore {
	auction_rows: AuctionRowElement[] = []
	companies: CompanyElement[] = []
	counterparty_credits: CounterpartyCreditElement[] = []
	de_auction: DeAuctionValue | null = null
	session_user: SessionUserValue | null = null
	time: TimeValue | null = null
	users: UserElement[] = []
}
export class StaleClientStore{
	stale_de_matrix_rounds: DeMatrixRoundElement[] = []
}


================
File: src/vite-env.d.ts
================
interface ImportMetaEnv {
  readonly VITE_WEBSOCKET_URL: string
}
interface ImportMeta {
  readonly env: ImportMetaEnv
}
declare module '*?worker' {
  const workerConstructor: {
    new (): Worker;
  };
  export default workerConstructor;
}
interface Window {
  testResult: any;
  testCompleted: boolean;
  testError: string | null;
  resolveTest: (value: any) => void;
  runTest: (serverUrl: string) => Promise<void>;
}

================
File: test/connector-browser.spec.ts
================
import { test, expect, Page } from '@playwright/test';
import { WebSocketServer, WebSocket } from 'ws';
import pako from 'pako';
import { CommandType, EngineCommandEnvelope } from '@/types/generated';
const TEST_PORT = 9001;
let wss: WebSocketServer | null = null;
let serverUrl: string;
test.beforeAll(async () => {
  return new Promise<void>((resolve, reject) => {
    wss = new WebSocketServer({ port: TEST_PORT });
    wss.once('listening', () => {
      const address = wss?.address();
      if (address && typeof address !== 'string') {
        serverUrl = `ws://localhost:${address.port}`;
        console.log(`Test WebSocket server started on ${serverUrl}`);
        resolve();
      } else {
        wss?.close();
        wss = null;
        reject(new Error('Invalid server address'));
      }
    });
    wss.once('error', (error) => {
      console.error('WebSocket server failed to start:', error);
      wss = null;
      reject(error);
    });
    wss.on('connection', (ws: WebSocket) => {
      console.log('Browser client connected to test server');
      ws.on('message', (message: Buffer) => {
        console.log('Server received message:', message.toString());
        try {
          const receivedEnvelope: EngineCommandEnvelope = JSON.parse(message.toString());
          console.log('Test server parsed message:', receivedEnvelope);
          const responseCommand = {
            command: CommandType.CommandSucceeded
          };
          const responseString = JSON.stringify(responseCommand);
          const compressedResponse = pako.deflate(responseString);
          console.log('Test server sending compressed response');
          ws.send(compressedResponse);
        } catch (error) {
          console.error('Server error handling message:', error);
          ws.close(1011, 'Internal Server Error');
        }
      });
      ws.on('error', (error) => {
        console.error('WebSocket connection error on server:', error);
      });
    });
  });
});
test.afterAll(async () => {
  if (wss) {
    return new Promise<void>((resolve, reject) => {
      console.log('Closing test WebSocket server...');
      wss!.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.terminate();
        }
      });
      wss!.close((err) => {
        if (err) {
          console.error('Error closing WebSocket server:', err);
          reject(err);
        } else {
          console.log('WebSocket server closed');
          resolve();
        }
        wss = null;
      });
    });
  }
});
const injectConnectorScript = async (page: Page) => {
  await page.setContent(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Connector Browser Test</title>
      </head>
      <body>
        <div id="status">Initializing...</div>
        <div id="result">No result yet</div>
        <script type="module">
          window.testResult = null;
          window.testCompleted = false;
          window.testError = null;
          window.runTest = async (serverUrl) => {
            try {
              const { Connector } = await import('/src/connector.js');
              const { ClientSocketState } = await import('/src/types/generated.js');
              document.getElementById('status').textContent = 'Connector imported, setting up test...';
              // Create a promise that will resolve when the handler is called
              const resultPromise = new Promise((resolve) => {
                window.resolveTest = resolve;
              });
              // Create command handlers
              const clientCommandHandlers = {
                onCommandSucceeded: (cmd) => {
                  console.log('Browser: onCommandSucceeded called with:', cmd);
                  document.getElementById('status').textContent = 'Received CommandSucceeded response';
                  window.testResult = cmd;
                  window.resolveTest(cmd);
                }
              };
              // Create connector with the handler
              const connector = Connector.create({
                url: serverUrl,
                session_id: "browser-test-session",
                show_connector_log: true,
                clientCommandHandlers
              });
              document.getElementById('status').textContent = 'Connecting...';
              await connector.connect();
              document.getElementById('status').textContent = 'Connected, publishing message...';
              // Publish the test message
              connector.publish({
                session_id: "123",
                simplename: "SessionConnectCommand",
                classname: "SessionConnectCommand",
                command: {
                  browser_name: "chrome",
                  browser_os: "macos",
                  browser_version: "1.0",
                  sid: "session-1",
                  state: "OPENED" // String format needed for browser compatibility
                }
              });
              // Wait for the result or timeout
              const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Timeout waiting for response')), 5000);
              });
              try {
                await Promise.race([resultPromise, timeoutPromise]);
                document.getElementById('status').textContent = 'Test completed successfully';
                // Close the connector now that we're done
                connector.close();
                window.testCompleted = true;
              } catch (error) {
                document.getElementById('status').textContent = 'Test failed: ' + error.message;
                window.testError = error.message;
                // Ensure connector is closed on error
                connector.close();
                throw error;
              }
            } catch (error) {
              console.error('Error in browser test:', error);
              document.getElementById('status').textContent = 'Test setup failed: ' + error.message;
              window.testError = error.message;
            }
          };
        </script>
      </body>
    </html>
  `);
};
test('Connector receives and processes compressed messages in browser', async ({ page }) => {
  await injectConnectorScript(page);
  page.on('console', msg => console.log(`Browser console: ${msg.text()}`));
  expect(wss).not.toBeNull();
  expect(serverUrl).toBeDefined();
  await page.evaluate((url) => {
    return window.runTest(url);
  }, serverUrl);
  await page.waitForFunction(() => window.testCompleted || window.testError, { timeout: 10000 });
  const testError = await page.evaluate(() => window.testError);
  if (testError) {
    throw new Error(`Browser test failed: ${testError}`);
  }
  const testStatus = await page.evaluate(() => document.getElementById('status')?.textContent);
  console.log('Final test status:', testStatus);
  const result = await page.evaluate(() => window.testResult);
  expect(result).toEqual({
    command: 'CommandSucceeded'
  });
  console.log('Browser test completed successfully');
});

================
File: test/connector.spec.ts
================
import { test, expect, type TestInfo } from '@playwright/test';
import { WebSocketServer, WebSocket } from 'ws';
import pako from 'pako';
import { Connector, ConnectorOptions } from '@/connector';
import {ClientCommand, ClientSocketState, CommandType, EngineCommandEnvelope} from '../src/types/generated';
const TEST_PORT = 9000;
let wss: WebSocketServer | null = null;
let serverUrl: string;
test.beforeAll(async () => {
    return new Promise<void>((resolve, reject) => {
        wss = new WebSocketServer({ port: TEST_PORT });
        const onError = (error: Error) => {
            console.error('WebSocket server failed to start:', error);
            wss?.off('listening', onListening);
            wss = null;
            reject(error);
        };
        const onListening = () => {
            const address = wss?.address();
            if (address && typeof address !== 'string') {
                serverUrl = `ws://localhost:${address.port}`;
                console.log(`WebSocket server started on ${serverUrl}`);
                wss?.off('error', onError);
                resolve();
            } else {
                console.error('WebSocket server address is not valid:', address);
                wss?.close();
                wss = null;
                reject(new Error('Invalid server address'));
            }
        };
        wss.once('listening', onListening);
        wss.once('error', onError);
        wss.on('error', (error) => {
            console.error('WebSocket server error:', error);
        });
        wss.on('connection', (ws: WebSocket) => {
            console.log('Client connected to server');
            ws.on('message', (message: Buffer) => {
                console.log('Server received message:', message.toString());
                try {
                    const receivedEnvelope: EngineCommandEnvelope = JSON.parse(message.toString());
                    console.log('Parsed received envelope:', receivedEnvelope);
                    const responseCommand = {
                        command: CommandType.CommandSucceeded
                    };
                    const responseString = JSON.stringify(responseCommand);
                    const compressedResponse = pako.deflate(responseString);
                    console.log('Server sending compressed response:', compressedResponse);
                    ws.send(compressedResponse);
                } catch (error) {
                    console.error('Server failed to process message or send response:', error);
                    ws.close(1011, 'Internal Server Error');
                }
            });
            ws.on('close', (code, reason) => {
                console.log(`Client disconnected from server: Code ${code}, Reason: ${reason.toString()}`);
            });
            ws.on('error', (error) => {
                console.error('WebSocket client error on server side:', error);
            });
        });
    });
});
test.afterAll(async () => {
    if (wss) {
        console.log('Closing WebSocket server...');
        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.terminate();
            }
        });
        return new Promise<void>((resolve, reject) => {
            wss?.close((err) => {
                if (err) {
                    console.error('Error closing WebSocket server:', err);
                    reject(err);
                } else {
                    console.log('WebSocket server closed.');
                    resolve();
                }
                wss = null;
            });
        });
    }
});
test('Connector receives and decodes compressed CommandSucceeded message', async ({ }, testInfo: TestInfo) => {
    expect(wss).not.toBeNull();
    expect(serverUrl).toBeDefined();
    let result: ClientCommand | null = null;
    let resolveResultPromise: (value: ClientCommand) => void;
    const resultPromise = new Promise<ClientCommand>(resolve => {
        resolveResultPromise = resolve;
    });
    const clientCommandHandlers: ConnectorOptions['clientCommandHandlers'] = {
        onCommandSucceeded: (cmd: ClientCommand) => {
            console.log('onCommandSucceeded handler called with:', cmd);
            result = cmd;
            resolveResultPromise(cmd);
        }
    };
    const connector = Connector.create({
        url: serverUrl,
        session_id: "test-session-123",
        show_connector_log: true,
        clientCommandHandlers
    });
    console.log('Connecting connector...');
    await connector.connect();
    console.log('Connector connected.');
    const engineCommand: EngineCommandEnvelope = {
        session_id: "123",
        simplename: "SessionConnectCommand",
        classname: "SessionConnectCommand",
        command: {
            browser_name: "chrome",
            browser_os: "macos",
            browser_version: "1.0",
            sid: "session-1",
            state: ClientSocketState.OPENED
        }
    };
    console.log('Publishing engine command:', engineCommand);
    connector.publish(engineCommand);
    console.log('Engine command published.');
    const timeoutDuration = testInfo.timeout - 2000;
    console.log(`Waiting for result (max ${timeoutDuration}ms)...`);
    try {
        await test.step('Wait for CommandSucceeded result', async () => {
            await Promise.race([
                resultPromise,
                new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout waiting for CommandSucceeded')), timeoutDuration))
            ]);
        });
        console.log('Result received.');
    } catch (error) {
        console.error('Error during wait:', error);
        connector.close();
        throw error;
    }
    const expectedCommand: ClientCommand = {
        command: CommandType.CommandSucceeded
    };
    console.log('Verifying result...');
    expect(result).not.toBeNull();
    expect(result).toEqual(expectedCommand);
    console.log('Result verified successfully.');
    connector.close();
    console.log('Connector closed.');
});


================
File: playwright.config.ts
================
import { defineConfig, devices } from '@playwright/test';
export default defineConfig({
  testDir: './test',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'list',
  use: {
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  webServer: {
    command: 'npx vite --port 5173',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 30000,
  },
});

================
File: vite.config.ts
================
import {defineConfig} from 'vite';
import {fileURLToPath, URL} from "node:url";
export default defineConfig({
    plugins: [],
    worker: {
        format: 'es',
    },
    resolve: {
        alias: {
            "@": fileURLToPath(new URL("./src", import.meta.url)),
        },
    },
});

================
File: vitest.config.ts
================
import { defineConfig } from 'vitest/config';
import {fileURLToPath, URL} from "node:url";
export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
});



================================================================
End of Codebase
================================================================

```
