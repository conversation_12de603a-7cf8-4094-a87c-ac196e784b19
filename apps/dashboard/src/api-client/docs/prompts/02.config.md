You are an expert senior software developer, skilled in typescript, javascript, nodejs, playwright, testing, and any 
other relevant skills. 
Your task is to create a test suite for a typescript package consisting of two files:
- unzip-worker.ts which is a web worker that decompresses data using pako.
- connector.ts which is a websocket client that uses the web worker to decompress data.
IMPORTANT: I want to test the connector in a real browser with playwright, agains a real websocket server.
- I do not want to mock the browser or the websocket server because I want to see how it responds to real network 
  failures. Abstracting the network is one of the great falacies of software development as you know.

Here is the unzip-worker file:
```ts
// src/unzip-worker.ts
import pako from "pako"
self.onmessage = function (event: MessageEvent<ArrayBuffer>) {
	try {
		console.log('received message in worker:' + event.data)
		const arrayBuffer = event.data
		const unzipped = pako.inflate(new Uint8Array(arrayBuffer))
		const decoded = new TextDecoder().decode(unzipped)
		const json = JSON.parse(decoded)
		self.postMessage(json)
	} catch (e) {
		console.error("Error in unzip-worker:", e)
    	self.postMessage(null)
	}
}
````
and here is the connector file:
```ts
// src/connector.ts
import {v4 as uuid} from 'uuid'
import {ClientCommand, CommandType, EngineCommandEnvelope} from '@/types/generated'
import UnzipWorker from './unzip-worker?worker'
function getDefaultWebSocketUrl(): string {
  const location = window.location
  return import.meta.env.VITE_WEBSOCKET_URL
    ? `${import.meta.env.VITE_WEBSOCKET_URL}/`
    : `${location.protocol.replace('http', 'ws')}//${location.host}/socket/`
}
const random_session_id = uuid()
export interface ConnectorOptions {
  url?: string
  session_id?: string
  show_connector_log?: boolean
  clientCommandHandlers?: {
    [K in keyof typeof CommandType as `on${K}`]?: (cmd: ClientCommand & { command: CommandType }) => void
  }
  onSecondsSinceLastMessage?: (seconds: number) => void
  onTerminate?: (reason: { reason: string }) => void
  onError?: (error: Error) => void
}
export interface ConnectorState {
  isConnected: boolean
  messageCount: number
  lastMessageTime: number
  reconnectAttempts: number
  secondsSinceLastMessage: number
}
const MAX_RECONNECT_DELAY = 10000
const QUIET_COMMANDS = ['SetLiveStore', 'AddElements']
export class Connector {
  private static instance: Connector | null = null
  private readonly options: ConnectorOptions
  private readonly outgoingQueue: EngineCommandEnvelope[] = []
  private ws: WebSocket | null = null
  private worker: Worker | null = null
  private messageTrackingInterval: number | null = null
  private reconnectInterval: number | null = null
  private state: ConnectorState
  private constructor(options: ConnectorOptions) {
    this.options = options
    this.state = this.getInitialState()
    this.setupWorker()
  }
  public static create(options: ConnectorOptions): Connector {
    if (!Connector.instance) {
      const url = options.url || getDefaultWebSocketUrl()
      Connector.instance = new Connector({ ...options, url })
      Connector.instance.connect()
    }
    return Connector.instance
  }
  public static reset(options?: ConnectorOptions): Connector | null {
    if (Connector.instance) {
      Connector.instance.cleanup()
    }
    Connector.instance = null
    return options ? Connector.create(options) : null
  }
  public connect(): Promise<void> {
    return new Promise((resolve) => {
      if (this.ws?.readyState === WebSocket.CONNECTING ||
        this.ws?.readyState === WebSocket.OPEN) {
        this.ws.close()
      }
      this.ws = new WebSocket(this.ensureTrailingSlash(this.options.url!))
      this.ws.binaryType = 'arraybuffer'
      this.ws.onopen = () => {
        if (this.options.show_connector_log) {
          console.log('WebSocket connected:', this.options.url)
        }
        this.state.isConnected = true
        this.stopReconnection()
        this.state.reconnectAttempts = 0
        this.flushQueue()
        resolve()
      }
      this.ws.onmessage = (event) => {
        this.state.lastMessageTime = Date.now()
        this.state.secondsSinceLastMessage = 0
        if (event.data instanceof ArrayBuffer) {
          this.worker?.postMessage(event.data, [event.data])
        }
        this.state.messageCount++
      }
      this.ws.onclose = () => {
        this.state.isConnected = false
        this.startReconnection()
      }
      this.ws.onerror = (error) => {
        if (this.options.show_connector_log) {
          console.error('WebSocket error:', error)
        }
        if (this.options.onError) {
          this.options.onError(new Error('WebSocket error: ' + error))
        }
        this.ws?.close()
      }
      this.setupBrowserEvents()
      this.startMessageTracking()
    })
  }
  public close(): void {
    this.cleanup()
  }
  public publish(envelope: EngineCommandEnvelope): void {
    envelope.session_id = this.options.session_id || random_session_id
    if (this.state.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(envelope))
      if (this.options.show_connector_log) {
        console.log('>>> sending:', envelope)
      }
    } else {
      this.outgoingQueue.push(envelope)
      if (this.options.show_connector_log) {
        console.log('queuing:', envelope)
      }
    }
  }
  public getState(): ConnectorState {
    return { ...this.state }
  }
  public isConnected(): boolean {
    return this.state.isConnected
  }
  public getMessageCount(): number {
    return this.state.messageCount
  }
  public getQueue(): EngineCommandEnvelope[] {
    return [...this.outgoingQueue]
  }
  private cleanup(): void {
    this.clearTimers()
    this.worker?.terminate()
    this.worker = null
    this.ws?.close()
    this.ws = null
    this.state = this.getInitialState()
    this.outgoingQueue.length = 0
    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private getInitialState(): ConnectorState {
    return {
      isConnected: false,
      messageCount: 0,
      lastMessageTime: Date.now(),
      reconnectAttempts: 0,
      secondsSinceLastMessage: 0
    }
  }
  private setupWorker(): void {
    this.worker = new UnzipWorker()
    this.worker.onmessage = (event) => {
      const cmd = event.data
      if (cmd) {
        this.handleCommand(cmd)
      }
    }
    this.worker.onerror = (error) => {
      if (this.options.onError) {
        this.options.onError(error instanceof Error ? error : new Error('Worker error'))
      }
    }
  }
  private handleCommand(cmd: ClientCommand): void {
    if (!cmd?.command || !this.options.clientCommandHandlers) return
    const handler = this.options.clientCommandHandlers[`on${cmd.command}`]
    if (handler) {
      handler(cmd)
    } else {
      console.error('No handler for command:', cmd.command)
    }
    if (this.options.show_connector_log && !QUIET_COMMANDS.includes(cmd.command)) {
      console.log('WS <<< ', cmd.command)
    }
  }
  private flushQueue(): void {
    while (this.outgoingQueue.length > 0) {
      const msg = this.outgoingQueue.shift()
      if (msg) this.publish(msg)
    }
  }
  private startMessageTracking(): void {
    this.updateMessageTracking()
    this.messageTrackingInterval = window.setInterval(() => {
      this.updateMessageTracking()
    }, 1000)
  }
  private updateMessageTracking(): void {
    this.state.secondsSinceLastMessage = Math.floor(
      (Date.now() - this.state.lastMessageTime) / 1000
    )
    if (this.options.onSecondsSinceLastMessage) {
      this.options.onSecondsSinceLastMessage(this.state.secondsSinceLastMessage)
    }
  }
  private handleBeforeUnload = () => {
    if (this.options.onTerminate) {
      this.options.onTerminate({ reason: 'BROWSER_UNLOADED' })
    }
  }
  private setupBrowserEvents(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private startReconnection(): void {
    this.clearTimers()
    const delay = this.calculateReconnectDelay()
    this.state.reconnectAttempts++
    if (this.options.show_connector_log) {
      console.log(`WebSocket closed. Reconnecting in ${delay}ms... (attempt ${this.state.reconnectAttempts})`)
    }
    setTimeout(() => this.connect(), 100)
    this.reconnectInterval = window.setInterval(() => {
      if (!this.state.isConnected) {
        if (this.options.show_connector_log) {
          console.log(`Attempting reconnection ${this.state.reconnectAttempts}...`)
        }
        this.connect()
      }
    }, delay)
  }
  private stopReconnection(): void {
    if (this.reconnectInterval !== null) {
      window.clearInterval(this.reconnectInterval)
      this.reconnectInterval = null
    }
  }
  private clearTimers(): void {
    if (this.messageTrackingInterval !== null) {
      window.clearInterval(this.messageTrackingInterval)
      this.messageTrackingInterval = null
    }
    this.stopReconnection()
  }
  private calculateReconnectDelay(): number {
    const baseDelay = Math.min(
      100 * Math.pow(2, this.state.reconnectAttempts),
      MAX_RECONNECT_DELAY
    )
    return baseDelay + Math.random() * 1000
  }
  private ensureTrailingSlash(url: string): string {
    if (!url.endsWith('/')) {
      url += '/'
    }
    if (!url.endsWith('socket/')) {
      console.warn('WebSocket URL should end with socket/')
    }
    return url
  }
}
export function createClientConnector(options: ConnectorOptions) {
  const connector = Connector.create(options)
  return {
    publish: (env: EngineCommandEnvelope) => connector.publish(env),
    close: () => connector.close()
  }
}
if (import.meta.hot) {
  import.meta.hot.accept()
}
```

the unzip-worker.ts passes this test, when running the file from intellij, by clicking on the green arrow:
```ts
// test/unzip-worker.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import pako from 'pako'
const mockSelf = {
    postMessage: vi.fn(),
    onmessage: null
}
const originalSelf = globalThis.self
describe('Unzip Worker', () => {
    beforeEach(() => {
        vi.resetAllMocks()
        globalThis.self = mockSelf
        return import('@/unzip-worker')
    })
    afterEach(() => {
        globalThis.self = originalSelf
    })
    it('should correctly unzip and parse valid data', async () => {
        if(mockSelf.onmessage == null) throw new Error("mockSelf.onmessage is null")
        const testObject = { test: 'data', number: 123 }
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode(JSON.stringify(testObject))
        const compressed = pako.deflate(encoded)
        const arrayBuffer = compressed.buffer
        mockSelf.onmessage({ data: arrayBuffer } as MessageEvent<ArrayBuffer>)
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(testObject)
    })
    it('should handle errors and return null', async () => {
        if(mockSelf.onmessage == null) throw new Error("mockSelf.onmessage is null")
        const invalidData = new ArrayBuffer(10)
        mockSelf.onmessage({ data: invalidData } as MessageEvent<ArrayBuffer>)
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null)
    })
})
```

Here is a test which proves that it's possible to create a real websocket server and test it.
This test also passes when running from intellij:
```ts
// test/websocket-test.spec.ts
import { test, expect } from '@playwright/test';
import { WebSocketServer, WebSocket } from 'ws';
const TEST_PORT = 8999;
let wss: WebSocketServer | null = null;
let serverUrl: string;
test.beforeAll(async () => {
    console.log(`beforeAll: Attempting to start server on port ${TEST_PORT}...`);
    await new Promise<void>((resolve, reject) => {
        wss = new WebSocketServer({ port: TEST_PORT });
        const onError = (error: Error) => {
            console.error(`beforeAll: Server error during startup: ${error.message}`);
            wss?.off('listening', onListening);
            wss?.off('error', onError);
            wss = null;
            reject(error);
        };
        const onListening = () => {
            const address = wss?.address();
            if (address && typeof address !== 'string') {
                serverUrl = `ws://localhost:${address.port}`;
                console.log(`beforeAll: Test WebSocket Server listening on ${serverUrl}`);
                wss?.off('error', onError);
                resolve();
            } else {
                const errMsg = 'beforeAll: Failed to get server address after listening.';
                console.error(errMsg);
                wss?.close();
                wss = null;
                reject(new Error(errMsg));
            }
        };
        wss.once('listening', onListening);
        wss.once('error', onError);
        wss.on('connection', (wsClient) => {
            console.log('Test Server: Client connected');
            wsClient.send('Hello from server!');
            wsClient.on('close', () => console.log('Test Server: Client disconnected'));
            wsClient.on('error', (error) => console.error('Test Server: Client error:', error));
        });
        wss.on('error', (error) => {
            console.error(`Test Server: Runtime error: ${error.message}`);
        });
    });
    console.log("beforeAll: Server startup promise resolved.");
});
test.afterAll(async () => {
    console.log("afterAll: Starting cleanup...");
    if (!wss) {
        console.log('afterAll: Server was not running or already cleaned up, skipping cleanup.');
        return;
    }
    await new Promise<void>((resolve, reject) => {
        console.log('afterAll: Closing Test WebSocket Server...');
        let clientCount = 0;
        for (const client of Array.from(wss!.clients)) {
            if (client.readyState === WebSocket.OPEN) {
                clientCount++;
                client.terminate();
            }
        }
        if (clientCount > 0) {
            console.log(`afterAll: Terminated ${clientCount} connected client(s).`);
        }
        wss!.close((err) => {
            if (err) {
                console.error("afterAll: Error closing test server:", err);
                reject(err);
            } else {
                console.log("afterAll: Test WebSocket Server closed successfully.");
                resolve();
            }
            wss = null;
        });
    });
    console.log("afterAll: Cleanup complete.");
});
test('should connect to the WebSocket server and receive a message', async ({}, testInfo) => {
    console.log("Test: Running test case...");
    expect(wss).not.toBeNull();
    expect(serverUrl).toBeDefined();
    const receivedMessage = await new Promise<string>((resolve, reject) => {
        console.log(`Test Client: Connecting to ${serverUrl}...`);
        const wsClient = new WebSocket(serverUrl);
        let received = false;
        let connected = false;
        const timeoutDuration = testInfo.timeout - 2000;
        console.log(`Test Client: Setting timeout for ${timeoutDuration}ms`);
        const timeoutId = setTimeout(() => {
            cleanupListeners();
            if (wsClient.readyState === WebSocket.OPEN || wsClient.readyState === WebSocket.CONNECTING) {
                console.error(`Test Client: Timing out after ${timeoutDuration}ms. State: ${wsClient.readyState}`);
                wsClient.terminate();
            }
            reject(new Error(`WebSocket test timed out after ${timeoutDuration}ms`));
        }, timeoutDuration);
        const cleanupListeners = () => {
            clearTimeout(timeoutId);
            wsClient.off('open', onOpen);
            wsClient.off('message', onMessage);
            wsClient.off('error', onError);
            wsClient.off('close', onClose);
            console.log("Test Client: Listeners cleaned up.");
        };
        const onOpen = () => {
            connected = true;
            console.log('Test Client: Connected successfully.');
        };
        const onMessage = (message: Buffer) => {
            const messageStr = message.toString();
            console.log(`Test Client: Received message: "${messageStr}"`);
            received = true;
            cleanupListeners();
            wsClient.close(1000, 'Test complete');
            console.log('Test Client: Closing connection after receiving message.');
            resolve(messageStr);
        };
        const onError = (error: Error) => {
            console.error('Test Client: WebSocket error:', error);
            cleanupListeners();
            if (wsClient.readyState !== WebSocket.CLOSED && wsClient.readyState !== WebSocket.CLOSING) {
                wsClient.terminate();
            }
            reject(error);
        };
        const onClose = (code: number, reason: Buffer) => {
            console.log(`Test Client: Connection closed (Code: ${code}, Reason: "${reason.toString()}", Received Msg: ${received}, Connected: ${connected})`);
            cleanupListeners();
            if (!received && code !== 1000) {
                reject(new Error(`WebSocket closed unexpectedly before receiving message. Code: ${code}, Reason: ${reason.toString()}`));
            }
        };
        wsClient.on('open', onOpen);
        wsClient.on('message', onMessage);
        wsClient.on('error', onError);
        wsClient.on('close', onClose);
    });
    console.log("Test: Message promise resolved.");
    expect(receivedMessage).toBe('Hello from server!');
    console.log("Test: Assertion passed.");
});
```

now, I wish to first test the connector, as follows:
- create a connector something like this, (or equivalent async test):
```ts
let result:any = null
const clientCommandHandlers =  {
  onCommandSucceeded: (cmd:ClientCommand) => {
    result = cmd
  }
}
Connector.create({clientCommandHandlers})
```
- publish the following EngineCommandEnvelope:
  - note the `@/types/generated` is ommitted for brevity, but this is the correct shape:
```ts
{
  session_id: "123",
  simplename: "SessionConnectCommand",
  classname: "SessionConnectCommand",
  command: {
    browser_name: "chrome",
    browser_os: "macos",
    browser_version: "1.0",
    sid: "session-1",
    state: "OPENED"
  }
}
```
- and have the server return this, compressed with pako
```ts
{
  command: `CommandSucceeded`
}
```
- and wait for the result and verify that result is:
```ts
{
  command: `CommandSucceeded`
}
```
later we will add coverage and more tests of the connector, including webserver failures, etc.

Now, the problem is that when I introduce playwright tests, then none of the above pass.
Problems such as ports, and 'exports not found' occur.
So, what I need from you first is correct config files, eg:
- vite-env.d.ts
- playwright.config.ts // if needed
- vite.config.ts
- vitest.config.ts
- package.json
- tsconfig.json // and any other tsconfig files if needed
- tsconfig.vitest.json
- tsconfig.vite.json
- any html files under ./public
and any pnpm packages needed.

Additional information:
- The project structure is:
```
/src
  /unzip-worker.ts
  /connector.ts
  /types
    /generated.ts
  /vite-env.d.ts
/tests
  /unzip-worker.test.ts
  /websocket-test.spec.ts
  /connector-browser.spec.ts  // to be added by you later
package.json
vite.config.ts
vitest.config.ts
(various tsconfig files, to be added by you)
```
- I prefer all my tests in ./tests
- I want to run my tests as follows:
```json
    "test:unit": "vitest run --coverage",
    "test:e2e": "playwright test",
    "test": "pnpm test:unit && pnpm test:e2e",
```
- This is a standalone package that will be used by multiple different ui frontend frameworks
  vite is the target, no ssr
- I use this in my tsconfig files:
```
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
```
and I have this mapped: @/types/generated
- this project is a 'module' not 'commonjs'
- I want Playwright to open tests, eg: http://localhost:5173/testpage-with-connector.html, I don't have an existing html file, you will need to create one.
- I want to be able to pass in the websocket url via .env eg if we have a real server running locally or remotely on, say port 4040, but we are developing locally with vite on say port 8080 I want to be able to specify that we should connect to the local or remote url with different host and port.
  -  My getDefaultWebSocketUrl() uses import.meta.env.VITE_WEBSOCKET_URL. In e2e tests you might need to inject something like VITE_WEBSOCKET_URL=ws://localhost:8999 when Vite starts.
  - But by default I want the websocket url to be taken from the browser location url (that's how it's used in production, as you can see in the Connector.ts code)
- The Vite worker import (import UnzipWorker from './unzip-worker?worker') is not essential, and can be restructured for tests if needed, though in my experience using various additional libraries makes it harder to configure the tests.
- I want to be able to run unit tests and e2e tests with one command if possible (though that command could call both a test:unit and test:e2e scripts.
- currently I used node 22
- you will might to create a `vite-env.d.ts` to be able to import the worker.
  I do want coverage.
- I do not need tsconfig strict
- I want the html file(s) in ./public
- I want the package.json file as well as the other config files.
- I am open to modifying the webworker but it does work in production already, but I accept that it might need
  modification for testing
- You won't need placeholders for the @/types/generated, because for now I just want the single test, ie: Connector.
  publish as above, and I already gave you correct shapes. Once we have that working you will have earned the right to see the rest of the generated types.
- Just to clarify the tests should be in the directory: `./tests` not `./test` that was an error on my part.
- I'm ok changing this syntax: `import UnzipWorker from './unzip-worker?worker'` but only if really necessary (you
  will need to provide an explanation, that is consistent with our goals)
- The `@/types/generated` already points to `src/types/generated.d.ts`
- I will need a bundled `pnpm build` and also a way to test that bundle.

Then, after we get the above test working (but not before) such that they can be run with pnpm, then we will add the 
connector-browser.spec.ts tests.

State the requirements before we start.
It's important that you ask question if you don't understand the requirements, and it's important that you never proceed to the next step unless we agree on the requirements.
Once we agree on the requirements, it's important that we agree on the design before we proceed to the implementation.
Any questions so far? 
