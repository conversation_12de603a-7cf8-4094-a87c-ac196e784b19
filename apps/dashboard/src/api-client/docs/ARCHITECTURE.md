# API Client Architecture

## Real-time Event-Driven CQRS

This package implements a specialized real-time event-driven architecture inspired by CQRS but optimized for auction systems with the following characteristics:

### Core Principles
- **Command-based communication**: Clients send commands to the server via WebSocket
- **Server-side materialized views**: Server processes commands serially, updates its model, and materializes client-specific views
- **Full state synchronization**: Complete state (LiveClientStore) is sent to each client after every command
- **Publish/Subscribe pattern**: No direct request/response coupling - clients publish commands and subscribe to state updates
- **Immutable state snapshots**: Each update is a complete, immutable snapshot of client state

### Benefits
- **Simplified client logic**: Clients only need to render current state, not track incremental changes
- **No lost message recovery needed**: Each message contains complete state
- **No change data capture required**: Server materializes views directly from its model
- **Developer productivity**: Easier to reason about and implement complex real-time behaviors

### Performance Characteristics
- Optimized for ~50 clients watching 1-2 concurrent auctions
- Server processes commands including DB and serialization in under 100ms
- Efficient in-memory DB with transparent durable persistence
- Immutable views enable parallel serialization and compression

### Trade-offs
- Trades CPU, memory, and bandwidth for developer productivity
- Designed for systems with a small number of clients (50-200)
- Prioritizes correctness and development speed over network efficiency

### Future Optimizations (Planned)
- Partial updates for heartbeats that only change time properties
- Event sourcing with client-specific materialization of changes
- Property spreading and queues for missed messages

## Implementation Details

### Connector
The `Connector` class handles WebSocket communication, including:
- Establishing and maintaining WebSocket connections
- Serializing and sending commands
- Receiving and deserializing state updates
- Reconnection logic and error handling

### LiveClientStore
The `LiveClientStore` represents the complete client state, including:
- Session information
- Auction data
- User and company information
- Current view state

### Command Flow
1. Client creates a command (e.g., `SessionConnectCommand`)
2. Command is serialized and sent via WebSocket
3. Server processes command and updates its model
4. Server materializes a view for each client
5. Server sends the complete state to each client
6. Client receives state and updates its local store
7. UI components react to store changes