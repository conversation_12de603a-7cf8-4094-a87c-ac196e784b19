# API Client Usage Guide

## Installation

### Within Monorepo (PNPM Workspaces)
In the consuming package's `package.json`:

```json
{
  "dependencies": {
    "@repo/api-client": "workspace:*"
  }
}
```

Then run `pnpm install` at the project root.

### External Projects
```shell
pnpm add @your-scope/api-client
```

## Basic Usage

```ts
import {Connector, ConnectorOptions, EngineCommandEnvelope} from '@repo/api-client';

// 1. Create connector with handlers
const connector = Connector.create({
  url: 'ws://your-server.com/socket/',
  show_connector_log: true,
  clientCommandHandlers: {
    onCommandSucceeded: (cmd) => {
      console.log('Success!', cmd);
    },
    onSetLiveStore: (cmd) => {
      // Update your application state with the new store
      updateAppState(cmd.store);
    }
  },
  onError: (error) => {
    console.error("Connector Error:", error);
  }
});

// 2. Send commands
const commandToSend: EngineCommandEnvelope = {
  session_id: 'some-session',
  simplename: "SessionConnectCommand",
  classname: "SessionConnectCommand",
  command: {
    browser_name: "chrome",
    browser_os: "macos",
    browser_version: "1.0",
    sid: "session-1",
    state: "OPENED"
  }
};

connector.publish(commandToSend);

// 3. Handle cleanup
function cleanup() {
  connector.close();
}
```

## Integration with UI Frameworks

### React
```tsx
import {useEffect, useState} from 'react';
import {Connector, LiveClientStore} from '@repo/api-client';

function AuctionApp() {
  const [store, setStore] = useState<LiveClientStore | null>(null);
  
  useEffect(() => {
    const connector = Connector.create({
      clientCommandHandlers: {
        onSetLiveStore: (cmd) => {
          setStore(cmd.store);
        }
      }
    });
    
    // Connect and send initial commands
    connector.connect();
    
    return () => {
      connector.close();
    };
  }, []);
  
  if (!store) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>Auction: {store.de_auction?.auction_name}</h1>
      {/* Render UI based on store */}
    </div>
  );
}
```

### Vue
```vue
<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
import {Connector} from '@repo/api-client';

const store = ref(null);
let connector;

onMounted(() => {
  connector = Connector.create({
    clientCommandHandlers: {
      onSetLiveStore: (cmd) => {
        store.value = cmd.store;
      }
    }
  });
  
  connector.connect();
});

onUnmounted(() => {
  connector?.close();
});
</script>

<template>
  <div v-if="store">
    <h1>Auction: {{ store.de_auction?.auction_name }}</h1>
    <!-- Render UI based on store -->
  </div>
  <div v-else>Loading...</div>
</template>
```