# Testing the API Client

## Unit Testing

### Testing the Connector
```ts
import {Connector} from '@repo/api-client';
import {vi, describe, it, expect, beforeEach, afterEach} from 'vitest';

describe('Connector', () => {
  let connector;
  let mockWebSocket;
  
  beforeEach(() => {
    // Mock WebSocket
    mockWebSocket = {
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    };
    
    global.WebSocket = vi.fn(() => mockWebSocket);
    
    connector = Connector.create({
      url: 'ws://test-server.com/socket/'
    });
  });
  
  afterEach(() => {
    connector.close();
    vi.resetAllMocks();
  });
  
  it('should send commands when connected', () => {
    // Simulate connection
    mockWebSocket.onopen();
    
    // Send command
    connector.publish({
      session_id: 'test-session',
      simplename: 'TestCommand',
      classname: 'TestCommand',
      command: {}
    });
    
    expect(mockWebSocket.send).toHaveBeenCalled();
  });
  
  it('should queue commands when not connected', () => {
    // Send command before connection
    connector.publish({
      session_id: 'test-session',
      simplename: 'TestCommand',
      classname: 'TestCommand',
      command: {}
    });
    
    expect(mockWebSocket.send).not.toHaveBeenCalled();
    expect(connector.getQueue().length).toBe(1);
    
    // Simulate connection
    mockWebSocket.onopen();
    
    // Queue should be flushed
    expect(mockWebSocket.send).toHaveBeenCalled();
    expect(connector.getQueue().length).toBe(0);
  });
});
```

## Integration Testing

For integration testing, you can use a mock WebSocket server:

```ts
import {Connector, LiveClientStore} from '@repo/api-client';
import {Server} from 'mock-socket';
import {vi, describe, it, expect, beforeEach, afterEach} from 'vitest';

describe('Connector Integration', () => {
  let mockServer;
  let connector;
  let receivedStore = null;
  
  beforeEach(() => {
    mockServer = new Server('ws://localhost:8080');
    
    mockServer.on('connection', socket => {
      socket.on('message', data => {
        const command = JSON.parse(data);
        
        // Respond with a store update
        const response = {
          command: 'SetLiveStore',
          store: {
            session_user: {
              username: 'test-user',
              role: 'TRADER'
            },
            // Other store properties
          }
        };
        
        socket.send(JSON.stringify(response));
      });
    });
    
    connector = Connector.create({
      url: 'ws://localhost:8080',
      clientCommandHandlers: {
        onSetLiveStore: (cmd) => {
          receivedStore = cmd.store;
        }
      }
    });
  });
  
  afterEach(() => {
    connector.close();
    mockServer.close();
    receivedStore = null;
  });
  
  it('should receive store updates', async () => {
    await connector.connect();
    
    connector.publish({
      session_id: 'test-session',
      simplename: 'TestCommand',
      classname: 'TestCommand',
      command: {}
    });
    
    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(receivedStore).not.toBeNull();
    expect(receivedStore.session_user.username).toBe('test-user');
  });
});
```

## Test Helpers

The API client includes test helpers for creating test stores:

```ts
import {LiveClientStoreBuilder} from '@repo/api-client';

// Create a test store for a trader
const traderStore = new LiveClientStoreBuilder()
  .withScenario('trader_logged_in')
  .withCompanies(3)
  .withTraders(2)
  .withLoggedInUser('1')
  .build();

// Create a test store for an auctioneer
const auctioneerStore = new LiveClientStoreBuilder()
  .withScenario('auctioneer_setup')
  .withActiveAuction("auction-de-1")
  .withCompanies(3)
  .withTraders(2)
  .withAuctioneers(1)
  .withLoggedInUser('3')
  .build();
```