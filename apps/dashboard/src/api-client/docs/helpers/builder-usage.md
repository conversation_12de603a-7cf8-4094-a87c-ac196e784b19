// Example Usage (Import necessary types and builder/helpers)
import { LiveClientStoreBuilder } from '../src/builder/LiveClientStore.builder'; // Adjust path as needed
import { PriceDirection, DeCommonState } from '@repo/api-client'; // Adjust path as needed

// Basic Trader Login Scenario
const traderStore = new LiveClientStoreBuilder()
    .withScenario('trader_logged_in')
    .withActiveAuction("auction-de-1")
    .withLoggedInUser('1') // Assumes first user generated is trader with ID '1'
    .withCompanies(2, [{ company_id: '1', company_shortname: 'TraderCorp' }]) // Override trader company name
    .build();

console.log("Trader Store User:", traderStore.session_user?.username);
console.log("Trader Store Auction State:", traderStore.de_auction?.common_status?.common_state);


// Auctioneer Setup Scenario
const setupStore = new LiveClientStoreBuilder()
    .withScenario('auctioneer_setup')
    .withActiveAuction("auction-de-2")
    .withCompanies(3)
    .withTraders(2)
    .withAuctioneers(1) // Explicitly add 1 auctioneer
    .withLoggedInUser('3') // Assumes auctioneer gets ID '3' (1, 2 are traders)
    .build();

console.log("Setup Store User:", setupStore.session_user?.username);
console.log("Setup Store Auction State:", setupStore.de_auction?.common_status?.common_state);


// Complex Auction In Progress Scenario
const complexStore = new LiveClientStoreBuilder()
    .withScenario('auction_in_progress')
    .withActiveAuction("auction-complex-1")
    .withCompanies(6)
    .withTraders(5)
    .withAuctioneers(1)
    .withLoggedInUser('6') // Auctioneer ID
    .withDeAuctionConfig({ maxRound: 3 }) // Explicitly set max round for generation
    .withDeAuction({ // Deep override example (simple merge assumed here)
        common_status: {
            round_price: "115.75",
            price_direction: PriceDirection.UP,
            common_state: DeCommonState.SETUP,
            common_state_text: '',
            isClosed: false,
            price_has_reversed: false,
            round_number: 0,
            round_seconds: 0,
            starting_price_announced: false,
            starting_time_text: ''
        },
        notice: "Round 3 is open. Submit bids now!"
    })
    .withCredits('all_pairs') // Generate credits between all companies
    .build();

console.log("Complex Store User:", complexStore.session_user?.username);
console.log("Complex Store Round:", complexStore.de_auction?.common_status?.round_number);
console.log("Complex Store Credits:", complexStore.counterparty_credits.length);

// Building without scenario, defining elements manually
const manualStore = new LiveClientStoreBuilder()
    .withCompanies(1, [{ company_id: '100', company_shortname: 'ManualCo' }])
    .withTraders(0)
    .withAuctioneers(1, [{ user_id: '999', username: 'ManualAdmin', company_id: '100' }])
    .withLoggedInUser('999')
    .withActiveAuction('manual-auction-1')
    .withGenericAuctionRows(0) // No generic rows
    .withAuctionRowOverrides([{ auction_id: 'manual-auction-1', auction_name: 'Manually Defined Auction' }]) // Define the active one
    .withDeAuction({
        settings: {
            price_label: 'Manual Price',
            auction_name: '',
            cost_multiplier: '',
            excess_level_0_label: '',
            excess_level_1_label: '',
            excess_level_1_quantity: '',
            excess_level_2_label: '',
            excess_level_2_quantity: '',
            excess_level_3_label: '',
            excess_level_3_quantity: '',
            excess_level_4_label: '',
            excess_level_4_quantity: '',
            price_change_initial: '',
            price_change_post_reversal: '',
            price_decimal_places: 0,
            quantity_label: '',
            quantity_minimum: '',
            quantity_step: '',
            round_closed_min_secs: 0,
            round_open_min_secs: 0,
            round_orange_secs: 0,
            round_red_secs: 0,
            starting_price_announcement_mins: 0,
            starting_time: undefined,
            use_counterparty_credits: false
        }
    })
    .build();

console.log("Manual Store Users:", manualStore.users.length);
console.log("Manual Store Price Label:", manualStore.de_auction?.settings?.price_label);
console.log("Manual Store Auction Rows:", manualStore.auction_rows.length);
