Okay, this is a common challenge when dealing with complex state objects in testing. Your existing `createDemo__` helpers are a good start, likely useful for Storybook or basic component rendering, but they lack the fine-grained control needed for specific test scenarios.

Here are a few strategies, ranging from simple enhancements to more structured approaches, to make creating `LiveClientStore` test data more configurable:

**1. Parameterize Existing Helpers (Incremental Improvement)**

*   **Concept:** Modify your `createDemo__` functions (or create parallel `createTest__` functions) to accept an optional `overrides` object. This object would use `Partial<T>` to allow specifying only the fields you care about for a given test.
*   **Example (`CompanyElement.helper.ts`):**

```typescript
import type { CompanyElement } from "@repo/api-client";
import { random_string } from "@/demo/random-helpers"; // Assuming you have this

// Keep the original for demos if needed
export function createDemo__CompanyElement(id: number | string = 1): CompanyElement {
    const companyIdStr = id.toString();
    return {
        id: `company-${companyIdStr}`, // Ensure unique IDs
        company_id: companyIdStr,
        company_longname: `Long Company Name ${random_string(5)} ${id}`,
        company_shortname: `Comp${id}`,
    };
}

// New function for testing
export function createTest__CompanyElement(
    id: number | string,
    overrides: Partial<CompanyElement> = {} // Accept overrides
): CompanyElement {
    const companyIdStr = id.toString();
    // Define defaults
    const defaults: CompanyElement = {
        id: `company-${companyIdStr}`,
        company_id: companyIdStr,
        company_longname: `Test Long Company ${id}`,
        company_shortname: `TestC${id}`,
    };

    // Merge defaults with overrides
    return { ...defaults, ...overrides };
}

// Usage in a test:
const specificCompany = createTest__CompanyElement(101, {
    company_shortname: 'SPEC',
    company_longname: 'Specific Test Company Inc.'
});

const defaultCompany = createTest__CompanyElement(102);
```

*   **Pros:**
    *   Leverages existing structure.
    *   Relatively easy to implement incrementally.
    *   Clear what's being overridden.
*   **Cons:**
    *   Still requires calling many individual helpers to build the full `LiveClientStore`.
    *   Doesn't inherently manage relationships between different parts of the store (e.g., ensuring a user's `company_id` matches a company in the `companies` array).

**2. Factory Function with Configuration Object**

*   **Concept:** Create a central factory function (e.g., `createTestLiveClientStore`) that takes a configuration object. This object describes the desired state, potentially using higher-level abstractions (like `numberOfTraders`) or allowing deep partial overrides.
*   **Example:**

```typescript
import { LiveClientStore, CompanyElement, UserElement, AuUserRole, DeAuctionValue, SessionUserValue /* ... other imports */ } from "@repo/api-client";
import { createTest__CompanyElement } from "@/demo/CompanyElement.helper"; // Use improved helpers
import { createTest__UserElement } from "@/demo/UserElement.helper"; // Use improved helpers
import { createTest__SessionUserValue } from "@/demo/SessionUserValue.helper";
import { createTest__DeAuctionValue } from "@/demo/DeAuctionValue.helper";
// ... import other test helpers

// Define a configuration interface (can be as simple or complex as needed)
export interface LiveClientStoreConfig {
    // High-level controls
    scenario?: 'empty' | 'trader_logged_in' | 'auctioneer_setup' | 'auction_in_progress';
    numCompanies?: number;
    numTraders?: number; // Assumes traders are users with role TRADER
    numAuctioneers?: number; // Assumes auctioneers are users with role AUCTIONEER
    loggedInUserId?: string; // ID of the user for session_user

    // Direct overrides (using Partial for flexibility)
    companyOverrides?: Partial<CompanyElement>[]; // Override specific companies by index or ID
    userOverrides?: Partial<UserElement>[];
    sessionUserOverrides?: Partial<SessionUserValue>;
    deAuctionOverrides?: Partial<DeAuctionValue>; // Allow deep overrides if needed
    // ... other specific overrides
}

// The Factory Function
export function createTestLiveClientStore(config: LiveClientStoreConfig = {}): LiveClientStore {
    const store = new LiveClientStore();

    // --- Apply Defaults based on Scenario or Config ---
    const numCompanies = config.numCompanies ?? (config.numTraders ?? 0) + (config.numAuctioneers ?? 0) + 1; // Ensure enough companies
    const numTraders = config.numTraders ?? (config.scenario === 'trader_logged_in' || config.scenario === 'auction_in_progress' ? 1 : 0);
    const numAuctioneers = config.numAuctioneers ?? (config.scenario === 'auctioneer_setup' || config.scenario === 'auction_in_progress' ? 1 : 0);

    // --- Generate Companies ---
    store.companies = Array.from({ length: numCompanies }, (_, i) => {
        const override = config.companyOverrides?.find(o => o.company_id === (i + 1).toString()) ?? config.companyOverrides?.[i] ?? {};
        return createTest__CompanyElement(i + 1, override);
    });

    // --- Generate Users (Traders and Auctioneers) ---
    let userIdCounter = 1;
    const traders: UserElement[] = Array.from({ length: numTraders }, (_, i) => {
        const companyIndex = i % store.companies.length; // Assign companies round-robin
        const company = store.companies[companyIndex];
        const userId = userIdCounter++;
        const override = config.userOverrides?.find(o => o.user_id === userId.toString()) ?? config.userOverrides?.[i] ?? {};
        return createTest__UserElement(userId, {
            role: AuUserRole.TRADER,
            company_id: company.company_id,
            company_shortname: company.company_shortname, // Keep consistent
            company_longname: company.company_longname, // Keep consistent
            ...override // Apply specific user overrides
        });
    });

    const auctioneers: UserElement[] = Array.from({ length: numAuctioneers }, (_, i) => {
        // Often auctioneers might belong to a specific "admin" company or the first one
        const companyIndex = (numTraders + i) % store.companies.length;
        const company = store.companies[companyIndex];
        const userId = userIdCounter++;
         const override = config.userOverrides?.find(o => o.user_id === userId.toString()) ?? config.userOverrides?.[numTraders + i] ?? {};
        return createTest__UserElement(userId, {
            role: AuUserRole.AUCTIONEER,
            company_id: company.company_id,
            company_shortname: company.company_shortname,
            company_longname: company.company_longname,
            ...override
        });
    });
    store.users = [...traders, ...auctioneers];

    // --- Generate Session User ---
    const loggedInUser = store.users.find(u => u.user_id === config.loggedInUserId)
        ?? (config.scenario === 'trader_logged_in' ? traders[0] : null)
        ?? (config.scenario === 'auctioneer_setup' ? auctioneers[0] : null)
        ?? store.users[0]; // Default to first user if any

    if (loggedInUser) {
        store.session_user = createTest__SessionUserValue({
            user_id: loggedInUser.user_id,
            username: loggedInUser.username,
            company_id: loggedInUser.company_id,
            company_shortname: loggedInUser.company_shortname,
            company_longname: loggedInUser.company_longname,
            role: loggedInUser.role,
            isAuctioneer: loggedInUser.role === AuUserRole.AUCTIONEER,
            // ... other session defaults
            ...(config.sessionUserOverrides ?? {}) // Apply overrides
        });
    } else {
         store.session_user = config.sessionUserOverrides
            ? createTest__SessionUserValue(config.sessionUserOverrides)
            : null; // Or a default logged-out state
    }


    // --- Generate DeAuction (Example - Needs refinement based on DeAuctionValue helper) ---
    // This part needs careful handling of dependencies (e.g., traders in blotter)
    if (config.scenario === 'auctioneer_setup' || config.scenario === 'auction_in_progress' || config.deAuctionOverrides) {
         // You'd likely have a createTest__DeAuctionValue helper that can take config
         // like the list of trader companies, auction state, etc.
        store.de_auction = createTest__DeAuctionValue({
             // Pass relevant generated data
             companies: store.companies,
             traders: traders.map(t => ({ company_id: t.company_id, shortname: t.company_shortname, /* ... other DeTraderElement fields */ })), // Map UserElement to DeTraderElement shape
             auctionState: config.scenario === 'auction_in_progress' ? DeCommonState.ROUND_OPEN : DeCommonState.SETUP,
             // ... other defaults based on scenario
             ...(config.deAuctionOverrides ?? {}) // Apply deep overrides
         });
    } else {
        store.de_auction = null;
    }

    // --- Generate other parts (auction_rows, counterparty_credits, time) similarly ---
    // store.time = createTest__TimeValue(...)
    // store.auction_rows = createTest__AuctionRowElementList(...)
    // store.counterparty_credits = createTest__CounterpartyCreditList(store.companies, ...)


    return store;
}

// Usage in a test:
const traderStore = createTestLiveClientStore({
    scenario: 'trader_logged_in',
    numTraders: 1,
    numCompanies: 2,
    loggedInUserId: '1', // Assuming trader user gets ID '1'
    sessionUserOverrides: { current_page: PageName.DE_TRADER_PAGE }
});

const complexAuctionStore = createTestLiveClientStore({
    scenario: 'auction_in_progress',
    numTraders: 5,
    numAuctioneers: 1,
    numCompanies: 6, // 5 traders + 1 auctioneer company
    loggedInUserId: '6', // Assuming auctioneer gets ID '6'
    deAuctionOverrides: {
        common_status: { // Deep override example
             common_state: DeCommonState.ROUND_OPEN,
             round_number: 3,
             round_price: "105.50"
        },
        // You might need a helper to generate blotter based on numTraders
        // blotter: createTestBlotterForTraders(traders)
    }
});
```

*   **Pros:**
    *   Centralized creation logic.
    *   High-level configuration (`scenario`, `numTraders`).
    *   Manages basic relationships (users get companies).
    *   Reduces boilerplate in tests significantly.
    *   Can incorporate the parameterized helpers (Strategy 1) internally.
*   **Cons:**
    *   Requires more upfront effort to build the factory.
    *   The factory can become complex if it needs to handle many scenarios and interdependencies.
    *   Deep overrides (`deAuctionOverrides`) might require careful implementation using merging utilities or dedicated config types for nested objects.

**3. Builder Pattern**

*   **Concept:** Create a `LiveClientStoreBuilder` class with fluent methods to configure the store step-by-step.
*   **Example:**

```typescript
import { LiveClientStore, CompanyElement, UserElement, AuUserRole /* ... */ } from "@repo/api-client";
// ... import test helpers

class LiveClientStoreBuilder {
    private _config: LiveClientStoreConfig = {}; // Use the config object internally

    withScenario(scenario: LiveClientStoreConfig['scenario']): this {
        this._config.scenario = scenario;
        // Could apply default numbers based on scenario here
        if (scenario === 'trader_logged_in' && this._config.numTraders === undefined) this._config.numTraders = 1;
        // ... etc
        return this;
    }

    withCompanies(count: number, overrides?: Partial<CompanyElement>[]): this {
        this._config.numCompanies = count;
        this._config.companyOverrides = overrides;
        return this;
    }

     withTraders(count: number, overrides?: Partial<UserElement>[]): this {
        this._config.numTraders = count;
        // Logic to potentially merge overrides if called multiple times
        this._config.userOverrides = [...(this._config.userOverrides ?? []), ...(overrides ?? [])];
        return this;
    }

     withLoggedInUser(userId: string, overrides?: Partial<SessionUserValue>): this {
         this._config.loggedInUserId = userId;
         this._config.sessionUserOverrides = overrides;
         return this;
     }

     withDeAuction(overrides: Partial<DeAuctionValue>): this {
         this._config.deAuctionOverrides = overrides;
         return this;
     }

    // ... other methods for auctioneers, time, etc.

    build(): LiveClientStore {
        // Call the factory function with the accumulated config
        return createTestLiveClientStore(this._config);
    }
}

// Usage in a test:
const store = new LiveClientStoreBuilder()
    .withScenario('auction_in_progress')
    .withCompanies(6)
    .withTraders(5)
    .withLoggedInUser('6') // Auctioneer ID
    .withDeAuction({ common_status: { round_number: 3 } })
    .build();
```

*   **Pros:**
    *   Fluent, readable API for test setup.
    *   Encapsulates the configuration logic.
    *   Can be easily extended with new configuration methods.
*   **Cons:**
    *   Requires writing and maintaining the builder class.
    *   Can be slightly more verbose than the factory for simple cases.

**Recommendation:**

1.  **Start with Strategy 1:** Enhance your existing helpers (`createDemo__` or new `createTest__`) to accept `Partial<T>` overrides. This gives immediate benefits with low effort.
2.  **Implement Strategy 2 (Factory Function):** This provides the most significant improvement for complex `LiveClientStore` creation. It centralizes logic and handles relationships. Use your enhanced helpers (from step 1) inside the factory. Define a `LiveClientStoreConfig` interface to guide usage.
3.  **Consider Strategy 3 (Builder) on top of Strategy 2:** If you find the configuration object for the factory becomes unwieldy or you prefer a fluent API, wrap the factory function call within a Builder class. The builder essentially becomes a user-friendly way to construct the configuration object before passing it to the factory.

This layered approach allows you to incrementally improve your test data generation, starting with simple overrides and moving towards a more robust and maintainable factory/builder system as needed. Remember to create dedicated `createTest__` helpers that produce deterministic (non-random) default data unless randomness is explicitly requested via an override.
