# @repo/api-client

## Overview
A real-time WebSocket-based API client implementing a specialized event-driven CQRS architecture for auction systems.

## Documentation
- [Architecture](./docs/ARCHITECTURE.md) - Detailed explanation of the real-time event-driven CQRS pattern
- [Usage Guide](./docs/USAGE.md) - How to use the API client in different contexts
- [Testing](./docs/TESTING.md) - Testing strategies and helpers

## Status
- [x] Core WebSocket connector implementation
- [x] LiveClientStore integration
- [x] Type-safe command handling
- [ ] Improved test coverage
- [ ] MSW implementation (see: https://egghead.io/lessons/make-a-type-safe-and-runtime-safe-web-socket-communication-with-zod~efw0y)

## Quick Start

```ts
import {Connector, ConnectorOptions, EngineCommandEnvelope} from '@repo/api-client';

const connector = Connector.create({
  url: 'ws://your-server.com/socket/',
  clientCommandHandlers: {
    onCommandSucceeded: (cmd) => console.log('Success!', cmd)
  }
});

connector.publish({
  session_id: 'some-session',
  simplename: "SessionConnectCommand",
  classname: "SessionConnectCommand",
  command: {
    // Command payload
  }
});
```

See the [Usage Guide](./docs/USAGE.md) for more detailed examples.

## Linking Packages Without Workspaces

Since you're not using PNPM workspaces, the way sibling packages use each other changes. Instead of the workspace:* protocol, you'll typically use local path dependencies.

In the consuming package (e.g., packages/my-app), you would run:
```shell
# Navigate to the consuming package's directory
cd ../my-app

# Add the connector using a relative path
pnpm add ../au25-connector
```

This tells PNPM to install the package located at ../au25-connector. PNPM is smart and will usually create a symlink in packages/my-app/node_modules/au25-connector pointing to your actual packages/au25-connector directory.

The consuming package's package.json will then have an entry like:
```json
// packages/my-app/package.json
"dependencies": {
  "@repo/api-client": "workspace:*", // Or similar depending on PNPM version
}
```



## Using the Package from Other Projects (Non-Monorepo)

This is now standard:

In the consuming project's directory:
```shell
npm install @your-scope/websocket-connector
# or yarn add @your-scope/websocket-connector
# or pnpm add @your-scope/websocket-connector
```

In the consuming project's code (e.g., a React component, a Node.js script):

```ts
import {Connector, ConnectorOptions, EngineCommandEnvelope} from '@your-scope/websocket-connector';

const options: ConnectorOptions = {
  url: 'ws://your-server.com/socket/',
  show_connector_log: true,
  clientCommandHandlers: {
    onSomeCommand: (cmd) => {
      console.log('Received SomeCommand:', cmd);
    },
    onCommandSucceeded: (cmd) => {
      console.log('Success!', cmd);
    }
// Add other handlers based on exported ClientCommand types
  },
  onError: (error) => {
    console.error("Connector Error:", error);
  }
};

try {
  const connector = Connector.create(options);

  // Send a command (ensure it matches an exported EngineCommandEnvelope type)
  const commandToSend: EngineCommandEnvelope = {
    session_id: 'some-session', // Or let the connector generate one
    simplename: "SessionConnectCommand", // Use exported CommandType if possible
    classname: "SessionConnectCommand",
    command: {
      // ... command payload matching the specific command type
    }
  };
  connector.publish(commandToSend);

} catch (error) {
  console.error("Failed to initialize connector:", error);
}

// Remember to handle cleanup if needed, e.g., connector.close() on component unmount
```

## Using the Connector from Sibling Packages - PNPM Workspaces

This is where PNPM workspaces shine:

1.  **Declare Dependency:** In the `package.json` of the *consuming* package (e.g., `packages/my-app/package.json`), add the connector using the `workspace:` protocol:

```json
// packages/my-app/package.json
{
  "name": "@your-workspace/my-app",
  "version": "1.0.0",
  "private": true,
  // ... other fields
  "dependencies": {
    // Reference the connector package using its name and the workspace protocol
    "@your-workspace/connector": "workspace:*",
    // ... other external dependencies (react, express, etc.)
  }
  // ... devDependencies, scripts
}
```
The `workspace:*` tells PNPM to link this dependency to the local package named `@your-workspace/connector` found within the workspace, instead of trying to download it from a registry.

2.  **Install:** Run `pnpm install` **at the project root**. PNPM will install all external dependencies for all packages and create the necessary symlinks in `node_modules` to connect your internal packages (`my-app` will be able to resolve imports from `@your-workspace/connector`).

3.  **Build the Connector:** Make sure you have run `pnpm build` (or `pnpm dev` for watch mode) inside the `packages/connector` directory (or use a root script like `pnpm --filter @your-workspace/connector build`). The `dist` folder *must* exist.

4.  **Import in Consumer:** Import as usual in your consuming package's code (`packages/my-app/src/someFile.ts`):

```typescript
import {
    Connector,
    ConnectorOptions,
    // ... other needed types
} from '@your-workspace/connector'; // Use the package name

// ... rest of your code using the connector ...
```

**Workflow Summary:**

1.  Set up `pnpm-workspace.yaml` at the root.
2.  Configure `packages/connector/package.json` with `private: true`, a unique `name`, and correct `main`/`module`/`types`/`exports` pointing to `dist`.
3.  Configure `vite.config.ts` and `tsconfig.json` in the connector package for library output to `dist`.
4.  In consuming packages (e.g., `packages/my-app`), add `"@your-workspace/connector": "workspace:*"` to dependencies in `package.json`.
5.  Run `pnpm install` at the root.
6.  Run `pnpm build` (or `pnpm dev`) in the connector package.
7.  Develop/build/run your consuming packages – they will import the compiled code from the connector's `dist` folder via the PNPM links.

## Explaining how a single tsconfig.json is used for dev, testing, and building library:

**Explanation of Changes/Rationale:**

1.  **`"noEmit": true` Removed:** Replaced by `"emitDeclarationOnly": true`. This is the modern way to configure TypeScript when a separate bundler (like Vite) handles the actual JavaScript generation. It allows `tsc` (or tools using the TS API like `vite-plugin-dts`) to focus *only* on creating the `.d.ts` files, while still performing full type-checking on your included files. Keeping `noEmit: true` can sometimes interfere with declaration generation plugins.
2.  **`include`:** Kept `tests/**/*.ts` here. This ensures `tsc --noEmit` (your `typecheck` script) and your IDE will correctly type-check your test files using the rules defined in this `tsconfig.json`.
3.  **`exclude`:** Correctly excludes build outputs and temporary directories. **Crucially, it does *not* exclude `tests`.**

**How it Works:**

*   **Development & Testing (`pnpm test`, `pnpm typecheck`):** TypeScript uses this `tsconfig.json`. It finds both `src` and `tests` via the `include` array and type-checks everything. Vitest also uses this configuration (implicitly or explicitly) to understand your code and types.
*   **Building (`pnpm build`):**
*   Vite runs the build.
*   Vite uses this `tsconfig.json` to understand path aliases (`@/*`) and basic TS settings.
*   Vite bundles the JavaScript starting from your entry point (`src/index.ts` defined in `vite.config.ts`). It **only includes code reachable from the entry point** in the final JS bundles (`dist/*.js`). Your test files won't be included in the *built JavaScript library output* unless you explicitly import them in your `src` code (which you shouldn't).
*   `vite-plugin-dts` runs. It uses the TypeScript API (and respects `tsconfig.json` settings like `declaration`, `declarationMap`, `outDir`) to analyze your *source* code (`src`) reachable from the entry point and generates the corresponding type definition files (`.d.ts`) in the `dist` directory. It **ignores** the `tests` directory when generating the public type signature for your library.


## setting up git-hooks to build ./dist every time I checkout or merge:

Since `dist` is generated and correctly listed in your `.gitignore` (it should be!), it won't exist after a fresh clone or checkout of a different branch unless you explicitly rebuild it.

Here are the common ways to manage this:

1.  **Manual Rebuilding (The Burden):**
  *   **What you identified:** Every time you `git checkout`, `git pull`, `git merge`, etc., that might bring in changes to `api-client`, you (and anyone else working on the project) **must remember** to navigate to `packages/api-client` and run `pnpm run build` before trying to run or build the sibling packages that depend on it.
  *   **Pros:** No extra setup.
  *   **Cons:** Highly error-prone, easy to forget, leads to confusing errors in consuming packages ("module not found", type errors, runtime errors from stale code), doesn't scale well with more local packages or team members.

2.  **Git Hooks (Automated Local Solution):**
  *   **Concept:** You can use Git hooks, specifically `post-checkout` and `post-merge`, to automatically trigger a build script *after* these Git operations complete.
  *   **Implementation:**
    *   Create a script, for example, `scripts/post-checkout-build.sh`:
        ```bash
        #!/bin/sh
        # scripts/post-checkout-build.sh

        echo "Git hook: Checking if connector build is needed..."

        # Check if the connector directory exists (optional, but safer)
        if [ -d "packages/api-client" ]; then
          echo "Git hook: Running build for api-client..."
          # Navigate and build. Use 'pnpm --dir <path> run build' for robustness
          pnpm --dir packages/api-client run build
          echo "Git hook: Connector build finished."
        else
          echo "Git hook: Connector directory not found, skipping build."
        fi

        exit 0 # Indicate success to Git
        ```
    *   Make it executable: `chmod +x scripts/post-checkout-build.sh`
    *   **Link/Copy to `.git/hooks`:** Git hooks live in the `.git/hooks` directory. This directory is *not* tracked by Git itself. You need to copy or symlink your script into `.git/hooks`.
      *   Copy: `cp scripts/post-checkout-build.sh .git/hooks/post-checkout`
      *   Copy: `cp scripts/post-checkout-build.sh .git/hooks/post-merge` (Use the same script for both hooks)
    *   **Sharing:** Since `.git/hooks` isn't tracked, each developer needs to set this up. You can create a setup script (e.g., `pnpm run setup-hooks`) committed to your repo that copies the necessary scripts into the local `.git/hooks` directory for them.
  *   **Pros:** Automates the build after relevant Git actions, reducing forgotten builds.
  *   **Cons:** Build runs even if connector source didn't change (can be slow), requires initial setup per developer (or a setup script), hooks aren't shared via Git directly.

3.  **Root-Level Build Scripts:**
  *   **Concept:** Define scripts in your *root* `package.json` (if you have one, outside the `packages` dir) or just adopt a convention to run a specific command from the root.
  *   **Implementation:**
      ```bash
      # Run from the project root directory
      pnpm --filter api-client build
      ```
      Or, if you have multiple buildable internal packages:
      ```bash
      # Build specific packages by name
      pnpm --filter api-client --filter @your-workspace/other-lib build

      # Or potentially build ALL packages that have a 'build' script (use with care)
      # pnpm -r run build
      ```
  *   **Pros:** Centralizes the build command, easier than `cd`-ing into each package.
  *   **Cons:** Still requires manual triggering after checkout/pull.

**Recommendation:**

*   **If you're working solo or on a small, disciplined team:** Option 1 (Manual) *might* suffice, but be prepared for occasional frustration. Clearly document the requirement.
*   **For better reliability/automation:** Option 2 (Git Hooks) is the most direct way to automate the build *locally* after checkout/merge. Just remember the setup step for each developer.
*   **Simplest Manual Improvement:** Option 3 (Root-Level Scripts) is better than navigating directories manually but still requires remembering to run the command.

No matter which you choose, **documenting** the chosen process for yourself and any collaborators is essential! Explain *why* the build is needed after Git operations and *how* to ensure it happens (manually, via hooks, or root scripts).
