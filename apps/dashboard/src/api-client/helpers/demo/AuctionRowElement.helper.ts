import type { AuctionRowElement } from "../../connector/types/generated";

export function createTest__AuctionRowElement(
    id: number | string,
    overrides: Partial<AuctionRowElement> = {}
): AuctionRowElement {
    const auctionIdStr = id.toString();
    const defaults: AuctionRowElement = {
        id: `auction-row-${auctionIdStr}`, // Store element ID
        auction_id: auctionIdStr,          // Business logic ID
        auction_name: `Test Auction ${auctionIdStr}`,
        auction_design: "DE", // Default design
        starting_time_text: "15 Jul 2024 10:30", // Example fixed text
        common_state_text: "Setup", // Example default state text
        isClosed: false,
        isHidden: false,
    };
    return { ...defaults, ...overrides };
}
