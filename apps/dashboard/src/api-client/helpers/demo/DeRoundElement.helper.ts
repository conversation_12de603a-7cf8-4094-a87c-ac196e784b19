import type { DeRoundElement } from "../../connector/types/generated";
import { OrderType, PriceDirection } from "../../connector/types/generated";

export function createTest__DeRoundElement(
    roundNumber: number,
    overrides: Partial<DeRoundElement> = {}
): DeRoundElement {
    const defaults: DeRoundElement = {
        id: `de-round-${roundNumber}`, // Store element ID
        round_number: roundNumber,
        round_price: roundNumber > 0 ? 100 + (roundNumber * 5) : null, // Example price progression
        round_price_str: roundNumber > 0 ? (100 + (roundNumber * 5)).toFixed(2) : "N/A",
        round_direction: roundNumber > 1 ? PriceDirection.UP : null, // Example direction
        buy_quantity: 0,
        sell_quantity: 0,
        matched: 0,
        raw_matched: 0,
        potential: 0,
        excess_quantity: 0,
        excess_side: OrderType.NONE,
        excess_indicator: "-", // Example
        buyer_count: 0,
        seller_count: 0,
        has_reversed: false,
        match_quantity_changed: 0,
        potential_changed: 0,
        sell_quantity_change: 0,
        round_duration: "00:00", // Example
        all_orders_in_next_round_will_be_mandatory: false,
    };
    return { ...defaults, ...overrides };
}