import type { DeRoundTraderElement, CompanyElement, DeBidConstraints } from "../../connector/types/generated";
import { OrderType, OrderSubmissionType } from "../../connector/types/generated";
// import { createTest__DeBidConstraints } from './DeBidConstraints.helper'; // Defined below

// Placeholder for constraints helper
export function createTest__DeBidConstraints(overrides: Partial<DeBidConstraints> = {}): DeBidConstraints {
     const defaults: DeBidConstraints = {
         min_buy_quantity: 0, max_buy_quantity: 1000,
         min_sell_quantity: 0, max_sell_quantity: 1000,
     };
     return { ...defaults, ...overrides };
}


export function createTest__DeRoundTraderElement(
    roundNumber: number,
    company: CompanyElement,
    overrides: Partial<DeRoundTraderElement> = {}
): DeRoundTraderElement {
    const defaults: DeRoundTraderElement = {
        id: `de-round-trader-${roundNumber}-${company.company_id}`, // Store element ID
        round: roundNumber,
        cid: company.company_id,
        company_shortname: company.company_shortname,
        order_type: OrderType.NONE,
        quantity_int: 0,
        quantity_str: "0",
        match: 0,
        changed: false,
        bid_while_closed: false,
        order_submission_type: OrderSubmissionType.DEFAULT, // Or MANUAL?
        order_submitted_by: "", // User ID who submitted
        timestamp_formatted: "15 Jul 2024 10:35:10", // Example fixed timestamp
        constraints: createTest__DeBidConstraints(),
        buyer_credit_limit: 1000000, // Example
        buyer_credit_limit_str: "$1,000,000.00", // Example
    };
    return { ...defaults, ...overrides };
}