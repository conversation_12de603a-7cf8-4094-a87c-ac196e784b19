import {
    AuMessageType,
    AuUserRole,
    type CompanyElement,
    type DeAuctioneerInfoValue,
    type DeAuctionValue,
    type DeAwardValue,
    type DeTraderHistoryRowElement,
    type DeTraderInfoValue,
    type MessageElement,
    OrderSubmissionType,
    OrderType,
    type UserElement
} from "../../connector/types/generated"; // Added missing enums
import {createTest__DeSettingsValue} from './DeSettingsValue.helper';
import {createTest__DeCommonStatusValue} from './DeCommonStatusValue.helper';
import {createTest__DeAuctioneerStatusValue} from './DeAuctioneerStatusValue.helper';
import {createTest__DeBlotter} from './DeBlotter.helper';
import {createTest__DeBidConstraints} from "./DeRoundTraderElement.helper";
// Other helpers needed: DeAuctioneerInfoValue, DeTraderInfoValue, DeAwardValue, MessageElement, DeTraderHistoryRowElement

// Placeholders for missing helpers (implement similarly to others)
export const createTest__DeAuctioneerInfoValue = (o: Partial<DeAuctioneerInfoValue> = {}): DeAuctioneerInfoValue => ({
    allow_credit_editing: false,
    last_round: 0,
    pen_round: '0',
    last_buyers: '',
    last_excess: '',
    last_match: '',
    last_sell_dec: '',
    last_sellers: '',
    last_total_buy: '',
    last_total_sell: '',
    pen_buyers: '',
    pen_excess: '',
    pen_match: '',
    pen_sell_dec: '',
    pen_sellers: '',
    pen_total_buy: '',
    pen_total_sell: '',
    potential: '', ...o
});

export const createTest__DeTraderInfoValue = (cid = '1', o: Partial<DeTraderInfoValue> = {}): DeTraderInfoValue => ({
    auction_id: '',
    award_direction: '',
    awarded_price: '',
    awarded_quantity: '',
    awarded_round_number: '',
    awarded_value: '',
    bid_constraints: createTest__DeBidConstraints(),
    company_id: cid,
    initial_limits: {
        initial_buying_cost_limit: 0,
        initial_buying_cost_limit_str: '',
        initial_selling_quantity_limit: 0,
        initial_selling_quantity_limit_str: ''
    },
    order_quantity: 0,
    order_submission_type: OrderSubmissionType.DEFAULT,
    order_type: OrderType.NONE,
    price_label: '',
    quantity_label: '',
    round_number: 0,
    round_price: '',
    value: '',
    award_line: null, ...o
});

export const createTest__DeAwardValue = (o: Partial<DeAwardValue> = {}): DeAwardValue => ({round_results: [], ...o});

export const createTest__MessageElement = (id = 1, o: Partial<MessageElement> = {}): MessageElement => ({
    id: `msg-${id}`,
    from: 'System',
    message: 'Test Message',
    message_type: AuMessageType.SYSTEM_BROADCAST,
    message_type_label: 'System Broadcast',
    timestamp: Date.now(),
    timestamp_label: new Date().toLocaleTimeString(),
    to: 'All', ...o
});

export const createTest__DeTraderHistoryRowElement = (id = 1, o: Partial<DeTraderHistoryRowElement> = {}): DeTraderHistoryRowElement => ({
    id: `hist-${id}`,
    auction_id: '',
    bid_constraints: null,
    company_id: '',
    excess_level: '',
    excess_side: null,
    order_submission_type: OrderSubmissionType.DEFAULT,
    order_submitted_by: '',
    order_type: null,
    price_direction: null,
    price_has_reversed: false,
    price_suffix: '',
    quantity: '0',
    round_number: '1',
    round_price: '',
    value: '', ...o
});


export function createTest__DeAuctionValue(
    auctionId: string,
    companiesInAuction: CompanyElement[] =[],
    usersInAuction: UserElement[] = [], // Needed for messages, seen status etc.
    maxRound: number = 0,
    overrides: Partial<DeAuctionValue> = {}
): DeAuctionValue {

    const traderCompanies = companiesInAuction.filter(c =>
        usersInAuction.some(u => u.company_id === c.company_id && u.role === AuUserRole.TRADER)
    );

    const defaults: DeAuctionValue = {
        auction_id: auctionId,
        settings: createTest__DeSettingsValue(),
        common_status: createTest__DeCommonStatusValue({round_number: maxRound}),
        auctioneer_status: createTest__DeAuctioneerStatusValue(),
        auctioneer_info: createTest__DeAuctioneerInfoValue({last_round: maxRound}),
        blotter: createTest__DeBlotter(traderCompanies, maxRound), // Only include trader companies in blotter
        trader_info: null, // Needs to be set based on logged-in trader
        award_value: null, // Usually null until awarded
        messages: [createTest__MessageElement(1)], // Default message
        notice: "This is a test auction notice.",
        trader_history_rows: [], // Needs specific generation based on trader/rounds
        auction_counterparty_credits: [], // Needs specific generation
        users_that_have_seen_auction: [], // Populate based on user actions
        matrix_last_round: null, // Needs specific generation
    };

    // TODO: Implement deep merging for nested objects if necessary.
    // This simple spread won't merge common_status correctly if override provides only { round_number: 3 }
    // It will replace the entire common_status object.
    // Using lodash.merge or a similar utility is recommended for deep merges.
    // return _.merge({}, defaults, overrides); // Example with lodash merge

    // Simple merge for now:
    const merged: DeAuctionValue = {
        ...defaults,
        ...overrides,
        // Manual deep merge for critical nested objects if lodash isn't used
        settings: overrides.settings ? {...defaults.settings, ...overrides.settings} : defaults.settings,
        common_status: overrides.common_status ? {...defaults.common_status, ...overrides.common_status} : defaults.common_status,
        auctioneer_status: overrides.auctioneer_status ? {...defaults.auctioneer_status, ...overrides.auctioneer_status} : defaults.auctioneer_status,
        auctioneer_info: overrides.auctioneer_info ? {...defaults.auctioneer_info, ...overrides.auctioneer_info} : defaults.auctioneer_info,
        blotter: overrides.blotter ? { // Example deep merge for blotter (adjust as needed)
            traders: overrides.blotter.traders ?? defaults.blotter?.traders ?? [],
            rounds: overrides.blotter.rounds ?? defaults.blotter?.rounds ?? [],
            round_traders: overrides.blotter.round_traders ?? defaults.blotter?.round_traders ?? [],
        } : defaults.blotter,
        // ... merge other nested objects as needed
    };

    // Post-merge consistency checks (example)
    if (merged.common_status && merged.blotter && merged.common_status.round_number > (merged.blotter.rounds?.length ?? 0)) {
        console.warn("Builder Warning: common_status.round_number exceeds rounds in blotter.");
        // Potentially adjust blotter here if desired auto-correction behavior
    }


    return merged;
}
