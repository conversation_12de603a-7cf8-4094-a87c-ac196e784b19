import type { DeTraderElement, CompanyElement } from "../../connector/types/generated";

export function createTest__DeTraderElement(
    company: CompanyElement,
    overrides: Partial<DeTraderElement> = {}
): DeTraderElement {
    const defaults: DeTraderElement = {
        id: `de-trader-${company.company_id}`, // Store element ID
        company_id: company.company_id,
        shortname: company.company_shortname,
        has_seen_auction: false,
        rank: null,
    };
    return { ...defaults, ...overrides };
}