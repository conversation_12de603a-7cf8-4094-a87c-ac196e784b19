import type { SessionUserValue, UserElement } from "../../connector/types/generated";
import { ClientSocketState, PageName } from "../../connector/types/generated";

export function createTest__SessionUserValue(
    loggedInUser: UserElement, // Base the session on an actual user
    overrides: Partial<SessionUserValue> = {}
): SessionUserValue {
    const defaults: SessionUserValue = {
        session_id: `session-for-${loggedInUser.user_id}`,
        user_id: loggedInUser.user_id,
        username: loggedInUser.username,
        company_id: loggedInUser.company_id,
        company_shortname: loggedInUser.company_shortname,
        company_longname: loggedInUser.company_longname,
        role: loggedInUser.role,
        isAuctioneer: loggedInUser.isAuctioneer,
        isOnline: true,
        socket_state: ClientSocketState.OPENED,
        current_auction_id: loggedInUser.current_auction_id ?? "auction-1", // Default auction ID if needed
        current_page: loggedInUser.isAuctioneer ? PageName.DE_AUCTIONEER_PAGE : PageName.DE_TRADER_PAGE, // Sensible default page
        client_info: { // Example client info
             platform: "TestPlatform",
             userAgent: "TestAgent/1.0",
             language: "en-US",
             clientVersion: "0.1.0-test"
        }
    };
    return { ...defaults, ...overrides };
}

// Helper for a logged-out state
export function createTest__SessionUserValue_LoggedOut(): SessionUserValue | null {
    // Depending on your app's logic, return null or a specific logged-out object
    return null;
}
