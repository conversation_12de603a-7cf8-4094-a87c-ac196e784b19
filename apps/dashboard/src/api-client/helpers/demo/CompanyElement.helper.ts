import type { CompanyElement } from "../../connector/types/generated";
import {random_int_inclusive} from "../helpers/random-helpers";

export function createTest__CompanyElement(
    id: number | string,
    overrides: Partial<CompanyElement> = {}
): CompanyElement {
    const companyIdStr = id.toString();
    const defaults: CompanyElement = {
        id: `company-${companyIdStr}`, // Store element ID
        company_id: companyIdStr,     // Business logic ID
        company_longname: `Test Long Company ${companyIdStr}`,
        company_shortname: `TestC${companyIdStr}`,
    };
    return { ...defaults, ...overrides };
}

export function createRandom__CompanyElement(max:number = 1): CompanyElement {
    return createTest__CompanyElement(random_int_inclusive(1,max));
}
