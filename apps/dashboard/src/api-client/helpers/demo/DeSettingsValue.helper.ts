import type { DeSettingsValue } from "../../connector/types/generated"; // Added DateTimeValue
import { createTest__DateTimeValue } from '../helpers/date-time-helper'; // Corrected path assumption

export function createTest__DeSettingsValue(overrides: Partial<DeSettingsValue> = {}): DeSettingsValue {
    const defaults: DeSettingsValue = {
        auction_name: "Test DE Auction",
        price_label: "Price ($/MWh)",
        quantity_label: "Quantity (MW)",
        price_decimal_places: 2,
        price_change_initial: "1.00",
        price_change_post_reversal: "0.50",
        quantity_minimum: "10",
        quantity_step: "5",
        excess_level_0_label: "Balanced",
        excess_level_1_label: "1-50 MW",
        excess_level_1_quantity: "50",
        excess_level_2_label: "51-100 MW",
        excess_level_2_quantity: "100",
        excess_level_3_label: "101-200 MW",
        excess_level_3_quantity: "200",
        excess_level_4_label: ">200 MW",
        excess_level_4_quantity: "999999", // Represents infinity effectively
        cost_multiplier: "1.0",
        starting_time: createTest__DateTimeValue(),
        starting_price_announcement_mins: 15,
        round_open_min_secs: 60,
        round_orange_secs: 30,
        round_red_secs: 15,
        round_closed_min_secs: 10,
        use_counterparty_credits: false,
        // month_is_1_based: true, // Assuming this might be part of settings if needed
    };
    // Note: Deep merging might be needed if overrides contain nested objects like starting_time
    // For simplicity here, we assume overrides replace the whole nested object if provided.
    return { ...defaults, ...overrides };
}
