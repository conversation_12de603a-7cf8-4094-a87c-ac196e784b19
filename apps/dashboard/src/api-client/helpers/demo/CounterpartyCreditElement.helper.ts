import type { CounterpartyCreditElement, CompanyElement } from "../../connector/types/generated";

export function createTest__CounterpartyCreditElement(
    buyer: CompanyElement,
    seller: CompanyElement,
    overrides: Partial<CounterpartyCreditElement> = {}
): CounterpartyCreditElement {
    const defaultLimit = 1000000; // Example default credit limit
    const defaults: CounterpartyCreditElement = {
        id: `credit-${buyer.company_id}-to-${seller.company_id}`, // Store element ID
        buyer_id: buyer.company_id,
        buyer_shortname: buyer.company_shortname,
        buyer_longname: buyer.company_longname,
        seller_id: seller.company_id,
        seller_shortname: seller.company_shortname,
        seller_longname: seller.company_longname,
        limit_str: defaultLimit.toLocaleString('en-US', { style: 'currency', currency: 'USD' }), // Formatted string
        // limit: defaultLimit, // If you have the numeric value too
    };
    return { ...defaults, ...overrides };
}