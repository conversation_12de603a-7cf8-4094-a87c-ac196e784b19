import type { UserElement, CompanyElement } from "../../connector/types/generated"; // Added CompanyElement import
import { AuUserRole, ClientSocketState } from "../../connector/types/generated"; // Assuming these enums are imported/available

export function createTest__UserElement(
    id: number | string,
    relatedCompany: CompanyElement, // Pass the company for consistency
    overrides: Partial<UserElement> = {}
): UserElement {
    const userIdStr = id.toString();
    const defaults: UserElement = {
        id: `user-${userIdStr}`, // Store element ID
        user_id: userIdStr,      // Business logic ID
        username: `testuser${userIdStr}`,
        password: 'password', // Default password for tests
        email: `test${userIdStr}@example.com`,
        phone: `123-456-${userIdStr.padStart(4, '0')}`,
        role: AuUserRole.TRADER, // Default role
        company_id: relatedCompany?.company_id,
        company_shortname: relatedCompany.company_shortname, // Keep consistent
        company_longname: relatedCompany.company_longname, // Keep consistent
        isAuctioneer: false,
        isObserver: false,
        isTester: false,
        isOnline: true,
        has_connection_problem: false,
        current_auction_id: null,
        socket_state: ClientSocketState.OPENED,
        socket_state_last_closed: null,
        termination_reason: null,
    };

    // Adjust defaults based on role override if present
    if (overrides.role === AuUserRole.AUCTIONEER) {
        defaults.isAuctioneer = true;
        defaults.role = AuUserRole.AUCTIONEER;
    } else if (overrides.role === AuUserRole.TRADER) {
         defaults.isAuctioneer = false;
         defaults.role = AuUserRole.TRADER;
    }


    const final = { ...defaults, ...overrides };

    // Ensure consistency if role is changed by override
     if (final.role === AuUserRole.AUCTIONEER) {
        final.isAuctioneer = true;
    } else {
         final.isAuctioneer = false;
     }


    return final;
}

