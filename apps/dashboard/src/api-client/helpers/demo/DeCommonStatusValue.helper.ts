import type { DeCommonStatusValue } from "../../connector/types/generated";
import { DeCommonState } from "../../connector/types/generated";

export function createTest__DeCommonStatusValue(overrides: Partial<DeCommonStatusValue> = {}): DeCommonStatusValue {
    const defaults: DeCommonStatusValue = {
        common_state: DeCommonState.SETUP,
        common_state_text: "Setup",
        round_number: 0,
        round_price: "100.00", // Starting price example
        round_seconds: 0,
        price_direction: null,
        price_has_reversed: false,
        starting_price_announced: false,
        starting_time_text: "15 Jul 2024 10:30", // Should match settings ideally
        isClosed: false,
    };
     // Basic state consistency
     if (overrides.common_state === DeCommonState.AUCTION_CLOSED) {
         defaults.isClosed = true;
     }
     if (overrides.round_number && overrides.round_number > 0 && defaults.common_state === DeCommonState.SETUP) {
         defaults.common_state = DeCommonState.ROUND_OPEN; // Sensible default if round > 0
         defaults.common_state_text = "Round Open";
     }


    return { ...defaults, ...overrides };
}
