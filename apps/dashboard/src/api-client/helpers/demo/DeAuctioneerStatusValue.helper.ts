import type { DeAuctioneerStatusValue } from "../../connector/types/generated";
import { AutopilotMode, DeAuctioneerState, DeFlowControlType, OrderType, DeTimeState } from "../../connector/types/generated";

export function createTest__DeAuctioneerStatusValue(overrides: Partial<DeAuctioneerStatusValue> = {}): DeAuctioneerStatusValue {
    const defaults: DeAuctioneerStatusValue = {
        auctioneer_state: DeAuctioneerState.STARTING_PRICE_NOT_SET,
        auctioneer_state_text: "Starting Price Not Set",
        autopilot: AutopilotMode.DISENGAGED,
        announced: false,
        awardable: false,
        starting_price: "100.00", // Example
        excess_level: "Balanced",
        excess_side: OrderType.NONE,
        price_has_overshot: false,
        round_open_min_secs: null, // Or get from settings
        time_state: DeTimeState.BEFORE_ANNOUNCE_TIME,
        controls: { // Default controls state (usually all disabled initially)
            [DeFlowControlType.HEARTBEAT]: true, // Always enabled?
            [DeFlowControlType.SET_STARTING_PRICE]: true,
            [DeFlowControlType.ANNOUNCE_STARTING_PRICE]: false,
            [DeFlowControlType.START_AUCTION]: false,
            [DeFlowControlType.CLOSE_ROUND]: false,
            [DeFlowControlType.REOPEN_ROUND]: false,
            [DeFlowControlType.NEXT_ROUND]: false,
            [DeFlowControlType.AWARD_AUCTION]: false,
        },
    };
    // TODO: Add more sophisticated logic to set controls based on auctioneer_state if needed
    return { ...defaults, ...overrides };
}