import type { TimeValue } from "../../connector/types/generated"; // Added DateTimeValue
import { createTest__DateTimeValue } from '../helpers/date-time-helper'; // Corrected path assumption

export function createTest__TimeValue(overrides: Partial<TimeValue> = {}): TimeValue {
    const defaults: TimeValue = {
        city: "Test/City",
        date_time: createTest__DateTimeValue(),
    };
    return { ...defaults, ...overrides };
}
