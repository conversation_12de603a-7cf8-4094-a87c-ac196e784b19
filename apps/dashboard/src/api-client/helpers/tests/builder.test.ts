import { strict as assert } from 'node:assert';
import { LiveClientStoreBuilder } from '../builder/LiveClientStore.builder'; // Adjust path if needed
import {AuUserRole, DeCommonState, PageName, type UserElement} from '../../connector/types/generated'; // Adjust path if needed

console.log("Running LiveClientStoreBuilder Tests...");

// --- Test Case 1: Default Build ---
try {
    const builder = new LiveClientStoreBuilder();
    const store = builder.build();

    assert.ok(store, "Test 1 Failed: Store should be created");
    assert.equal(store.companies.length, 3, "Test 1 Failed: Default company count should be 3"); // Default logic might vary, adjust as needed
    assert.equal(store.users.length, 3, "Test 1 Failed: Default user count should be 3 (2 traders, 1 auctioneer)");
    assert.ok(store.session_user, "Test 1 Failed: Default build should have a session user");
    assert.equal(store.session_user?.user_id, '1', "Test 1 Failed: Default session user should be the first user (trader)"); // Based on current factory logic fallback
    assert.ok(store.de_auction, "Test 1 Failed: Default build should create de_auction");
    assert.equal(store.de_auction?.auction_id, 'auction-1', "Test 1 Failed: Default active auction ID");
    console.log("Test 1 Passed: Default Build");
} catch (error) {
    console.error("Test 1 Failed:", error);
}

// --- Test Case 2: Scenario - Trader Logged In ---
try {
    const builder = new LiveClientStoreBuilder()
        .withScenario('trader_logged_in')
        .withActiveAuction('trader-auction'); // Use a specific auction ID
    const store = builder.build();

    assert.ok(store, "Test 2 Failed: Store should be created");
    assert.equal(store.companies.length, 2, "Test 2 Failed: Trader scenario default company count");
    assert.equal(store.users.length, 2, "Test 2 Failed: Trader scenario default user count (1 trader, 1 auctioneer)");
    const traderUser = store.users.find((u:UserElement) => u.role === AuUserRole.TRADER);
    const auctioneerUser = store.users.find((u:UserElement) => u.role === AuUserRole.AUCTIONEER);
    assert.ok(traderUser, "Test 2 Failed: Should have a trader user");
    assert.ok(auctioneerUser, "Test 2 Failed: Should have an auctioneer user");
    assert.ok(store.session_user, "Test 2 Failed: Should have a session user");
    assert.equal(store.session_user?.role, AuUserRole.TRADER, "Test 2 Failed: Session user should be trader");
    assert.equal(store.session_user?.user_id, traderUser?.user_id, "Test 2 Failed: Session user ID should match trader");
    assert.equal(store.session_user?.current_page, PageName.DE_TRADER_PAGE, "Test 2 Failed: Default page for trader");
    assert.ok(store.de_auction, "Test 2 Failed: Should have de_auction");
    assert.equal(store.de_auction?.auction_id, 'trader-auction', "Test 2 Failed: Active auction ID should match");
    assert.equal(store.de_auction?.common_status?.common_state, DeCommonState.SETUP, "Test 2 Failed: Expected state for trader scenario");
    assert.ok(store.de_auction?.trader_info, "Test 2 Failed: Trader info should be populated");
    assert.equal(store.de_auction?.trader_info?.company_id, store.session_user?.company_id, "Test 2 Failed: Trader info company ID mismatch");
    console.log("Test 2 Passed: Scenario - Trader Logged In");
} catch (error) {
    console.error("Test 2 Failed:", error);
}

// --- Test Case 3: Scenario - Auctioneer Setup ---
try {
    const builder = new LiveClientStoreBuilder()
        .withScenario('auctioneer_setup')
        .withLoggedInUser('3'); // Explicitly log in auctioneer (assuming ID 3)
    const store = builder.build();

    assert.ok(store, "Test 3 Failed: Store should be created");
    assert.equal(store.companies.length, 3, "Test 3 Failed: Auctioneer setup default company count");
    assert.equal(store.users.length, 3, "Test 3 Failed: Auctioneer setup default user count (2 traders, 1 auctioneer)");
    assert.ok(store.session_user, "Test 3 Failed: Should have a session user");
    assert.equal(store.session_user?.role, AuUserRole.AUCTIONEER, "Test 3 Failed: Session user should be auctioneer");
    assert.equal(store.session_user?.user_id, '3', "Test 3 Failed: Session user ID should be 3");
    assert.equal(store.session_user?.current_page, PageName.DE_AUCTIONEER_PAGE, "Test 3 Failed: Default page for auctioneer");
    assert.ok(store.de_auction, "Test 3 Failed: Should have de_auction");
    assert.equal(store.de_auction?.common_status?.common_state, DeCommonState.SETUP, "Test 3 Failed: Expected state for auctioneer setup");
    assert.ok(store.de_auction.auctioneer_info, "Test 3 Failed: Auctioneer info should exist");
    assert.equal(store.de_auction.trader_info, null, "Test 3 Failed: Trader info should be null for auctioneer");
    console.log("Test 3 Passed: Scenario - Auctioneer Setup");
} catch (error) {
    console.error("Test 3 Failed:", error);
}

// --- Test Case 4: Specific Counts and Overrides ---
try {
    const builder = new LiveClientStoreBuilder()
        .withCompanies(5, [{ company_id: '1', company_shortname: 'FirstCo' }, { company_id: '5', company_longname: 'The Last Company LLC' }])
        .withTraders(3)
        .withAuctioneers(1, [{ user_id: '4', username: 'TheAuctioneer' }]) // User ID 4 = 3 traders + 1st auctioneer
        .withLoggedInUser('4')
        .withActiveAuction('custom-auction-xyz')
        .withDeAuction({ notice: 'Custom Auction Notice Test' })
        .withCredits('all_pairs');
    const store = builder.build();

    assert.ok(store, "Test 4 Failed: Store should be created");
    assert.equal(store.companies.length, 5, "Test 4 Failed: Company count mismatch");
    assert.equal(store.users.length, 4, "Test 4 Failed: User count mismatch (3 traders + 1 auctioneer)");
    assert.equal(store.companies[0]?.company_shortname, 'FirstCo', "Test 4 Failed: Company override 1 failed");
    assert.equal(store.companies[4]?.company_longname, 'The Last Company LLC', "Test 4 Failed: Company override 2 failed");
    const auctioneer = store.users.find(u => u.user_id === '4');
    assert.ok(auctioneer, "Test 4 Failed: Auctioneer user not found");
    assert.equal(auctioneer?.username, 'TheAuctioneer', "Test 4 Failed: Auctioneer override failed");
    assert.equal(store.session_user?.user_id, '4', "Test 4 Failed: Logged in user mismatch");
    assert.ok(store.de_auction, "Test 4 Failed: DeAuction missing");
    assert.equal(store.de_auction?.auction_id, 'custom-auction-xyz', "Test 4 Failed: Active auction ID mismatch");
    assert.equal(store.de_auction?.notice, 'Custom Auction Notice Test', "Test 4 Failed: DeAuction override failed");
    // Check credits: 5 companies -> 5*4 / 2 = 10 pairs * 2 directions = 20 credits
    assert.equal(store.counterparty_credits.length, 20, "Test 4 Failed: Credit count mismatch for all_pairs");
    console.log("Test 4 Passed: Specific Counts and Overrides");
} catch (error) {
    console.error("Test 4 Failed:", error);
}

// --- Test Case 5: Auction Row Overrides ---
try {
    const builder = new LiveClientStoreBuilder()
        .withActiveAuction('main-auction')
        .withGenericAuctionRows(1) // 1 generic + 1 active = 2 total
        .withAuctionRowOverrides([
            { auction_id: 'main-auction', auction_name: 'The Main Event', isHidden: true },
            { auction_id: 'auction-2', common_state_text: 'Closed Manually' } // Override the generic one
        ]);
    const store = builder.build();

    assert.ok(store, "Test 5 Failed: Store should be created");
    assert.equal(store.auction_rows.length, 2, "Test 5 Failed: Auction row count mismatch");
    const mainAuctionRow = store.auction_rows.find(r => r.auction_id === 'main-auction');
    const genericAuctionRow = store.auction_rows.find(r => r.auction_id === 'auction-2');
    assert.ok(mainAuctionRow, "Test 5 Failed: Main auction row missing");
    assert.ok(genericAuctionRow, "Test 5 Failed: Generic auction row missing");
    assert.equal(mainAuctionRow?.auction_name, 'The Main Event', "Test 5 Failed: Main auction override failed (name)");
    assert.equal(mainAuctionRow?.isHidden, true, "Test 5 Failed: Main auction override failed (isHidden)");
    assert.equal(genericAuctionRow?.common_state_text, 'Closed Manually', "Test 5 Failed: Generic auction override failed");
    console.log("Test 5 Passed: Auction Row Overrides");
} catch (error) {
    console.error("Test 5 Failed:", error);
}


// --- Test Case 6: Empty Scenario ---
try {
    const builder = new LiveClientStoreBuilder()
        .withScenario('empty');
    const store = builder.build();

    assert.ok(store, "Test 6 Failed: Store should be created");
    assert.equal(store.companies.length, 0, "Test 6 Failed: Empty scenario company count");
    assert.equal(store.users.length, 0, "Test 6 Failed: Empty scenario user count");
    assert.equal(store.session_user, null, "Test 6 Failed: Empty scenario session user should be null");
    assert.equal(store.de_auction, null, "Test 6 Failed: Empty scenario de_auction should be null");
    assert.equal(store.auction_rows.length, 0, "Test 6 Failed: Empty scenario auction rows");
    console.log("Test 6 Passed: Empty Scenario");
} catch (error) {
    console.error("Test 6 Failed:", error);
}


console.log("All LiveClientStoreBuilder Tests Attempted.");
