import type { DateTimeValue } from "../../connector/types/generated";

// Provides a fixed, deterministic date for tests unless overridden
export const createTest__DateTimeValue = (overrides: Partial<DateTimeValue> = {}): DateTimeValue => {
    const defaults: DateTimeValue = {
        year: 2024,
        month: 7, // 1-based month
        day_of_month: 15,
        day_of_week: 1, // Monday
        hour: 10,
        minutes: 30,
        seconds: 0,
    };
    return { ...defaults, ...overrides };
};