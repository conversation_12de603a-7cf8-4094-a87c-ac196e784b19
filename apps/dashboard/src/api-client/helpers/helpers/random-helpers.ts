// NOTE: For testing, we generally want deterministic data.
// These helpers might be used *sparingly* if some variance is needed,
// but prefer fixed values in test helpers.

export const test_string = (prefix: string, id: string | number): string => `${prefix}-${id}`;

export function random_int_inclusive (min:number = 1, max: number): number {
    min = Math.ceil(min);   // Ensure min is an integer (or rounded up if float)
    max = Math.floor(max); // Ensure max is an integer (or rounded down if float)
    return Math.floor(Math.random() * (max - min + 1)) + min;
    // The maximum is inclusive and the minimum is inclusive
}

// const random_number_string = (start = 1, to: number, fixed=2): string => (start + Math.random() * to).toFixed(fixed).toString(); // Example fixed format

// function random_bool(probabilityOfTrue: number = 0.5): boolean {
//     if (probabilityOfTrue < 0 || probabilityOfTrue > 1) {
//         console.warn(
//             `getRandomBoolean: probabilityOfTrue (${probabilityOfTrue}) is out of range [0, 1]. Clamping to the nearest valid value.`
//         );
//         probabilityOfTrue = Math.max(0, Math.min(1, probabilityOfTrue)); // Clamp between 0 and 1
//     }
//     return Math.random() < probabilityOfTrue;
// }

// Keep random_enum if needed for selecting from fixed sets deterministically or randomly
export const random_enum = <T>(enumObject: Record<string, T>): T => {
    const values = Object.values(enumObject);
    // For true randomness (if needed): return values[Math.floor(Math.random() * values.length)];
    // For deterministic selection (example):
    const index = Object.keys(enumObject).length % values.length; // Simple deterministic pick
    return values[index];
};
