import type {
    // UserSaveCommand, // TODO: Use when implementing user save
    // UserDeleteCommand, // TODO: Use when implementing user delete
    // CompanySaveCommand, // TODO: Use when implementing company save
    // CompanyDeleteCommand, // TODO: Use when implementing company delete
    UserElement,
    CompanyElement
} from '../../connector/types/generated';
import { AuUserRole } from '../../connector/types/generated';
import {
    user_save_command,
    user_delete_command,
    company_save_command,
    company_delete_command
} from '../../connector/types/generated';

/**
 * CQRS Command Helper for User & Company Management
 *
 * This helper provides functions to create and dispatch commands for user and company operations.
 * Commands are sent to the server asynchronously, and the UI subscribes to store updates.
 *
 * IMPORTANT: This follows CQRS pattern - no request/response!
 * - Commands are dispatched to server
 * - UI subscribes to valtio store changes
 * - Store updates happen asynchronously when server processes commands
 */

// Type for command dispatcher function (to be provided by consuming app)
export type CommandDispatcher = (command: any) => void;

/**
 * User Management Commands
 */
export class UserCommands {
    constructor(private dispatch: CommandDispatcher) {}

    /**
     * Create a new user
     */
    createUser(userData: {
        username: string;
        email: string;
        password: string;
        phone: string;
        role: AuUserRole;
        company_id: string;
    }): void {
        // Generate new user ID (in real app, this might come from server or be UUID)
        const user_id = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const command = user_save_command({
            user_id,
            username: userData.username,
            email: userData.email,
            password: userData.password,
            phone: userData.phone,
            role: userData.role,
            company_id: userData.company_id
        });

        this.dispatch(command);
    }

    /**
     * Update an existing user
     */
    updateUser(user: UserElement, updates: Partial<{
        username: string;
        email: string;
        password: string;
        phone: string;
        role: AuUserRole;
        company_id: string;
    }>): void {
        const command = user_save_command({
            user_id: user.user_id,
            username: updates.username ?? user.username,
            email: updates.email ?? user.email,
            password: updates.password ?? user.password,
            phone: updates.phone ?? user.phone,
            role: updates.role ?? user.role,
            company_id: updates.company_id ?? user.company_id
        });

        this.dispatch(command);
    }

    /**
     * Delete a user
     */
    deleteUser(user_id: string): void {
        const command = user_delete_command({
            user_id
        });

        this.dispatch(command);
    }

    /**
     * Assign user to a different company
     */
    assignUserToCompany(user: UserElement, company_id: string): void {
        this.updateUser(user, { company_id });
    }

    /**
     * Change user role
     */
    changeUserRole(user: UserElement, role: AuUserRole): void {
        this.updateUser(user, { role });
    }

    /**
     * Bulk delete users
     */
    bulkDeleteUsers(user_ids: string[]): void {
        user_ids.forEach(user_id => {
            this.deleteUser(user_id);
        });
    }

    /**
     * Bulk assign users to company
     */
    bulkAssignUsersToCompany(users: UserElement[], company_id: string): void {
        users.forEach(user => {
            this.assignUserToCompany(user, company_id);
        });
    }
}

/**
 * Company Management Commands
 */
export class CompanyCommands {
    constructor(private dispatch: CommandDispatcher) {}

    /**
     * Create a new company
     */
    createCompany(companyData: {
        company_shortname: string;
        company_longname: string;
    }): void {
        // Generate new company ID
        const company_id = `company-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const command = company_save_command({
            company_id,
            company_shortname: companyData.company_shortname,
            company_longname: companyData.company_longname
        });

        this.dispatch(command);
    }

    /**
     * Update an existing company
     */
    updateCompany(company: CompanyElement, updates: Partial<{
        company_shortname: string;
        company_longname: string;
    }>): void {
        const command = company_save_command({
            company_id: company.company_id,
            company_shortname: updates.company_shortname ?? company.company_shortname,
            company_longname: updates.company_longname ?? company.company_longname
        });

        this.dispatch(command);
    }

    /**
     * Delete a company
     */
    deleteCompany(company_id: string): void {
        const command = company_delete_command({
            company_id
        });

        this.dispatch(command);
    }
}

/**
 * Combined User & Company Commands Helper
 */
export class UserCompanyCommands {
    public readonly users: UserCommands;
    public readonly companies: CompanyCommands;

    constructor(dispatch: CommandDispatcher) {
        this.users = new UserCommands(dispatch);
        this.companies = new CompanyCommands(dispatch);
    }
}

/**
 * Factory function to create command helper with dispatcher
 */
export function createUserCompanyCommands(dispatch: CommandDispatcher): UserCompanyCommands {
    return new UserCompanyCommands(dispatch);
}

/**
 * Utility functions for validation
 */
export const UserCompanyValidation = {
    /**
     * Validate user data before creating/updating
     */
    validateUserData(data: {
        username: string;
        email: string;
        password?: string;
        phone: string;
        role: AuUserRole;
        company_id: string;
    }): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!data.username || data.username.trim().length < 3) {
            errors.push('Username must be at least 3 characters long');
        }

        if (!data.email || !data.email.includes('@')) {
            errors.push('Valid email address is required');
        }

        if (data.password && data.password.length < 6) {
            errors.push('Password must be at least 6 characters long');
        }

        if (!data.company_id) {
            errors.push('Company assignment is required');
        }

        if (!Object.values(AuUserRole).includes(data.role)) {
            errors.push('Valid role is required');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    },

    /**
     * Validate company data before creating/updating
     */
    validateCompanyData(data: {
        company_shortname: string;
        company_longname: string;
    }): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!data.company_shortname || data.company_shortname.trim().length < 2) {
            errors.push('Company short name must be at least 2 characters long');
        }

        if (!data.company_longname || data.company_longname.trim().length < 3) {
            errors.push('Company long name must be at least 3 characters long');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
};
