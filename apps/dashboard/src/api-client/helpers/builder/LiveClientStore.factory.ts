import {
    LiveClientS<PERSON>,
    <PERSON>U<PERSON>R<PERSON>,
    DeCommonState,
    type DeAuctionValue
} from "../../connector/types/generated"; // Import from api-client
import type { LiveClientStoreConfig } from './LiveClientStore.config';
import { createTest__CompanyElement } from '../demo/CompanyElement.helper';
import { createTest__UserElement } from '../demo/UserElement.helper';
import { createTest__SessionUserValue, createTest__SessionUserValue_LoggedOut } from '../demo/SessionUserValue.helper';
import { createTest__TimeValue } from '../demo/TimeValue.helper';
import { createTest__AuctionRowElement } from '../demo/AuctionRowElement.helper';
import { createTest__CounterpartyCreditElement } from '../demo/CounterpartyCreditElement.helper';
import { createTest__DeAuctionValue, createTest__DeTraderInfoValue, createTest__DeTraderHistoryRowElement } from '../demo/DeAuctionValue.helper'; // Added missing DeAuction helpers
import type { CompanyElement, UserElement } from "../../connector/types/generated"; // Added missing types
// ... import other necessary helpers ...
// import * as _ from 'lodash'; // Optional: for deep merging

// The Factory Function
export function createTestLiveClientStore(config: LiveClientStoreConfig = {}): LiveClientStore {
    const store = new LiveClientStore();
    const activeAuctionId = config.activeAuctionId ?? "auction-1"; // Default active auction

    // --- Apply Defaults based on Scenario or Config ---
    let numCompanies = config.numCompanies;
    let numTraders = config.numTraders;
    let numAuctioneers = config.numAuctioneers;
    let scenarioMaxRound = 0;
    let scenarioState = DeCommonState.SETUP;

    switch (config.scenario) {
        case 'empty':
            numCompanies = numCompanies ?? 0;
            numTraders = numTraders ?? 0;
            numAuctioneers = numAuctioneers ?? 0;
            break;
        case 'trader_logged_in':
            numCompanies = numCompanies ?? 2; // Trader's company + maybe another
            numTraders = numTraders ?? 1;
            numAuctioneers = numAuctioneers ?? 1; // Often an auctioneer exists
            scenarioState = DeCommonState.STARTING_PRICE_ANNOUNCED; // Plausible state
            break;
        case 'auctioneer_setup':
            numCompanies = numCompanies ?? 3; // Auctioneer + >=2 traders
            numTraders = numTraders ?? 2;
            numAuctioneers = numAuctioneers ?? 1;
            scenarioState = DeCommonState.SETUP;
            break;
        case 'auction_in_progress':
            numCompanies = numCompanies ?? 5;
            numTraders = numTraders ?? 4;
            numAuctioneers = numAuctioneers ?? 1;
            scenarioMaxRound = config.deAuctionConfig?.maxRound ?? 3; // Default to round 3
            scenarioState = DeCommonState.ROUND_OPEN;
            break;
         case 'auction_closed':
            numCompanies = numCompanies ?? 5;
            numTraders = numTraders ?? 4;
            numAuctioneers = numAuctioneers ?? 1;
            scenarioMaxRound = config.deAuctionConfig?.maxRound ?? 5; // Default to round 5 finished
            scenarioState = DeCommonState.AUCTION_CLOSED;
            break;
        default:
            // Default case if no scenario or unknown scenario
            numCompanies = numCompanies ?? 3;
            numTraders = numTraders ?? 2;
            numAuctioneers = numAuctioneers ?? 1;
            break;
    }
     // Ensure enough companies for users
     const requiredCompanies = (numTraders ?? 0) + (numAuctioneers ?? 0);
     if (numCompanies < requiredCompanies) {
         console.warn(`Builder Warning: numCompanies (${numCompanies}) is less than required for users (${requiredCompanies}). Increasing numCompanies.`);
         numCompanies = requiredCompanies;
     }


    // --- Generate Companies ---
    store.companies = Array.from({ length: numCompanies }, (_, i) => {
        const id = i + 1;
        const override = config.companyOverrides?.find(o => o.company_id === id.toString()) ?? config.companyOverrides?.[i] ?? {};
        return createTest__CompanyElement(id, override);
    });

    // --- Generate Users (Traders and Auctioneers) ---
    let userIdCounter = 1;
    const traders: UserElement[] = [];
    const auctioneers: UserElement[] = [];

    for (let i = 0; i < (numTraders ?? 0); i++) {
        const companyIndex = i % store.companies.length; // Assign companies round-robin
        const company = store.companies[companyIndex];
        const userId = userIdCounter++;
        const override = config.userOverrides?.find(o => o.user_id === userId.toString()) ?? config.userOverrides?.[i] ?? {};
        traders.push(createTest__UserElement(userId, company, { role: AuUserRole.TRADER, ...override }));
    }

    for (let i = 0; i < (numAuctioneers ?? 0); i++) {
        // Assign auctioneers to later companies if possible, or round-robin
        const companyIndex = (numTraders + i) % store.companies.length;
        const company = store.companies[companyIndex];
        const userId = userIdCounter++;
        const override = config.userOverrides?.find(o => o.user_id === userId.toString()) ?? config.userOverrides?.[numTraders + i] ?? {};
         auctioneers.push(createTest__UserElement(userId, company, { role: AuUserRole.AUCTIONEER, ...override }));
    }
    store.users = [...traders, ...auctioneers];

    // --- Generate Session User ---
    const loggedInUser = store.users.find(u => u.user_id === config.loggedInUserId)
        ?? (config.scenario === 'trader_logged_in' ? traders[0] : null)
        ?? (config.scenario === 'auctioneer_setup' ? auctioneers[0] : null)
        ?? (config.scenario === 'auction_in_progress' && auctioneers.length > 0 ? auctioneers[0] : null) // Default to auctioneer if in progress
        ?? (config.scenario === 'auction_closed' && auctioneers.length > 0 ? auctioneers[0] : null)
        ?? store.users[0]; // Fallback to first user

    if (loggedInUser) {
        loggedInUser.current_auction_id = activeAuctionId; // Ensure logged-in user is looking at the active auction
        store.session_user = createTest__SessionUserValue(loggedInUser, {
             current_auction_id: activeAuctionId, // Ensure session reflects active auction
             ...(config.sessionUserOverrides ?? {}) // Apply overrides
         });
    } else {
         store.session_user = createTest__SessionUserValue_LoggedOut();
    }

    // --- Generate Time ---
    store.time = createTest__TimeValue(config.timeOverride ?? {});

    // --- Generate Auction Rows ---
    const numGenericAuctions = config.numGenericAuctions ?? (store.companies.length > 0 ? 1 : 0); // Default to 1 if companies exist
    store.auction_rows = [];
    if (numGenericAuctions > 0 || config.auctionRowOverrides?.some(o => o.auction_id === activeAuctionId)) { // Ensure active auction row exists if needed
        // Ensure the active auction exists in the rows
        //const activeAuctionExists = config.auctionRowOverrides?.some(o => o.auction_id === activeAuctionId);
        let activeAuctionRowOverride = config.auctionRowOverrides?.find(o => o.auction_id === activeAuctionId) ?? {};

        if (!store.auction_rows.some(r => r.auction_id === activeAuctionId)) {
             store.auction_rows.push(createTest__AuctionRowElement(activeAuctionId, {
                 // Potentially set state based on scenario
                 isClosed: config.scenario === 'auction_closed',
                 common_state_text: scenarioState.toString(), // Reflect scenario state text (adjust as needed)
                 ...activeAuctionRowOverride // Apply specific overrides for the active auction row
             }));
        }
         // Add other generic auctions if needed, applying overrides
         for (let i = 0; i < numGenericAuctions; i++) {
             const id = `auction-${i + 2}`; // Start from auction-2 if active is auction-1
             if (id === activeAuctionId) continue; // Already added active one
             const override = config.auctionRowOverrides?.find(o => o.auction_id === id) ?? config.auctionRowOverrides?.[i] ?? {};
             if (!store.auction_rows.some(r => r.auction_id === id)) { // Avoid duplicates
                 store.auction_rows.push(createTest__AuctionRowElement(id, override));
             }
         }
         // Apply overrides to existing rows if needed (e.g., if activeAuctionExists was true but row wasn't added above)
         config.auctionRowOverrides?.forEach(override => {
             const index = store.auction_rows.findIndex(r => r.auction_id === override.auction_id);
             if (index !== -1) {
                 // Merge override into existing row (simple merge)
                 store.auction_rows[index] = { ...store.auction_rows[index], ...override };
             } else if (override.auction_id && override.auction_id !== activeAuctionId) {
                 // Add override if it specifies an ID not already present and not the active one (already handled)
                  store.auction_rows.push(createTest__AuctionRowElement(override.auction_id, override));
             }
         });

    }


    // --- Generate DeAuction (Only if active auction is selected and needed) ---
    if (store.session_user?.current_auction_id === activeAuctionId && store.companies.length > 0) {
        const companiesForThisAuction = store.companies; // Or filter based on auction setup if more complex
        const usersForThisAuction = store.users;       // Or filter similarly

        // Determine max round based on config or scenario
        const maxRoundForDeAuction = config.deAuctionConfig?.maxRound ?? scenarioMaxRound;

        // Prepare base overrides from scenario state
        const deAuctionBaseOverrides: Partial<DeAuctionValue> = {
            // common_status: { common_state: scenarioState, round_number: maxRoundForDeAuction },
             // Potentially set auctioneer_status based on scenarioState too
        };
         // Deep merge base overrides with specific config overrides
         // Using a simple merge here, consider lodash.merge for true deep merge
         const finalDeAuctionOverrides:Partial<DeAuctionValue> = {
             ...deAuctionBaseOverrides,
             ...(config.deAuctionOverrides ?? {}),
             // Manually merge nested objects again if needed
             // common_status: {
             //     ...(deAuctionBaseOverrides.common_status),
             //     ...(config.deAuctionOverrides?.common_status)
             // },
             // ... merge other nested parts like auctioneer_status, blotter etc.
         };


        store.de_auction = createTest__DeAuctionValue(
            activeAuctionId,
            companiesForThisAuction,
            usersForThisAuction,
            maxRoundForDeAuction,
            finalDeAuctionOverrides
        );

        // Set trader-specific info if a trader is logged in
        if (store.session_user && store.session_user.role === AuUserRole.TRADER) {
            const traderCompanyId = store.session_user.company_id;
            // Find relevant history/info for this trader (placeholder logic)
            store.de_auction.trader_info = createTest__DeTraderInfoValue(traderCompanyId, {
                 auction_id: activeAuctionId,
                 company_id: traderCompanyId,
                 round_number: maxRoundForDeAuction,
                 // ... other trader specific fields based on auction state/history
            });
             store.de_auction.trader_history_rows = [ // Example history row
                 createTest__DeTraderHistoryRowElement(1, {
                     auction_id: activeAuctionId,
                     company_id: traderCompanyId,
                     round_number: maxRoundForDeAuction > 0 ? maxRoundForDeAuction.toString() : '0',
                     // ... other history fields
                 })
             ];
        }

    } else {
        store.de_auction = null;
    }


    // --- Generate Counterparty Credits ---
    store.counterparty_credits = [];
    if (config.generateCredits && store.companies.length >= 2) {
        const pairs = config.generateCredits === 'all_pairs'
            ? store.companies.flatMap((c1, i) => store.companies.slice(i + 1).map(c2 => [c1, c2] as [CompanyElement, CompanyElement]))
            : (store.companies.length >= 2 ? [[store.companies[0], store.companies[1]]] : []); // Default: just first pair

        pairs.forEach(([c1, c2]) => {
            // Create credit limits in both directions
            const override1 = config.counterpartyCreditOverrides?.find(o => o.buyer_id === c1.company_id && o.seller_id === c2.company_id);
            const override2 = config.counterpartyCreditOverrides?.find(o => o.buyer_id === c2.company_id && o.seller_id === c1.company_id);
            store.counterparty_credits.push(createTest__CounterpartyCreditElement(c1, c2, override1));
            store.counterparty_credits.push(createTest__CounterpartyCreditElement(c2, c1, override2));
        });
         // Add any specific overrides not matching generated pairs
         config.counterpartyCreditOverrides?.forEach(override => {
             if (!store.counterparty_credits.some(c => c.id === override.id || (c.buyer_id === override.buyer_id && c.seller_id === override.seller_id))) {
                 const buyer = store.companies.find(c => c.company_id === override.buyer_id);
                 const seller = store.companies.find(c => c.company_id === override.seller_id);
                 if (buyer && seller) {
                     store.counterparty_credits.push(createTest__CounterpartyCreditElement(buyer, seller, override));
                 }
             }
         });
    }


    return store;
}
