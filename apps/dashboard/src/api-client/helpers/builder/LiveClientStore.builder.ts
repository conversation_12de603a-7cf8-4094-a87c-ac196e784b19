import { LiveClientStore } from "../../connector/types/generated";
import type { LiveClientStoreConfig } from './LiveClientStore.config';
import { createTestLiveClientStore } from './LiveClientStore.factory';
import type { CompanyElement, UserElement, SessionUserValue, DeAuctionValue, TimeValue, AuctionRowElement, CounterpartyCreditElement } from "../../connector/types/generated";
// import * as _ from 'lodash'; // Optional: for deep merging overrides

export class LiveClientStoreBuilder {
    private _config: LiveClientStoreConfig = {};

    /** Sets a predefined scenario, applying default counts and states. */
    withScenario(scenario: NonNullable<LiveClientStoreConfig['scenario']>): this {
        this._config.scenario = scenario;
        // Reset counts potentially set by previous scenario/calls if needed
        // delete this._config.numCompanies;
        // delete this._config.numTraders;
        // delete this._config.numAuctioneers;
        return this;
    }

    /** Sets the total number of companies to generate. */
    withCompanies(count: number, overrides?: Partial<CompanyElement>[]): this {
        this._config.numCompanies = count;
        this._config.companyOverrides = overrides; // Overwrites previous company overrides
        return this;
    }

    /** Adds or sets company overrides. */
    withCompanyOverrides(overrides: Partial<CompanyElement>[], merge: boolean = false): this {
        if (merge && this._config.companyOverrides) {
            // Basic merge/replace based on company_id or index
             overrides.forEach(override => {
                 const index = this._config.companyOverrides!.findIndex(existing => existing.company_id && existing.company_id === override.company_id);
                 if (index !== -1) {
                     this._config.companyOverrides![index] = { ...this._config.companyOverrides![index], ...override };
                 } else {
                     this._config.companyOverrides!.push(override);
                 }
             });
        } else {
            this._config.companyOverrides = overrides;
        }
        return this;
    }


    /** Sets the number of trader users to generate. */
    withTraders(count: number, overrides?: Partial<UserElement>[]): this {
        this._config.numTraders = count;
        // Store trader-specific overrides separately or merge carefully
        // This simple approach overwrites previous user overrides
        this._config.userOverrides = overrides;
        return this;
    }

     /** Sets the number of auctioneer users to generate. */
    withAuctioneers(count: number, overrides?: Partial<UserElement>[]): this {
        this._config.numAuctioneers = count;
         // Merge auctioneer overrides with existing user overrides carefully
         const existingUserOverrides = this._config.userOverrides ?? [];
         this._config.userOverrides = [...existingUserOverrides, ...(overrides ?? [])]; // Simple append, might need smarter merge
        return this;
    }

     /** Adds or sets user overrides. */
    withUserOverrides(overrides: Partial<UserElement>[], merge: boolean = false): this {
        if (merge && this._config.userOverrides) {
             // Basic merge/replace based on user_id
             overrides.forEach(override => {
                 const index = this._config.userOverrides!.findIndex(existing => existing.user_id && existing.user_id === override.user_id);
                 if (index !== -1) {
                     this._config.userOverrides![index] = { ...this._config.userOverrides![index], ...override };
                 } else {
                     this._config.userOverrides!.push(override);
                 }
             });
        } else {
            this._config.userOverrides = overrides;
        }
        return this;
    }


    /** Sets the user ID to be marked as logged in and optionally overrides session details. */
    withLoggedInUser(userId: string, overrides?: Partial<SessionUserValue>): this {
        this._config.loggedInUserId = userId;
        this._config.sessionUserOverrides = overrides;
        return this;
    }

     /** Sets the active auction ID. */
     withActiveAuction(auctionId: string): this {
         this._config.activeAuctionId = auctionId;
         return this;
     }

    /** Overrides parts of the DeAuctionValue. Use with caution for deep nesting. */
    withDeAuction(overrides: Partial<DeAuctionValue>): this {
        // Simple override assignment. For deep merge, use _.merge or similar in the build/factory.
        // this._config.deAuctionOverrides = _.merge({}, this._config.deAuctionOverrides ?? {}, overrides);
        this._config.deAuctionOverrides = { ...(this._config.deAuctionOverrides ?? {}), ...overrides };
        return this;
    }

     /** Sets configuration for DeAuction generation, like the max round. */
     withDeAuctionConfig(config: NonNullable<LiveClientStoreConfig['deAuctionConfig']>): this {
         this._config.deAuctionConfig = { ...(this._config.deAuctionConfig ?? {}), ...config };
         return this;
     }


    /** Overrides the time value. */
    withTime(overrides: Partial<TimeValue>): this {
        this._config.timeOverride = overrides;
        return this;
    }

     /** Sets the number of generic auction rows to generate (in addition to the active one). */
     withGenericAuctionRows(count: number, overrides?: Partial<AuctionRowElement>[]): this {
         this._config.numGenericAuctions = count;
         this._config.auctionRowOverrides = overrides;
         return this;
     }

     /** Configures counterparty credit generation. */
     withCredits(generate: boolean | 'all_pairs', overrides?: Partial<CounterpartyCreditElement>[]): this {
         this._config.generateCredits = generate;
         this._config.counterpartyCreditOverrides = overrides;
         return this;
     }

    /** Adds or sets auction row overrides. */
    withAuctionRowOverrides(overrides: Partial<AuctionRowElement>[], merge: boolean = false): this {
        if (merge && this._config.auctionRowOverrides) {
            // Basic merge/replace based on auction_id
            overrides.forEach(override => {
                const index = this._config.auctionRowOverrides!.findIndex(existing => existing.auction_id && existing.auction_id === override.auction_id);
                if (index !== -1) {
                    this._config.auctionRowOverrides![index] = { ...this._config.auctionRowOverrides![index], ...override };
                } else {
                    this._config.auctionRowOverrides!.push(override);
                }
            });
        } else {
            this._config.auctionRowOverrides = overrides;
        }
        return this;
    }


    /** Builds the LiveClientStore using the accumulated configuration. */
    build(): LiveClientStore {
        // Call the factory function with the accumulated config
        return createTestLiveClientStore(this._config);
    }
}
