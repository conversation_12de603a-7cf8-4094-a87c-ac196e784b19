// API Client - Unified package containing connector, helpers, and stores

// Export the main connector class and its configuration/state types
export { Connector } from './connector/connector.gemini';
export type { IConnector, ConnectorOptions, ConnectorState } from './connector/connector.gemini';

// Export all types from the generated file
// This makes sure consumers can type check the commands they send
// and the messages they receive.
export * from './connector/types/generated';

// Re-export everything from helpers
export * from './helpers';

// Explicitly export specific helper functions to ensure they're included in build
export {
    createTest__CompanyElement,
    createRandom__CompanyElement
} from './helpers/demo/CompanyElement.helper';
export { createTest__UserElement } from './helpers/demo/UserElement.helper';
export { createTestLiveClientStore } from './helpers/builder/LiveClientStore.factory';

// Re-export everything from stores
export * from './stores';