import type { <PERSON>a, StoryObj } from '@storybook/react';
import { DeRoundTable } from './DeRoundTable';
import {
  createTest__CompanyElement,
  createTest__UserElement,
  createTest__DeBlotter,
  createTest__DeAuctionValue,
  OrderType,
  OrderSubmissionType,
  AuUserRole,
  setMockDomainStore
} from '@/api-client';

const meta: Meta<typeof DeRoundTable> = {
  title: 'Data Grid/DeRoundTable',
  component: DeRoundTable,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Create test data and set up mock store
const setupMockStore = (numCompanies = 5, numRounds = 3) => {
  // Create companies
  const companies = Array.from({ length: numCompanies }, (_, i) =>
    createTest__CompanyElement(i + 1, {
      company_shortname: `COMP${i + 1}`,
      company_longname: `Company ${i + 1} Ltd`
    })
  );

  // Create users
  const users = companies.map((company, i) =>
    createTest__UserElement(i + 1, company, {
      role: AuUserRole.TRADER,
      isOnline: Math.random() > 0.3, // 70% online
      username: `trader${i + 1}`
    })
  );

  // Create blotter with some realistic data
  const blotter = createTest__DeBlotter(companies, numRounds);

  // Add some variety to the round trader data
  blotter.round_traders.forEach((roundTrader, index) => {
    const isEven = index % 2 === 0;

    // Vary order types and quantities
    roundTrader.order_type = isEven ? OrderType.BUY : OrderType.SELL;
    roundTrader.quantity_int = Math.floor(Math.random() * 100) + 10;
    roundTrader.quantity_str = roundTrader.quantity_int.toString();
    roundTrader.match = Math.floor(Math.random() * roundTrader.quantity_int);
    roundTrader.order_submission_type = Math.random() > 0.8 ? OrderSubmissionType.MANDATORY : OrderSubmissionType.DEFAULT;
  });

  // Add some variety to round data
  blotter.rounds.forEach((round) => {
    round.buy_quantity = Math.floor(Math.random() * 500) + 100;
    round.sell_quantity = Math.floor(Math.random() * 500) + 100;
    round.matched = Math.min(round.buy_quantity, round.sell_quantity) * 0.8;
    round.excess_quantity = Math.abs(round.buy_quantity - round.sell_quantity);
    round.excess_side = round.buy_quantity > round.sell_quantity ? OrderType.BUY : OrderType.SELL;
    round.excess_indicator = round.excess_quantity > 100 ? '3+' : round.excess_quantity > 50 ? '2+' : '1+';
  });

  // Create auction value with the blotter
  const deAuction = createTest__DeAuctionValue('test-auction', companies, users, numRounds, {
    blotter
  });

  // Set up mock domain store
  setMockDomainStore({
    companies,
    users,
    de_auction: deAuction
  });

  return { companies, users, blotter };
};

export const Default: Story = {
  render: () => {
    setupMockStore(5, 3);
    return (
      <div className="dark bg-gray-950 p-4 min-h-[500px]">
        <DeRoundTable
          height={400}
          buyMax={100}
          sellMax={100}
          selectedRound={1}
        />
      </div>
    );
  },
};

export const LargeTable: Story = {
  render: () => {
    setupMockStore(10, 6);
    return (
      <div className="dark bg-gray-950 p-4 min-h-[700px]">
        <DeRoundTable
          height={600}
          buyMax={200}
          sellMax={200}
          selectedRound={3}
        />
      </div>
    );
  },
};

export const SmallTable: Story = {
  render: () => {
    setupMockStore(3, 2);
    return (
      <div className="dark bg-gray-950 p-4 min-h-[400px]">
        <DeRoundTable
          height={300}
          buyMax={50}
          sellMax={50}
          selectedRound={2}
        />
      </div>
    );
  },
};

export const Interactive: Story = {
  render: () => {
    setupMockStore(6, 4);
    return (
      <div className="dark bg-gray-950 p-4 min-h-[600px]">
        <DeRoundTable
          height={500}
          buyMax={150}
          sellMax={150}
          selectedRound={2}
          onSelectedRoundChange={(round: number) => {
            console.log('Selected round changed to:', round);
          }}
          onCompanyClick={(companyId: string) => {
            console.log('Company clicked:', companyId);
          }}
        />
      </div>
    );
  },
};

export const FullScale: Story = {
  render: () => {
    setupMockStore(50, 20); // 50 companies × 20 rounds for scrolling demo
    return (
      <div className="dark bg-gray-950 p-4 min-h-[800px]">
        <div style={{ padding: '20px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '24px', fontWeight: 'bold', color: '#fff' }}>
            Full Scale DeRoundTable Demo
          </h2>
          <p style={{ marginBottom: '16px', color: '#ccc' }}>
            50 Traders × 20 Rounds - Demonstrates scrolling, pinned rows/columns, and all features
          </p>
          <DeRoundTable
            height={600}
            buyMax={500}
            sellMax={500}
            selectedRound={10}
            onSelectedRoundChange={(round: number) => {
              console.log('Selected round changed to:', round);
            }}
            onCompanyClick={(companyId: string) => {
              console.log('Company clicked:', companyId);
            }}
            onGridReady={(api: any) => {
              console.log('Grid API ready:', api);
              // Example: Auto-scroll to middle after 2 seconds
              setTimeout(() => {
                api.ensureIndexVisible(25, 'middle');
                api.ensureColumnVisible('round-10', 'middle');
              }, 2000);
            }}
          />
          <div style={{ marginTop: '16px', fontSize: '14px', color: '#ccc' }}>
            <p><strong style={{ color: '#fff' }}>Features demonstrated:</strong></p>
            <ul style={{ marginLeft: '20px', marginTop: '8px' }}>
              <li>Vertical scrolling (50 trader rows)</li>
              <li>Horizontal scrolling (20 rounds)</li>
              <li>Pinned price row (top) and totals (bottom)</li>
              <li>Pinned company names column (left)</li>
              <li>Round selection (click headers)</li>
              <li>Company selection (click names)</li>
              <li>Auto-scroll demonstration (after 2 seconds)</li>
            </ul>
          </div>
        </div>
      </div>
    );
  },
};
