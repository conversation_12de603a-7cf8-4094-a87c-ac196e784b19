import type {
  DeRoundElement,
  DeRoundTraderElement,
  DeTraderElement
} from '@/api-client';

export type DeRoundTableSortBy = {
  title: string;
  field: 'quantity_str' | 'shortname';
  sort_direction: 'asc' | 'desc';
};

export type DeRoundTableCellParams = {
  onRoundClick: () => void;
  onTradersClick: () => void;
  getSortBy: () => DeRoundTableSortBy;
};

export type DeRoundTableConstraintsParams = {
  rounds: DeRoundElement[];
};

export type DeRoundTableAgRowType =
  | 'PRICE'
  | 'TRADER'
  | 'FOOTER'
  | 'EXCESS_DEMAND'
  | 'BLANK';

export class DeBlotterRowAg {
  readonly id: string;
  readonly rowType: DeRoundTableAgRowType;
  readonly trader: DeTraderElement | null;
  readonly cells: DeRoundTraderElement[];
  readonly selected_round_trader: DeRoundTraderElement | null;

  constructor(options: {
    id: string;
    rowType: DeRoundTableAgRowType;
    trader: DeTraderElement | null;
    cells: DeRoundTraderElement[];
    selected_round: number | null;
  }) {
    this.id = options.id;
    this.trader = options.trader;
    this.rowType = options.rowType;
    this.cells = options.cells;

    if (options.selected_round && options.selected_round > 0 && options.selected_round <= options.cells.length) {
      this.selected_round_trader = options.cells[options.selected_round - 1];
    } else {
      this.selected_round_trader = null;
    }
  }
}

export function exists(value: any): boolean {
  return !(value === null || value === undefined || value === '');
}

export function addOrRemoveCssClass(
  element: HTMLElement,
  className: string,
  addOrRemove: boolean
) {
  if (addOrRemove) {
    addCssClass(element, className);
  } else {
    removeCssClass(element, className);
  }
}

export function removeCssClass(element: HTMLElement, className: string) {
  if (element.classList) {
    element.classList.remove(className);
  } else {
    if (element.className && element.className.length > 0) {
      const cssClasses = element.className.split(' ');
      if (cssClasses.indexOf(className) >= 0) {
        // remove all instances of the item, not just the first, in case it's in more than once
        while (cssClasses.indexOf(className) >= 0) {
          cssClasses.splice(cssClasses.indexOf(className), 1);
        }
        element.className = cssClasses.join(' ');
      }
    }
  }
}

export function addCssClass(element: HTMLElement, className: string) {
  if (!className || className.length === 0) {
    return;
  }
  if (className.indexOf(' ') >= 0) {
    className.split(' ').forEach(value => addCssClass(element, value));
    return;
  }
  if (element.classList) {
    element.classList.add(className);
  } else {
    if (element.className && element.className.length > 0) {
      const cssClasses = element.className.split(' ');
      if (cssClasses.indexOf(className) < 0) {
        cssClasses.push(className);
        element.className = cssClasses.join(' ');
      }
    } else {
      element.className = className;
    }
  }
}

// Helper function to get column ID for a round
export const getColId = (round: number) => `round-${round}`;

// Sort options for the table
export const sortByOptions: DeRoundTableSortBy[] = [
  { title: 'Name', field: 'shortname', sort_direction: 'asc' },
  { title: 'Qty', field: 'quantity_str', sort_direction: 'desc' },
];

// Helper function to get round trader elements for a company sorted by round
export const roundTraderElementsForCompanySortedByRound = (
  roundTraderElements: DeRoundTraderElement[],
  companyId: string
): DeRoundTraderElement[] =>
  roundTraderElements
    .filter(cell => cell.cid === companyId)
    .sort((a, b) => a.round - b.round);
