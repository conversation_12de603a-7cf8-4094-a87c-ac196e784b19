import React, { useMemo, useCallback, createContext, useContext, useState } from 'react';
import type { ColDef, ICellRendererParams, GridOptions } from 'ag-grid-community';
import { BaseAgGrid } from '../BaseAgGrid';
import type {
  // DeBlotter, // TODO: Use when implementing blotter functionality
  DeRoundElement,
  DeTraderElement,
  // UserElement, // TODO: Use when implementing user functionality
  DeRoundTraderElement,
  OrderSubmissionType,
  OrderType,
  PriceDirection
} from '@/api-client';
import { getDomainStore } from '@/api-client';
import {
  DeBlotterRowAg,
  DeRoundTableSortBy,
  DeRoundTableCellParams,
  getColId,
  sortByOptions,
  roundTraderElementsForCompanySortedByRound
} from './de-round-table-helpers';

// ========================= BLINK CONTEXT =========================

interface BlinkContextType {
  blinkingCells: Set<string>;
  blinkingTraders: Set<string>;
  addBlinkingCell: (cellId: string) => void;
  addBlinkingTrader: (traderId: string) => void;
  suppressBlinks: boolean;
  setSuppressBlinks: (suppress: boolean) => void;
}

const BlinkContext = createContext<BlinkContextType | null>(null);

const BlinkProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [blinkingCells, setBlinkingCells] = useState<Set<string>>(new Set());
  const [blinkingTraders, setBlinkingTraders] = useState<Set<string>>(new Set());
  const [suppressBlinks, setSuppressBlinks] = useState(true); // Suppress on initial load

  const addBlinkingCell = useCallback((cellId: string) => {
    if (suppressBlinks) return;
    setBlinkingCells(prev => new Set(prev).add(cellId));
    setTimeout(() => {
      setBlinkingCells(prev => {
        const newSet = new Set(prev);
        newSet.delete(cellId);
        return newSet;
      });
    }, 200); // 200ms blink duration
  }, [suppressBlinks]);

  const addBlinkingTrader = useCallback((traderId: string) => {
    if (suppressBlinks) return;
    setBlinkingTraders(prev => new Set(prev).add(traderId));
    setTimeout(() => {
      setBlinkingTraders(prev => {
        const newSet = new Set(prev);
        newSet.delete(traderId);
        return newSet;
      });
    }, 200); // 200ms blink duration
  }, [suppressBlinks]);

  return (
    <BlinkContext.Provider value={{
      blinkingCells,
      blinkingTraders,
      addBlinkingCell,
      addBlinkingTrader,
      suppressBlinks,
      setSuppressBlinks
    }}>
      {children}
    </BlinkContext.Provider>
  );
};

const useBlinkContext = () => {
  const context = useContext(BlinkContext);
  if (!context) {
    throw new Error('useBlinkContext must be used within BlinkProvider');
  }
  return context;
};

// ========================= CELL COMPONENTS =========================

// Header Cell Component
const DeRoundTableHeaderCell: React.FC<ICellRendererParams> = (params) => {
  const displayName = params?.column?.getColDef()?.headerName || '';
  const cellParams: DeRoundTableCellParams = params?.column?.getColDef()?.cellRendererParams;
  const sortParams: DeRoundTableSortBy = cellParams?.getSortBy();
  const isTraderColumn = params?.column?.getColDef()?.headerName === 'Traders';

  const handleClick = () => {
    if (isTraderColumn) {
      cellParams?.onTradersClick();
      return;
    }
    cellParams?.onRoundClick();
  };

  return (
    <div
      onClick={handleClick}
      style={{
        display: 'flex',
        cursor: 'pointer',
        justifyContent: 'space-around',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        padding: '4px'
      }}
    >
      {displayName}
      {isTraderColumn && sortParams && (
        <div style={{ marginLeft: '4px', fontSize: '12px' }}>
          {sortParams.title}
          <span style={{ marginLeft: '2px' }}>
            {sortParams.sort_direction === 'asc' ? '↓' : '↑'}
          </span>
        </div>
      )}
    </div>
  );
};

// Simple price formatter component
const PriceFormatter: React.FC<{
  price: string;
  priceDirection: PriceDirection | null;
}> = ({ price, priceDirection }) => (
  <div style={{ textAlign: 'center', fontSize: '14px' }}>
    {priceDirection && (
      <span style={{ marginRight: '4px' }}>
        {priceDirection === 'UP' ? '↑' : '↓'}
      </span>
    )}
    <span style={{ fontWeight: 'bold' }}>{price}</span>
  </div>
);

// Simple buy/sell bar component with blink support
const BuySellVBar: React.FC<{
  width: number;
  height: number;
  buyMax: number;
  sellMax: number;
  orderQuantityInt: number;
  orderQuantityStr: string;
  orderType: OrderType;
  orderSubmissionType: OrderSubmissionType;
  cellId?: string; // For blink tracking
}> = ({
  width,
  height,
  buyMax,
  sellMax,
  orderQuantityInt,
  orderQuantityStr,
  orderType,
  orderSubmissionType,
  cellId
}) => {
  const { blinkingCells } = useBlinkContext();
  const isBuy = orderType === 'BUY';
  const isSell = orderType === 'SELL';
  const maxValue = Math.max(buyMax, sellMax);
  const barWidth = maxValue > 0 ? (Math.abs(orderQuantityInt) / maxValue) * width : 0;
  const isBlinking = cellId && blinkingCells.has(cellId);

  return (
    <div style={{
      width,
      height,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: isBlinking ? '#FFFF99' : 'transparent', // Light yellow blink
      transition: isBlinking ? 'none' : 'background-color 0.2s ease-out'
    }}>
      {(isBuy || isSell) && (
        <div
          style={{
            position: 'absolute',
            left: isBuy ? 0 : width - barWidth,
            width: barWidth,
            height: height - 2,
            backgroundColor: isBuy ? '#4CAF50' : '#F44336',
            opacity: 0.7
          }}
        />
      )}
      <span style={{
        fontSize: '11px',
        zIndex: 1,
        color: 'white',
        fontWeight: orderSubmissionType === 'MANDATORY' ? 'bold' : 'normal'
      }}>
        {orderQuantityStr}
      </span>
    </div>
  );
};

// Simple blinker component for excess demand
const Blinker: React.FC<{
  value: string;
  background?: boolean;
}> = ({ value, background }) => (
  <div style={{
    width: '100%',
    textAlign: 'center',
    color: 'yellow',
    backgroundColor: background ? 'rgba(255, 255, 0, 0.1)' : 'transparent'
  }}>
    {value}
  </div>
);

// Simple round total cell component
const DeRoundTotalCell: React.FC<{
  round: DeRoundElement;
}> = ({ round }) => (
  <div style={{
    fontSize: '10px',
    textAlign: 'center',
    padding: '2px'
  }}>
    <div>B: {round.buy_quantity}</div>
    <div>S: {round.sell_quantity}</div>
    <div>M: {round.matched}</div>
  </div>
);

// Simple online icon component with blink support
const TraderOnlineIcon: React.FC<{
  isOnline: boolean;
  traderId?: string;
}> = ({ isOnline, traderId }) => {
  const { blinkingTraders } = useBlinkContext();
  const isBlinking = traderId && blinkingTraders.has(traderId);

  return (
    <span
      style={{
        display: 'inline-block',
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: isBlinking ? '#FFFF99' : (isOnline ? '#4CAF50' : '#F44336'),
        marginRight: '4px',
        transition: isBlinking ? 'none' : 'background-color 0.2s ease-out'
      }}
    />
  );
};

// Column Cell Component (for trader names)
const DeRoundTableColumnCell: React.FC<ICellRendererParams> = (params) => {
  const row: DeBlotterRowAg = params?.data;

  if (!row) return <div />;

  const isTraderCell = !!row.trader;

  if (!isTraderCell) {
    // For non-trader rows (PRICE, FOOTER, etc.), show appropriate labels
    switch (row.rowType) {
      case 'PRICE':
        return (
          <div style={{
            padding: '4px',
            fontWeight: 'bold',
            textAlign: 'center'
          }}>
            Price
          </div>
        );
      case 'FOOTER':
        return (
          <div style={{
            padding: '4px',
            fontWeight: 'bold',
            textAlign: 'center',
            fontSize: '12px'
          }}>
            Totals
          </div>
        );
      case 'EXCESS_DEMAND':
        return (
          <div style={{
            padding: '4px',
            fontWeight: 'bold',
            textAlign: 'center',
            fontSize: '12px'
          }}>
            Excess
          </div>
        );
      default:
        return <div />;
    }
  }

  // For trader rows, show trader information
  const trader = row.trader;
  if (!trader) return <div />;

  // Get online users from domain store instead of props
  const domainStore = getDomainStore();
  const isOnline = domainStore.users.some(user => user.company_id === trader.company_id && user.isOnline);

  return (
    <div style={{
      padding: '4px',
      display: 'flex',
      alignItems: 'center',
      fontSize: '12px'
    }}>
      <TraderOnlineIcon isOnline={isOnline} traderId={trader.company_id} />
      <span title={trader.shortname}>
        {trader.shortname}
      </span>
      {trader.rank && (
        <span style={{
          marginLeft: '4px',
          fontSize: '10px',
          color: '#888'
        }}>
          #{trader.rank}
        </span>
      )}
    </div>
  );
};

// Body Cell Component
const DeRoundTableBodyCell: React.FC<ICellRendererParams> = (params) => {
  const row: DeBlotterRowAg = params?.data;
  const roundNumber = parseInt(params?.column?.getColDef()?.headerName || '0');
  const width = 70;

  if (!row) return <div />;

  const rowType = row.rowType;

  // Get data from domain store
  const domainStore = getDomainStore();
  const blotter = domainStore.de_auction?.blotter;
  const buyMax = 50; // TODO: Get from props or store
  const sellMax = 50; // TODO: Get from props or store
  // const maxValue = 100; // TODO: Calculate from store

  // Get round element from the blotter
  const roundElement: DeRoundElement | null = blotter?.rounds.find((r: DeRoundElement) => r.round_number === roundNumber) || null;

  const cellElement: DeRoundTraderElement | null = row.cells?.[roundNumber - 1] || null;

  if (rowType === 'PRICE' && roundElement) {
    return (
      <PriceFormatter
        price={roundElement.round_price_str}
        priceDirection={roundElement.round_direction}
      />
    );
  }

  if (rowType === 'TRADER' && cellElement) {
    const cellId = `${row.trader?.company_id}-${roundNumber}`;
    return (
      <BuySellVBar
        width={width}
        height={21}
        buyMax={buyMax}
        sellMax={sellMax}
        orderQuantityInt={cellElement.quantity_int}
        orderQuantityStr={cellElement.quantity_str}
        orderType={cellElement.order_type}
        orderSubmissionType={cellElement.order_submission_type}
        cellId={cellId}
      />
    );
  }

  if (rowType === 'EXCESS_DEMAND' && roundElement) {
    return (
      <Blinker
        background
        value={roundElement.excess_indicator}
      />
    );
  }

  if (rowType === 'FOOTER' && roundElement) {
    return (
      <DeRoundTotalCell
        round={roundElement}
      />
    );
  }

  return <div />;
};

export interface DeRoundTableProps {
  height: number;
  buyMax?: number;
  sellMax?: number;
  selectedRound?: number;
  onSelectedRoundChange?: (round: number) => void;
  onCompanyClick?: (companyId: string) => void;
  onGridReady?: (api: any) => void;
}

const DeRoundTableInner: React.FC<DeRoundTableProps> = ({
  height,
  // buyMax = 50, // TODO: Use when implementing buy/sell limits
  // sellMax = 50, // TODO: Use when implementing buy/sell limits
  selectedRound = 1,
  onSelectedRoundChange,
  // onCompanyClick, // TODO: Use when implementing company click handler
  onGridReady
}) => {
  // Get data from domain store instead of props
  const domainStore = getDomainStore();
  const blotter = domainStore.de_auction?.blotter;
  // const onlineUsers = domainStore.users.filter(user => user.isOnline); // TODO: Use when implementing online status

  // Early return if no blotter data
  if (!blotter) {
    return <div>No auction data available</div>;
  }
  const [sortBy, setSortBy] = React.useState<DeRoundTableSortBy>(sortByOptions[0]);

  // Table dimensions
  const tableWidth = 650;
  const bodyCellWidth = 70;
  const rowHeight = 25;
  const footerHeight = 90;

  // Calculate max value for footer display
  // const maxValue = useMemo(() => {
  //   let max = 0;
  //   blotter.rounds.forEach(round => {
  //     const roundMax = Math.max(
  //       round.buy_quantity,
  //       round.sell_quantity,
  //       round.matched
  //     );
  //     if (roundMax > max) {
  //       max = roundMax;
  //     }
  //   });
  //   return max;
  // }, [blotter.rounds]); // TODO: Use when implementing footer calculations

  // Handle round click
  const handleRoundClick = useCallback((roundNumber: number) => {
    onSelectedRoundChange?.(roundNumber);
  }, [onSelectedRoundChange]);

  // Handle traders column click (toggle sort)
  const handleTradersClick = useCallback(() => {
    const newSortBy = sortByOptions.find(option => option !== sortBy) || sortByOptions[0];
    setSortBy(newSortBy);
  }, [sortBy]);

  // Get row height based on row type
  const getRowHeight = useCallback((params: any) => {
    const row: DeBlotterRowAg = params.data;
    return row?.rowType === 'FOOTER' ? footerHeight : rowHeight;
  }, [footerHeight, rowHeight]);

  // Generate column definitions
  const columnDefs = useMemo((): ColDef[] => {
    const cols: ColDef[] = [];

    // First column: Traders
    cols.push({
      headerName: 'Traders',
      pinned: 'left',
      width: 140,
      cellStyle: { padding: 0 },
      headerComponent: DeRoundTableHeaderCell,
      cellRenderer: DeRoundTableColumnCell,
      cellRendererParams: {
        onRoundClick: () => {},
        onTradersClick: handleTradersClick,
        getSortBy: () => sortBy
      } as DeRoundTableCellParams
    });

    // Dynamic columns for each round
    blotter.rounds.forEach((roundElement: DeRoundElement) => {
      cols.push({
        colId: getColId(roundElement.round_number),
        headerName: roundElement.round_number.toString(),
        cellStyle: { padding: 0, margin: 0 },
        width: bodyCellWidth,
        headerClass: selectedRound === roundElement.round_number ? 'de-round-table-col--selected' : '',
        headerComponent: DeRoundTableHeaderCell,
        cellRenderer: DeRoundTableBodyCell,
        cellRendererParams: {
          onRoundClick: () => handleRoundClick(roundElement.round_number),
          onTradersClick: () => {},
          getSortBy: () => sortBy
        } as DeRoundTableCellParams
      });
    });

    // Add empty columns if needed (to maintain consistent width)
    const emptyColCount = 6 - blotter.rounds.length;
    if (emptyColCount > 0) {
      for (let i = 0; i < emptyColCount; i++) {
        cols.push({
          colId: `empty-col.${i}`,
          headerName: '',
          cellStyle: { padding: 0, margin: 0 },
          width: bodyCellWidth
        });
      }
    }

    return cols;
  }, [blotter.rounds, selectedRound, sortBy, bodyCellWidth, handleRoundClick, handleTradersClick]);

  // Generate pinned top row data (prices)
  const pinnedTopRowData = useMemo((): DeBlotterRowAg[] => {
    return [
      new DeBlotterRowAg({
        id: 'PRICE',
        rowType: 'PRICE',
        trader: null,
        cells: [],
        selected_round: null
      })
    ];
  }, []);

  // Generate pinned bottom row data (totals)
  const pinnedBottomRowData = useMemo((): DeBlotterRowAg[] => {
    return [
      new DeBlotterRowAg({
        id: 'FOOTER',
        rowType: 'FOOTER',
        trader: null,
        cells: [],
        selected_round: null
      })
    ];
  }, []);

  // Generate main row data (traders)
  const rowData = useMemo((): DeBlotterRowAg[] => {
    let rows = blotter.traders.map((trader: DeTraderElement) =>
      new DeBlotterRowAg({
        id: `TRADER.${trader.company_id}`,
        rowType: 'TRADER',
        trader: trader,
        cells: roundTraderElementsForCompanySortedByRound(
          blotter.round_traders,
          trader.company_id
        ),
        selected_round: selectedRound
      })
    );

    // Sort rows based on current sort criteria
    rows.sort((a, b) => {
      if (!a.trader || !b.trader) return 0;

      let aValue: string | number;
      let bValue: string | number;

      if (sortBy.field === 'shortname') {
        aValue = a.trader.shortname;
        bValue = b.trader.shortname;
      } else {
        // For quantity_str, we need to get the selected round trader
        const aRoundTrader = a.selected_round_trader;
        const bRoundTrader = b.selected_round_trader;
        aValue = aRoundTrader?.quantity_str || '';
        bValue = bRoundTrader?.quantity_str || '';
      }

      if (sortBy.sort_direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return rows;
  }, [blotter.traders, blotter.round_traders, selectedRound, sortBy]);

  // Grid options for BaseAgGrid
  const gridOptions: GridOptions = {
    // Row data
    getRowId: (params) => params.data.id,

    // Grid behavior
    animateRows: false,
    suppressRowClickSelection: true,
    suppressContextMenu: true,
    suppressLoadingOverlay: true,
    suppressMovableColumns: true,
    suppressNoRowsOverlay: true,
    alwaysShowVerticalScroll: true,
  };

  return (
    <BaseAgGrid
      columnDefs={columnDefs}
      pinnedBottomRowData={pinnedBottomRowData}
      pinnedTopRowData={pinnedTopRowData}
      rowData={rowData}
      width={tableWidth}
      height={height}
      getRowHeight={getRowHeight}
      suppressAutoSize={true}
      autoRefresh={false}
      gridOptions={gridOptions}
      className="de-round-table"
      onGridReady={(event) => onGridReady?.(event.api)}
    />
  );
};

// Wrapped component with BlinkProvider
export const DeRoundTable: React.FC<DeRoundTableProps> = (props) => {
  return (
    <BlinkProvider>
      <DeRoundTableInner {...props} />
    </BlinkProvider>
  );
};

// Export the blink context for external use
export { useBlinkContext, BlinkProvider };
