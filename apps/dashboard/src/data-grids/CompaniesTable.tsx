import type { ColDef } from 'ag-grid-community';
import { BaseAgGrid } from './BaseAgGrid';
import type { CompanyElement } from '@/api-client';

// Test data for development
const defaultCompanies: CompanyElement[] = [
  {
    id: '1',
    company_id: 'C1',
    company_shortname: 'ACME',
    company_longname: 'ACME Corporation',
  },
  {
    id: '2',
    company_id: 'C2',
    company_shortname: 'TechCorp',
    company_longname: 'TechCorp Inc.',
  },
  {
    id: '3',
    company_id: 'C3',
    company_shortname: 'StartupX',
    company_longname: 'StartupX Ltd.',
  },
];

interface CompaniesTableProps {
  companies?: CompanyElement[];
  onCompanySelect?: (company: CompanyElement) => void;
  height: number | string;
  width: number | string;
}

export function CompaniesTable({
  companies = defaultCompanies,
  onCompanySelect,
  height,
  width,
}: CompaniesTableProps) {
  const columnDefs: ColDef<CompanyElement>[] = [
    {
      field: 'company_shortname',
      headerName: 'Short Name',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'company_longname',
      headerName: 'Long Name',
      sortable: true,
      filter: true,
      flex: 2,
    },
    {
      field: 'company_id',
      headerName: 'Company ID',
      sortable: true,
      filter: true,
      flex: 1,
    },
  ];

  return (
    <BaseAgGrid<CompanyElement>
      rowData={companies}
      columnDefs={columnDefs}
      enableSelection={true}
      onSelectionChanged={(event) => {
        const selectedCompany = event.api.getSelectedRows()[0];
        if (selectedCompany && onCompanySelect) {
          onCompanySelect(selectedCompany);
        }
      }}
      height={height}
      width={width}
      className="companies-table"
    />
  );
}
