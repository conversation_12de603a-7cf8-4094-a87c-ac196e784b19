import {
  DeTraderHistoryRowElement,
  DeSettingsValue,
  OrderType,
  OrderSubmissionType,
  PriceDirection
} from '@/api-client';

/**
 * Helper functions for DeTraderHistoryTable component
 */

// Color utilities for price direction
export const getPriceColor = (priceDirection: PriceDirection | null): string => {
  switch (priceDirection) {
    case PriceDirection.UP:
      return '#4ade80'; // green-400
    case PriceDirection.DOWN:
      return '#f87171'; // red-400
    default:
      return '#d1d5db'; // gray-300
  }
};

// Color utilities for order types
export const getOrderTypeColor = (orderType: OrderType | null): string => {
  switch (orderType) {
    case OrderType.BUY:
      return '#3b82f6'; // blue-500
    case OrderType.SELL:
      return '#ef4444'; // red-500
    default:
      return '#6b7280'; // gray-500
  }
};

// Get order type label
export const getOrderTypeLabel = (orderType: OrderType | null): string => {
  switch (orderType) {
    case OrderType.BUY:
      return 'Buy';
    case OrderType.SELL:
      return 'Sell';
    default:
      return '---';
  }
};

// Get excess level color
export const getExcessLevelColor = (excessSide: OrderType | null): string => {
  switch (excessSide) {
    case OrderType.BUY:
      return '#3b82f6'; // blue-500
    case OrderType.SELL:
      return '#ef4444'; // red-500
    default:
      return '#6b7280'; // gray-500
  }
};

// Format price with suffix
export const formatPrice = (price: string, suffix?: string): string => {
  if (!price) return '';
  return suffix ? `${price} ${suffix}` : price;
};

// Create demo data for testing
export const createTest__DeTraderHistoryRowElement = (
  roundNumber: number,
  overrides: Partial<DeTraderHistoryRowElement> = {}
): DeTraderHistoryRowElement => {
  const quantity = Math.floor(Math.random() * 100) - 50; // -50 to +50
  const isReversed = roundNumber > 5;

  return {
    id: `hist-${roundNumber}`,
    auction_id: '1',
    bid_constraints: {
      min_buy_quantity: 0,
      max_buy_quantity: 30,
      min_sell_quantity: 0,
      max_sell_quantity: 50
    },
    company_id: '2',
    excess_level: Math.abs(quantity) > 30 ? '4+' : '2+',
    excess_side: quantity > 0 ? OrderType.SELL : OrderType.BUY,
    order_submission_type: OrderSubmissionType.MANUAL,
    order_submitted_by: 'trader1',
    order_type: quantity > 0 ? OrderType.SELL : OrderType.BUY,
    price_direction: roundNumber === 1 ? PriceDirection.UP : (isReversed ? PriceDirection.DOWN : null),
    price_has_reversed: isReversed,
    price_suffix: 'cpp',
    round_number: roundNumber.toString(),
    round_price: (100 + roundNumber * 0.5).toFixed(3),
    value: '',
    quantity: Math.abs(quantity).toString(),
    ...overrides
  };
};

// Create a sequence of demo history rows
export const createTest__DeTraderHistoryRowSequence = (length: number): DeTraderHistoryRowElement[] => {
  const rows: DeTraderHistoryRowElement[] = [];
  let currentPrice = 101.500;
  const priceIncrement = -0.125;
  const reversalRound = Math.floor(length * 0.7); // Reversal at 70% through

  for (let i = 0; i < length; i++) {
    const roundNumber = i + 1;
    const isAfterReversal = roundNumber > reversalRound;

    // Calculate price
    if (roundNumber === reversalRound + 1) {
      // Price reversal
      currentPrice = currentPrice + Math.abs(priceIncrement) * 2;
    } else if (isAfterReversal) {
      currentPrice = currentPrice + Math.abs(priceIncrement);
    } else {
      currentPrice = currentPrice + priceIncrement;
    }

    // Calculate quantity (decreasing towards reversal, then increasing)
    const distanceFromReversal = Math.abs(roundNumber - reversalRound);
    const baseQuantity = Math.max(5, 40 - distanceFromReversal * 3);
    const quantity = roundNumber % 2 === 0 ? baseQuantity : -baseQuantity;

    rows.push(createTest__DeTraderHistoryRowElement(roundNumber, {
      round_price: currentPrice.toFixed(3),
      quantity: Math.abs(quantity).toString(),
      order_type: quantity > 0 ? OrderType.SELL : OrderType.BUY,
      excess_side: quantity > 0 ? OrderType.SELL : OrderType.BUY,
      price_direction: roundNumber === 1 ? PriceDirection.UP : (isAfterReversal ? PriceDirection.DOWN : null),
      price_has_reversed: isAfterReversal,
      excess_level: Math.abs(quantity) > 30 ? '4+' : Math.abs(quantity) > 20 ? '3+' : '2+',
      order_submitted_by: `trader${(roundNumber % 3) + 1}`
    }));
  }

  return rows;
};

// Create default settings for testing
export const createTest__DeSettingsValue = (): DeSettingsValue => ({
  price_label: 'Price',
  quantity_label: 'Qty',
  // Add other required settings properties as needed
} as DeSettingsValue);

// Column width constants (matching Vue implementation)
export const COLUMN_WIDTHS = {
  ROUND: 45,
  PRICE: 75,
  CONSTRAINTS: 140,
  ORDER: 70,
  VALUE: 84,
  SUBMITTED_BY: 85,
  EXCESS: 70
} as const;

// Total table width
export const TABLE_WIDTH = Object.values(COLUMN_WIDTHS).reduce((sum, width) => sum + width, 0);
