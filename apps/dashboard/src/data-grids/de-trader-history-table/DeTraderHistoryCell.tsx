import React from 'react';
import type { ICellRendererParams } from 'ag-grid-community';
import type { DeTraderHistoryRowElement, DeSettingsValue } from '@/api-client';
import { ConstraintsBar } from '@/widgets/volume-meter/ConstraintsBar';
import {
  getPriceColor,
  getOrderTypeColor,
  getOrderTypeLabel,
  getExcessLevelColor,
  formatPrice
} from './de-trader-history-table-helpers';

export interface DeTraderHistoryCellParams extends ICellRendererParams {
  data: DeTraderHistoryRowElement;
  settings: DeSettingsValue;
}

/**
 * Custom cell renderer for DeTraderHistoryTable
 * Replicates the Vue DeTraderHistoryCell component
 */
export const DeTraderHistoryCell: React.FC<DeTraderHistoryCellParams> = (params) => {
  const { column, data: row } = params;
  const headerName = column?.getColDef()?.headerName || '';

  if (!row) {
    return <div className="de-trader-history-cell" />;
  }

  const renderContent = () => {
    switch (headerName) {
      case 'Round':
        return (
          <div
            className="round-cell"
            style={{
              paddingLeft: '10px',
              paddingRight: '5px',
              textAlign: 'right',
              width: '30px'
            }}
          >
            {row.round_number}
          </div>
        );

      case 'Price':
        return (
          <div
            className="price-cell"
            style={{
              display: 'inline-block',
              paddingRight: '8px',
              textAlign: 'right',
              width: '65px',
              color: getPriceColor(row.price_direction)
            }}
          >
            {formatPrice(row.round_price, row.price_suffix)}
          </div>
        );

      case 'Constraints':
        return (
          <div
            className="constraints-cell"
            style={{
              position: 'relative',
              textAlign: 'center',
              top: '-6px',
              left: '-2px',
              width: '100%'
            }}
          >
            <ConstraintsBar
              width={140}
              height={18}
              constraints={row.bid_constraints}
              orderType={row.order_type}
              orderQuantity={parseInt(row.quantity) || 0}
              tickFontSize={9}
              showLabels={false}
              style={{ left: '-1px', top: '-6px' }}
            />
          </div>
        );

      case 'Order':
        return (
          <div
            className="order-cell"
            style={{
              overflow: 'hidden',
              textAlign: 'right',
              paddingRight: '2px',
              width: '60px',
              color: getOrderTypeColor(row.order_type)
            }}
          >
            <div style={{ display: 'inline-block', width: '35px' }}>
              {getOrderTypeLabel(row.order_type)}
            </div>
            <div style={{ display: 'inline-block', textAlign: 'right', width: '25px' }}>
              {row.quantity}
            </div>
          </div>
        );

      case 'Value':
        return (
          <span
            className="value-cell"
            style={{ color: '#ddd' }}
          >
            {row.value}
          </span>
        );

      case 'Submitted by':
        return (
          <span
            className="username-cell"
            style={{ color: '#ddd' }}
          >
            {row.order_submitted_by}
          </span>
        );

      case 'Excess':
        return (
          <div className="excess-cell">
            <span
              className="excess-level"
              style={{
                display: 'inline-block',
                left: '-5px',
                position: 'relative',
                textAlign: 'right',
                width: '30px',
                color: getExcessLevelColor(row.excess_side)
              }}
            >
              {row.excess_level}
            </span>
            <span
              className="excess-direction"
              style={{
                display: 'inline-block',
                color: getExcessLevelColor(row.excess_side)
              }}
            >
              {row.excess_side}
            </span>
          </div>
        );

      default:
        return <div />;
    }
  };

  return (
    <div
      className="de-trader-history-cell"
      style={{
        fontSize: '13px',
        margin: 0,
        padding: 0,
        width: '100%'
      }}
    >
      {renderContent()}
    </div>
  );
};

export default DeTraderHistoryCell;
