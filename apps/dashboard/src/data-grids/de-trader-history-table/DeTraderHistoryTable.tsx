import { useEffect, useRef, useCallback, useMemo, useImperativeHandle, forwardRef } from 'react';
import type { ColDef, GridOptions, GridApi } from 'ag-grid-community';
import { BaseAgGrid } from '@/data-grids';
import type { DeTraderHistoryRowElement, DeSettingsValue } from '@/api-client';
import { useBlinkContext } from '@/data-grids';
import DeTraderHistoryTableHeader from './DeTraderHistoryTableHeader';
import DeTraderHistoryCell from './DeTraderHistoryCell';
import { COLUMN_WIDTHS, TABLE_WIDTH } from './de-trader-history-table-helpers';

export interface DeTraderHistoryTableProps {
  height: number;
  width?: number;
  historyRows: DeTraderHistoryRowElement[];
  settings: DeSettingsValue;
  className?: string;
  onRowSelect?: (row: DeTraderHistoryRowElement) => void;
}

export interface DeTraderHistoryTableRef {
  scrollToFirst: () => void;
  scrollToLast: () => void;
  scrollToRound: (roundNumber: number) => void;
}

/**
 * DeTraderHistoryTable - React implementation of the Vue DeTraderHistoryTable
 *
 * Displays a history of trading rounds with:
 * - Round numbers
 * - Prices with direction indicators
 * - Constraint visualizations
 * - Order details (type and quantity)
 * - Values and submission info
 * - Excess indicators
 *
 * Features:
 * - Auto-scroll to bottom on new rows
 * - Blink effect on new row additions
 * - Custom cell renderers for each column type
 * - Responsive to settings changes
 * - Sorted by round number (ascending)
 * - Scroll controls via ref
 */
export const DeTraderHistoryTable = forwardRef<DeTraderHistoryTableRef, DeTraderHistoryTableProps>(({
  height,
  width = TABLE_WIDTH,
  historyRows,
  settings,
  className = '',
  onRowSelect
}, ref) => {
  const gridRef = useRef<GridApi | null>(null);
  const { addBlinkingCell } = useBlinkContext();
  const previousRowCountRef = useRef(historyRows.length);

  // Sort history rows by round number (ascending) - last round at bottom
  const sortedHistoryRows = useMemo(() => {
    return [...historyRows].sort((a, b) => {
      const roundA = parseInt(a.round_number) || 0;
      const roundB = parseInt(b.round_number) || 0;
      return roundA - roundB;
    });
  }, [historyRows]);

  // Scroll functions exposed via ref
  const scrollToFirst = useCallback(() => {
    if (gridRef.current && sortedHistoryRows.length > 0) {
      gridRef.current.ensureIndexVisible(0, 'top');
    }
  }, [sortedHistoryRows.length]);

  const scrollToLast = useCallback(() => {
    if (gridRef.current && sortedHistoryRows.length > 0) {
      gridRef.current.ensureIndexVisible(sortedHistoryRows.length - 1, 'bottom');
    }
  }, [sortedHistoryRows.length]);

  const scrollToRound = useCallback((roundNumber: number) => {
    if (gridRef.current && sortedHistoryRows.length > 0) {
      const index = sortedHistoryRows.findIndex(row => parseInt(row.round_number) === roundNumber);
      if (index >= 0) {
        gridRef.current.ensureIndexVisible(index, 'middle');
      }
    }
  }, [sortedHistoryRows]);

  // Expose scroll functions via ref
  useImperativeHandle(ref, () => ({
    scrollToFirst,
    scrollToLast,
    scrollToRound
  }), [scrollToFirst, scrollToLast, scrollToRound]);

  // Column definitions matching the Vue implementation
  const columnDefs: ColDef[] = [
    {
      headerName: 'Round',
      width: COLUMN_WIDTHS.ROUND,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Price',
      width: COLUMN_WIDTHS.PRICE,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Constraints',
      width: COLUMN_WIDTHS.CONSTRAINTS,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Order',
      width: COLUMN_WIDTHS.ORDER,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Value',
      width: COLUMN_WIDTHS.VALUE,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Submitted by',
      width: COLUMN_WIDTHS.SUBMITTED_BY,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    },
    {
      headerName: 'Excess',
      width: COLUMN_WIDTHS.EXCESS,
      headerComponent: DeTraderHistoryTableHeader,
      cellRenderer: DeTraderHistoryCell,
      headerComponentParams: { settings },
      cellRendererParams: { settings }
    }
  ];

  // Grid options
  const gridOptions: GridOptions = {
    suppressHorizontalScroll: true,
    getRowId: (params) => params.data.id,
    suppressColumnMoveAnimation: true,
    defaultColDef: {
      sortable: false // Disable sorting on all columns
    },
    onGridReady: (params) => {
      gridRef.current = params.api;
      // Auto-scroll to bottom on initial load if we have data
      if (sortedHistoryRows.length > 0) {
        setTimeout(() => {
          params.api.ensureIndexVisible(sortedHistoryRows.length - 1, 'bottom');
        }, 100);
      }
    },
    onRowClicked: (event) => {
      if (onRowSelect && event.data) {
        onRowSelect(event.data);
      }
    }
  };

  // Auto-scroll to bottom and trigger blink effect when new rows are added
  const handleNewRows = useCallback(async () => {
    if (!gridRef.current) return;

    const currentRowCount = sortedHistoryRows.length;
    const previousRowCount = previousRowCountRef.current;

    if (currentRowCount > previousRowCount) {
      // Scroll to bottom (last round)
      gridRef.current.ensureIndexVisible(currentRowCount - 1, 'bottom');

      // Wait a bit for the scroll to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Trigger blink effect on the last row (highest round number)
      const lastRowIndex = currentRowCount - 1;
      addBlinkingCell(`trader-history-row-${lastRowIndex}`);
    }

    previousRowCountRef.current = currentRowCount;
  }, [sortedHistoryRows.length, addBlinkingCell]);

  // Effect to handle new rows
  useEffect(() => {
    handleNewRows();
  }, [handleNewRows]);

  return (
    <div className={`de-trader-history-table ${className}`}>
      {/* Table Heading */}
      <div
        className="table-heading"
        style={{
          backgroundColor: '#333',
          borderRadius: '4px',
          textAlign: 'center',
          padding: '8px',
          marginBottom: '8px',
          color: '#fff',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
      >
        Round History
      </div>

      {/* AG Grid Table */}
      <BaseAgGrid
        height={height}
        width={width}
        rowData={sortedHistoryRows}
        columnDefs={columnDefs}
        gridOptions={gridOptions}
        className="de-trader-history-grid"
      />
    </div>
  );
});

// Add display name for debugging
DeTraderHistoryTable.displayName = 'DeTraderHistoryTable';

export default DeTraderHistoryTable;
