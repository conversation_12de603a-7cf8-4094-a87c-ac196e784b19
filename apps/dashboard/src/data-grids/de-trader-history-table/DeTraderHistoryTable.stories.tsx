import type { Meta, StoryObj } from '@storybook/react';
import { useRef, useState } from 'react';
import { DeTraderHistoryTable, type DeTraderHistoryTableRef } from './DeTraderHistoryTable';
import { BlinkProvider } from '@/data-grids';
import { OrderType, PriceDirection } from '@/api-client';
import { Button } from '@/components/ui/button';
import {
  createTest__DeTraderHistoryRowSequence,
  createTest__DeTraderHistoryRowElement,
  createTest__DeSettingsValue
} from './de-trader-history-table-helpers';

const meta: Meta<typeof DeTraderHistoryTable> = {
  title: 'Data Grid/DeTraderHistoryTable',
  component: DeTraderHistoryTable,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <BlinkProvider>
        <div style={{ padding: '20px', backgroundColor: '#1a1a1a' }}>
          <Story />
        </div>
      </BlinkProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof DeTraderHistoryTable>;

// Default story with a sequence of history rows
export const Default: Story = {
  args: {
    height: 400,
    width: 590,
    historyRows: createTest__DeTraderHistoryRowSequence(8),
    settings: createTest__DeSettingsValue(),
  },
};

// Story with many rows to test scrolling
export const ManyRows: Story = {
  args: {
    height: 500,
    width: 590,
    historyRows: createTest__DeTraderHistoryRowSequence(20),
    settings: createTest__DeSettingsValue(),
  },
};

// Story with minimal data
export const MinimalData: Story = {
  args: {
    height: 300,
    width: 590,
    historyRows: createTest__DeTraderHistoryRowSequence(3),
    settings: createTest__DeSettingsValue(),
  },
};

// Story with custom settings
export const CustomSettings: Story = {
  args: {
    height: 400,
    width: 590,
    historyRows: createTest__DeTraderHistoryRowSequence(10),
    settings: {
      price_label: 'USD',
      quantity_label: 'Units',
    } as any,
  },
};

// Story with single row
export const SingleRow: Story = {
  args: {
    height: 200,
    width: 590,
    historyRows: [createTest__DeTraderHistoryRowElement(1)],
    settings: createTest__DeSettingsValue(),
  },
};

// Empty state
export const Empty: Story = {
  args: {
    height: 300,
    width: 590,
    historyRows: [],
    settings: createTest__DeSettingsValue(),
  },
};

// Interactive story with row selection
export const WithSelection: Story = {
  args: {
    height: 400,
    width: 590,
    historyRows: createTest__DeTraderHistoryRowSequence(12),
    settings: createTest__DeSettingsValue(),
    onRowSelect: (row) => {
      console.log('Selected row:', row);
      alert(`Selected round ${row.round_number} - ${row.order_type} ${row.quantity}`);
    },
  },
};

// Story demonstrating different order types and price directions
export const VariedData: Story = {
  args: {
    height: 450,
    width: 590,
    historyRows: [
      createTest__DeTraderHistoryRowElement(1, {
        order_type: OrderType.BUY,
        quantity: '25',
        price_direction: PriceDirection.UP,
        round_price: '101.250',
        excess_level: '2+',
        excess_side: OrderType.BUY
      }),
      createTest__DeTraderHistoryRowElement(2, {
        order_type: OrderType.SELL,
        quantity: '40',
        price_direction: null,
        round_price: '101.125',
        excess_level: '3+',
        excess_side: OrderType.SELL
      }),
      createTest__DeTraderHistoryRowElement(3, {
        order_type: OrderType.BUY,
        quantity: '15',
        price_direction: PriceDirection.DOWN,
        round_price: '100.875',
        excess_level: '1+',
        excess_side: OrderType.BUY,
        price_has_reversed: true
      }),
      createTest__DeTraderHistoryRowElement(4, {
        order_type: OrderType.SELL,
        quantity: '60',
        price_direction: PriceDirection.DOWN,
        round_price: '100.750',
        excess_level: '4+',
        excess_side: OrderType.SELL,
        price_has_reversed: true
      }),
    ],
    settings: createTest__DeSettingsValue(),
  },
};

// Interactive story with scroll controls and add round functionality
export const InteractiveScrolling = {
  render: () => {
    const tableRef = useRef<DeTraderHistoryTableRef>(null);
    const [historyRows, setHistoryRows] = useState(() => createTest__DeTraderHistoryRowSequence(15));
    const settings = createTest__DeSettingsValue();

    const scrollToFirst = () => {
      tableRef.current?.scrollToFirst();
    };

    const scrollToLast = () => {
      tableRef.current?.scrollToLast();
    };

    const addRound = () => {
      const nextRoundNumber = Math.max(...historyRows.map(row => parseInt(row.round_number))) + 1;
      const newRow = createTest__DeTraderHistoryRowElement(nextRoundNumber, {
        round_price: (100 + nextRoundNumber * 0.125).toFixed(3),
        quantity: (Math.floor(Math.random() * 50) + 10).toString(),
        order_type: Math.random() > 0.5 ? OrderType.BUY : OrderType.SELL,
        excess_side: Math.random() > 0.5 ? OrderType.BUY : OrderType.SELL,
        price_direction: Math.random() > 0.7 ? PriceDirection.UP : Math.random() > 0.5 ? PriceDirection.DOWN : null,
        excess_level: ['1+', '2+', '3+', '4+'][Math.floor(Math.random() * 4)],
        order_submitted_by: `trader${(nextRoundNumber % 3) + 1}`
      });

      setHistoryRows(prev => [...prev, newRow]);
    };

    return (
      <div style={{ padding: '20px', backgroundColor: '#1a1a1a' }}>
        <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <Button onClick={scrollToFirst} variant="outline" size="sm">
            Scroll to First Round
          </Button>
          <Button onClick={scrollToLast} variant="outline" size="sm">
            Scroll to Last Round
          </Button>
          <Button onClick={addRound} variant="default" size="sm">
            Add Round (Test Auto-Scroll)
          </Button>
          <div style={{ color: '#ccc', fontSize: '14px', alignSelf: 'center' }}>
            Total Rounds: {historyRows.length}
          </div>
        </div>

        <DeTraderHistoryTable
          ref={tableRef}
          height={400}
          width={590}
          historyRows={historyRows}
          settings={settings}
          onRowSelect={(row) => {
            console.log('Selected row:', row);
            alert(`Selected round ${row.round_number} - ${row.order_type} ${row.quantity}`);
          }}
        />
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive story demonstrating scroll controls and auto-scroll on new round addition. The table is sorted by round number (ascending) with the last round at the bottom.'
      }
    }
  }
};
