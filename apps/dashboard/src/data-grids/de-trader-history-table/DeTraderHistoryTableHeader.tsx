import React from 'react';
import type { ICellRendererParams } from 'ag-grid-community';
import type { DeSettingsValue } from '@/api-client';

export interface DeTraderHistoryTableHeaderParams extends ICellRendererParams {
  settings: DeSettingsValue;
}

/**
 * Custom header component for DeTraderHistoryTable
 * Replicates the Vue DeTraderHistoryTableHeader component
 */
export const DeTraderHistoryTableHeader: React.FC<DeTraderHistoryTableHeaderParams> = (params) => {
  const { column, settings } = params;
  const displayName = column?.getColDef()?.headerName || '';
  
  // Get the top line (main header name)
  const getTopText = (): string => {
    return displayName;
  };

  // Get the bottom line (sub-label with units)
  const getBottomText = (): string => {
    switch (displayName) {
      case 'Round':
        return '';
      case 'Price':
        return `(${settings?.price_label || 'Price'})`;
      case 'Order':
        return `(${settings?.quantity_label || 'Qty'})`;
      case 'Constraints':
        return `(${settings?.quantity_label || 'Qty'})`;
      case 'Excess':
        return '(indicator)';
      case 'Submitted by':
        return '(bidder)';
      case 'Value':
        return '';
      default:
        return '';
    }
  };

  return (
    <div 
      className="de-trader-history-table-header"
      style={{
        fontFamily: '"Helvetica Neue", Helvetica, Arial, sans-serif',
        textAlign: 'center',
        width: '100%',
        whiteSpace: 'normal',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        height: '100%',
        padding: '2px'
      }}
    >
      <div style={{ height: '15px', fontSize: '12px', lineHeight: '15px' }}>
        {getTopText()}
      </div>
      <div style={{ height: '15px', fontSize: '10px', lineHeight: '15px', color: '#999' }}>
        {getBottomText()}
      </div>
    </div>
  );
};

export default DeTraderHistoryTableHeader;
