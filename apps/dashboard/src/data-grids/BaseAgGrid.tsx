import { AgGridReact } from 'ag-grid-react';
import type {
  ColDef,
  GridOptions,
  GridReadyEvent,
  CellClickedEvent,
  SelectionChangedEvent
} from 'ag-grid-community';
import { ModuleRegistry, AllCommunityModule, themeAlpine } from 'ag-grid-community';

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);

export interface BaseAgGridProps<T> {
  // Required props
  rowData: T[];
  columnDefs: ColDef[];
  height: string | number;
  width: string | number;

  // Optional props with defaults
  theme?: typeof themeAlpine;
  className?: string;

  // Grid options
  gridOptions?: GridOptions;

  // Pinned rows (for DeRoundTable support)
  pinnedTopRowData?: T[];
  pinnedBottomRowData?: T[];

  // Row height function (for DeRoundTable support)
  getRowHeight?: (params: any) => number;

  // Auto sizing control
  suppressAutoSize?: boolean;
  autoRefresh?: boolean;

  // Event handlers
  onGridReady?: (event: GridReadyEvent) => void;
  onCellClicked?: (event: CellClickedEvent) => void;
  onSelectionChanged?: (event: SelectionChangedEvent) => void;

  // Common features
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enablePagination?: boolean;
  enableSelection?: boolean;
  enableColumnResize?: boolean;
  enableColumnMove?: boolean;
  enableColumnPin?: boolean;
}

export function BaseAgGrid<T>({
  // Required props
  rowData,
  columnDefs,
  height,
  width,

  // Optional props with defaults
  theme = themeAlpine,
  className = '',

  // Grid options
  gridOptions = {},

  // Pinned rows
  pinnedTopRowData,
  pinnedBottomRowData,

  // Row height function
  getRowHeight,

  // Auto sizing control
  suppressAutoSize = false,
  // autoRefresh = true, // TODO: Implement auto refresh functionality

  // Event handlers
  onGridReady,
  onCellClicked,
  onSelectionChanged,

  // Common features
  enableSorting = true,
  enableFiltering = true,
  enablePagination = false,
  enableColumnResize = true,
  enableColumnMove = true,
  enableColumnPin = true,
}: BaseAgGridProps<T>) {

  // Combine default options with user-provided options
  const defaultGridOptions: GridOptions = {
    // Default column definitions
    defaultColDef: {
      sortable: enableSorting,
      filter: enableFiltering,
      resizable: enableColumnResize,
      suppressMovable: !enableColumnMove,
      lockPinned: !enableColumnPin,
    },

    // Grid features
    suppressMovableColumns: !enableColumnMove,
    suppressPaginationPanel: !enablePagination,
    suppressAutoSize: suppressAutoSize,

    // Row height
    getRowHeight: getRowHeight,

    // Event handlers
    onGridReady,
    onCellClicked,
    onSelectionChanged,
  };

  const finalGridOptions = {
    ...defaultGridOptions,
    ...gridOptions,
  };

  return (
    <div
      className={className}
      style={{ height, width }}
    >
      <AgGridReact
        rowData={rowData}
        columnDefs={columnDefs}
        pinnedTopRowData={pinnedTopRowData}
        pinnedBottomRowData={pinnedBottomRowData}
        theme={theme}
        {...finalGridOptions}
      />
    </div>
  );
}
