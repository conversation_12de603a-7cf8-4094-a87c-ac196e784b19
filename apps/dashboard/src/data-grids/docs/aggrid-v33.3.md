# AG Grid v33.3+ Migration Guide

## Overview

AG Grid v33.3+ introduces significant changes that affect how we implement and style data grids in our application. This document outlines the key changes and how to implement them in our project.

## CSS Import Changes

### Removed CSS Imports

In AG Grid v33.3+, the traditional CSS imports are no longer necessary and should be removed:

```typescript
// REMOVE these imports
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
```

### Using the New Theming API

Instead of CSS imports, themes are imported directly from the JavaScript package:

```typescript
// Import the theme object
import { themeAlpine } from 'ag-grid-community';

// Apply it directly to the component
<AgGridReact
  theme={themeAlpine}
  // other props...
/>
```

### CSS Class Changes

Previously, we needed a wrapper div with a theme class:

```typescript
// Old approach
<div className="ag-theme-alpine" style={{ height, width }}>
  <AgGridReact {...props} />
</div>
```

You can still use the wrapper div for dimensions, but the theme should be passed directly to `AgGridReact`:

```typescript
<div style={{ height, width }}>
  <AgGridReact
    theme={themeAlpine}
    {...props}
  />
</div>
```

## Row Selection Changes

### Deprecated String Format

The string-based rowSelection format is deprecated in AG Grid v33.3+:

```typescript
// DEPRECATED format
rowSelection: enableSelection ? 'multiple' : undefined
```

### New Object Format

Use the new object format instead:

```typescript
// New format
rowSelection: { mode: "singleRow" },
```

## Component Structure Best Practices

### Required Dimensions

AG Grid components should require explicit dimensions:

```typescript
// In component interfaces
interface MyGridProps {
  // Required dimensions
  height: string | number;
  width: string | number;
  // Other props...
}
```

### Implementation Example

```typescript
// BaseAgGrid component
export interface BaseAgGridProps<T> {
  // Required props with explicit dimensions
  rowData: T[];
  columnDefs: ColDef[];
  height: string | number;
  width: string | number;
  
  // Optional props
  theme?: typeof themeAlpine;
  // ...
}

// Usage in other components
<BaseAgGrid
  rowData={data}
  columnDefs={columns}
  height={500}
  width="100%"
  // ...
/>
```

## Theme Customization

You can customize themes using the `withParams()` method:

```typescript
const customTheme = themeAlpine.withParams({
  // TypeScript validated parameters
  accentColor: '#3366FF',
  borderRadius: 4,
  headerHeight: 50,
  rowHeight: 40,
});

// Then use customTheme in your grid
<AgGridReact theme={customTheme} />
```

## Available Themes

AG Grid provides several built-in themes:

- `themeAlpine` - Modern, clean design (light mode)
- `themeAlpineDark` - Dark version of Alpine
- `themeBalham` - Compact, business-like design (light mode)
- `themeBalhamDark` - Dark version of Balham
- `themeMaterial` - Material Design-inspired theme
- `themeQuartz` - New theme with a contemporary look

## Storybook Integration

When using AG Grid components in Storybook:

1. Remove any CSS imports from `.storybook/preview.ts` or `.storybook/preview.js`
2. Import theme objects in story files and pass them to components
3. Make sure to specify required dimensions in story args

Example story:

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { MyDataGrid } from '@repo/data-grid';
import { themeAlpine } from 'ag-grid-community';

const meta: Meta<typeof MyDataGrid> = {
  title: 'Data Grid/MyDataGrid',
  component: MyDataGrid,
};

export default meta;
type Story = StoryObj<typeof MyDataGrid>;

export const Default: Story = {
  args: {
    theme: themeAlpine,
    height: 500,
    width: 800,
    rowData: [...],
    columnDefs: [...],
  },
};
```

## Legacy Theme Support (Optional Temporary Solution)

If you need to temporarily use the legacy theming system:

```typescript
// Mark all grids as using legacy themes
import { provideGlobalGridOptions } from 'ag-grid-community';

provideGlobalGridOptions({
  theme: "legacy",
});

// Then continue to use CSS imports and classes as before
```

## References

- [AG Grid Theming Migration Guide](https://www.ag-grid.com/javascript-data-grid/theming-migration/)
- [AG Grid Theming API Documentation](https://www.ag-grid.com/javascript-data-grid/themes-provided/)
