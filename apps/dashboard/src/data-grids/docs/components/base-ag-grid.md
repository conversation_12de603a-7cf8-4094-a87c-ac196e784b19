# BaseAgGrid Component

This is the foundational AG Grid wrapper component used by other specialized grids in the `@repo/data-grid` package.

## Overview

`BaseAgGrid` provides a common set of configurations, styling, and features that are inherited by components like `UsersTable` and `CompaniesTable`.

Its purpose is to:
- Ensure consistency across all data grids.
- Reduce boilerplate when creating new grid components.
- Centralize common grid logic (e.g., default column options, theming hooks).

## Features (Examples - to be detailed)

- Default column definitions (resizable, sortable, etc.)
- Pre-configured grid options for AU25 styling.
- Support for common AG Grid features like row selection, filtering, etc.
- Customizable via props.

## Props (To be detailed)

```tsx
interface BaseAgGridProps<TData = any> {
  // General AG Grid props
  rowData?: TData[] | null;
  columnDefs: ColDef<TData>[];
  defaultColDef?: ColDef<TData>;
  // Custom props
  height?: string | number;
  width?: string | number;
  // ... other props for pagination, selection, etc.
}
```

## Usage

Typically, you would not use `BaseAgGrid` directly unless creating a new, highly custom grid. Instead, you would use one of the specialized components like `UsersTable` or `CompaniesTable`.

If creating a new grid component:

```tsx
import { BaseAgGrid, type BaseAgGridProps } from '@repo/data-grid';
import type { ColDef } from 'ag-grid-community';

interface MyCustomData {
  id: string;
  name: string;
  value: number;
}

function MyCustomGrid(props: Omit<BaseAgGridProps<MyCustomData>, 'columnDefs'> & { data: MyCustomData[] }) {
  const columnDefs: ColDef<MyCustomData>[] = [
    { field: 'name', headerName: 'Name' },
    { field: 'value', headerName: 'Value' },
  ];

  return <BaseAgGrid<MyCustomData> rowData={props.data} columnDefs={columnDefs} {...props} />;
}
```

(This documentation is a placeholder and needs to be expanded with actual details about `BaseAgGrid`'s implementation and props.)
