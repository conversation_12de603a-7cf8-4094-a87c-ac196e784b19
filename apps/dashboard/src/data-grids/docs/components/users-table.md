# UsersTable Component

A specialized data grid component for displaying and managing user data in the AU25 system.

## Features

- Displays user information including username, email, company, role, and online status
- Supports sorting and filtering on all columns
- Row selection with callback for user selection events
- Consistent styling with the AU25 design system
- Built on top of BaseAgGrid for common functionality

## Usage

```tsx
import { UsersTable } from '@acme/data-grid';
import type { UserElement } from '@repo/api-client';

function MyComponent() {
  const handleUserSelect = (user: UserElement) => {
    console.log('Selected user:', user);
  };

  return (
    <UsersTable
      users={myUsers}
      onUserSelect={handleUserSelect}
    />
  );
}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| users | UserElement[] | No | Array of user data to display. Defaults to sample data if not provided. |
| onUserSelect | (user: UserElement) => void | No | Callback function when a user is selected |

## Data Structure

The component expects data in the `UserElement` format from `@repo/api-client`:

```typescript
interface UserElement {
  username: string;
  email: string;
  company_shortname: string;
  role: AuUserRole;
  isOnline: boolean;
  // ... other fields
}
```

## Styling

The component uses the Alpine theme from AG Grid and follows the AU25 design system guidelines. The table has a fixed height of 500px by default.

## Future Enhancements

- [ ] Add support for custom column configurations
- [ ] Implement row actions (edit, delete, etc.)
- [ ] Add support for bulk selection
- [ ] Add support for custom filters 