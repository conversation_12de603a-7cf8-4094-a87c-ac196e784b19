# CompaniesTable Component

A specialized data grid component for displaying company information in the AU25 system.

## Features

- Displays company information including short name, long name, and ID.
- Supports sorting and filtering on all columns.
- Consistent styling with the AU25 design system.
- Built on top of `BaseAgGrid` for common functionality.

## Usage

```tsx
import { CompaniesTable } from '@repo/data-grid'; // Assuming this is the correct import path
import type { CompanyElement } from '@repo/api-client';

function MyCompanyDisplay() {
  // Example: Fetch or define your company data
  const myCompanies: CompanyElement[] = [
    { company_id: 1, company_shortname: 'ACMECorp', company_longname: 'ACME Corporation Ltd.' },
    { company_id: 2, company_shortname: 'TechSol', company_longname: 'Technology Solutions Inc.' },
  ];

  return (
    <CompaniesTable
      companies={myCompanies}
      height="500px"
      width="100%"
    />
  );
}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `companies` | `CompanyElement[]` | No | Array of company data to display. Defaults to sample data if not provided. |
| `height` | `string` \| `number` | Yes | The height of the grid (e.g., '500px', 500). |
| `width` | `string` \| `number` | Yes | The width of the grid (e.g., '100%', 800). |
| `onCompanySelect` | `(company: CompanyElement) => void` | No | Callback function triggered when a company row is selected. (Note: This prop is not yet implemented in the current CompaniesTable component but can be added if needed, similar to UsersTable's `onUserSelect`.) |

## Data Structure

The component expects data in the `CompanyElement` format from `@repo/api-client`:

```typescript
interface CompanyElement {
  company_id: number;
  company_shortname: string;
  company_longname?: string;
  // ... other potential fields related to a company
}
```

See the `@repo/api-client` package for the full `CompanyElement` definition.
