# @repo/data-grid

This package provides a set of reusable data grid components built using AG Grid for the AU25 Turbo React Design system.

## Overview

The data grid package offers a suite of reusable table components built upon AG Grid, tailored for the AU25 system. These components aim to provide consistent data display and interaction patterns.

## Components

Detailed documentation for each component:

-   [BaseAgGrid](./docs/components/base-ag-grid.md) - The foundational wrapper for all grid components.
-   [UsersTable](./docs/components/users-table.md) - A grid for displaying user information.
-   [CompaniesTable](./docs/components/companies-table.md) - A grid for displaying company information.
-   [MyGrid2](./docs/components/my-grid-2.md) - (Placeholder for MyGrid2 documentation)

## AG Grid Specifics

Information related to the AG Grid library usage within this package:

-   [AG Grid Notes (v33.3)](./docs/aggrid-v33.3.md) - General notes, setup, and configuration for AG Grid.
-   [AG Grid Theming (v33.3)](./docs/aggrid-theming-v33.3.md) - Guidelines and details on theming AG Grid components in this project.

## Development

Information for developers working on or contributing to this package:

-   [Development Guide](./docs/development.md) - Setup, contribution guidelines, and best practices.

## Storybook

Components from this package, including `UsersTable` and `CompaniesTable`, can be viewed and tested in isolation within the `docs` application's Storybook.

To run Storybook locally:

```bash
# From the project root
just storybook-dev
```

Then navigate to the appropriate story under the "Data Grid" section.
