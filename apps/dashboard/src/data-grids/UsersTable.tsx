import type { ColDef } from 'ag-grid-community';
import { BaseAgGrid } from './BaseAgGrid';
import type { UserElement } from '@/api-client';
import { AuUserRole } from '@/api-client';

// Test data for development
const defaultUsers: UserElement[] = [
  {
    username: 'john.doe',
    email: '<EMAIL>',
    company_shortname: 'ACME',
    role: AuUserRole.TRADER,
    isOnline: true,
    company_id: '1',
    company_longname: 'ACME Corporation',
    current_auction_id: null,
    has_connection_problem: false,
    id: '1',
    isAuctioneer: false,
    isObserver: false,
    isTester: false,
    password: '',
    phone: '',
    socket_state: null,
    socket_state_last_closed: null,
    termination_reason: null,
    user_id: '1'
  },
  {
    username: 'jane.smith',
    email: '<EMAIL>',
    company_shortname: 'TechCorp',
    role: AuUserRole.AUCTIONEER,
    isOnline: false,
    company_id: '2',
    company_longname: 'TechCorp Inc',
    current_auction_id: null,
    has_connection_problem: false,
    id: '2',
    isAuctioneer: true,
    isObserver: false,
    isTester: false,
    password: '',
    phone: '',
    socket_state: null,
    socket_state_last_closed: null,
    termination_reason: null,
    user_id: '2'
  },
  {
    username: 'bob.wilson',
    email: '<EMAIL>',
    company_shortname: 'StartupX',
    role: AuUserRole.TRADER,
    isOnline: true,
    company_id: '3',
    company_longname: 'StartupX Ltd',
    current_auction_id: null,
    has_connection_problem: false,
    id: '3',
    isAuctioneer: false,
    isObserver: false,
    isTester: false,
    password: '',
    phone: '',
    socket_state: null,
    socket_state_last_closed: null,
    termination_reason: null,
    user_id: '3'
  }
];

interface UsersTableProps {
  users?: UserElement[];
  onUserSelect?: (user: UserElement) => void;
  height: number | string;
  width: number | string;
}

export function UsersTable({
  users = defaultUsers,
  onUserSelect,
  height,
  width
}: UsersTableProps) {
  const columnDefs: ColDef<UserElement>[] = [
    {
      field: 'username',
      headerName: 'Username',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'email',
      headerName: 'Email',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'company_shortname',
      headerName: 'Company',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'role',
      headerName: 'Role',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'isOnline',
      headerName: 'Status',
      sortable: true,
      filter: true,
      flex: 1,
      valueFormatter: (params) => params.value ? 'Online' : 'Offline',
    },
  ];

  return (
    <BaseAgGrid<UserElement>
      rowData={users}
      columnDefs={columnDefs}
      enableSelection={true}
      onSelectionChanged={(event) => {
        const selectedUser = event.api.getSelectedRows()[0];
        if (selectedUser && onUserSelect) {
          onUserSelect(selectedUser);
        }
      }}
      height={height}
      width={width}
      className="users-table"
    />
  );
}
