import React, { useState } from 'react';
import type { ColDef } from 'ag-grid-community';
import { BaseAgGrid } from './BaseAgGrid';

export interface ProductData {
    id: number;
    name: string;
    category: string;
    price: number;
    stock: number;
}

export const MyGrid2: React.FC = () => {
    const [rowData] = useState<ProductData[]>([
        { id: 1, name: "Laptop", category: "Electronics", price: 999.99, stock: 50 },
        { id: 2, name: "Desk Chair", category: "Furniture", price: 199.99, stock: 30 },
        { id: 3, name: "Coffee Maker", category: "Appliances", price: 79.99, stock: 100 },
        { id: 4, name: "Headphones", category: "Electronics", price: 149.99, stock: 75 }
    ]);

    const [colDefs] = useState<ColDef[]>([
        { field: "id", headerName: "ID", width: 70 },
        { field: "name", headerName: "Product Name", width: 150 },
        { field: "category", headerName: "Category", width: 120 },
        {
            field: "price",
            headerName: "Price",
            width: 100,
            valueFormatter: (params) => `$${params.value.toFixed(2)}`
        },
        {
            field: "stock",
            headerName: "Stock",
            width: 100,
            cellStyle: (params) => {
                if (params.value < 50) {
                    return { color: 'red' };
                }
                return null;
            }
        }
    ]);

    return (
        <BaseAgGrid
            height={400}
            width={500}
            rowData={rowData}
            columnDefs={colDefs}
            enableSorting={true}
            enableFiltering={true}
            enableSelection={true}
            enablePagination={true}
            onCellClicked={(event) => {
                console.log('Cell clicked:', event.data);
            }}
        />
    );
};
