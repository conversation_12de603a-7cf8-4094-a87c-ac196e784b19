import React from 'react';
import {AgGridReact} from 'ag-grid-react';
import type {ColDef, RowSelectedEvent} from 'ag-grid-community';
import {AllCommunityModule, ModuleRegistry, themeAlpine} from 'ag-grid-community';
import "ag-grid-community/styles/ag-theme-alpine.css"; // Theme CSS

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);

// import { fetchData } from '@repo/api-client'; // Example usage of api-client

interface RowData {
    [key: string]: any;
}

interface MyDataGridProps {
    rowData?: RowData[];
    columnDefs?: ColDef[];
    onRowSelected?: (event: RowSelectedEvent) => void;
    width?: number | string;
    height?: number | string;
}

const defaultRowData: RowData[] = [
    {make: "Toyota", model: "Celica", price: 35000},
    {make: "Ford", model: "Mondeo", price: 32000},
    {make: "Porsche", model: "Boxster", price: 72000}
];

const defaultColDefs: ColDef[] = [
    {field: "make"},
    {field: "model"},
    {field: "price"}
];

export const MyDataGrid: React.FC<MyDataGridProps> = ({
                                                          rowData: propsRowData,
                                                          columnDefs: propsColDefs,
                                                          onRowSelected,
                                                          width = 800,
                                                          height = 400
                                                      }) => {
    const rowData = propsRowData && propsRowData.length > 0 ? propsRowData : defaultRowData;
    const colDefs = propsColDefs && propsColDefs.length > 0 ? propsColDefs : defaultColDefs;

    return <div className="ag-theme-alpine" style={{height, width}}>
        <AgGridReact
            rowData={rowData}
            columnDefs={colDefs}
            onRowSelected={onRowSelected}
            rowSelection={{mode: 'singleRow'}}
            theme={themeAlpine}
        />
    </div>;
};
