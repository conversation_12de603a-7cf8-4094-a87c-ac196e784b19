/**
 * Sankey diagram implementation using D3-Sankey
 * https://github.com/d3/d3-sankey
 */

import React, { useEffect, useRef, useMemo } from 'react';
import * as d3 from 'd3-sankey';
import { select } from 'd3-selection';
import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType } from '../../api-client';
import { getOrderColor, SANKEY_COLORS } from './sankey-colors';

// Mandatory interface - no library-specific imports
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

export const SankeyD3: React.FC<SankeyProps> = ({
  width,
  height,
  matrix_edges,
  round_trader_elements,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  // Internal data transformation - no external dependencies
  const d3Data = useMemo(() => {
    // Separate buyers and sellers
    const sellers = round_trader_elements.filter(rte => rte.order_type === OrderType.SELL);
    const buyers = round_trader_elements.filter(rte => rte.order_type === OrderType.BUY);

    // Create nodes for D3 format
    const nodes = [
      // Max sell constraints
      ...sellers.map(seller => ({
        id: `max_sell_${seller.cid}`,
        name: `${seller.company_shortname} (Max: ${seller.constraints.max_sell_quantity})`
      })),
      // Seller orders
      ...sellers.map(seller => ({
        id: `sell_order_${seller.cid}`,
        name: `${seller.company_shortname}: ${seller.quantity_int}`
      })),
      // Buyer orders
      ...buyers.map(buyer => ({
        id: `buy_order_${buyer.cid}`,
        name: `${buyer.company_shortname}: ${buyer.quantity_int}`
      })),
      // Max buy constraints
      ...buyers.map(buyer => ({
        id: `max_buy_${buyer.cid}`,
        name: `${buyer.company_shortname} (Max: ${buyer.constraints.max_buy_quantity})`
      }))
    ];

    // Create links
    const links: Array<{source: number; target: number; value: number}> = [];

    // Column 1 → 2: Max constraints to orders
    sellers.forEach((seller, index) => {
      const sourceIndex = index; // max_sell nodes
      const targetIndex = sellers.length + index; // sell_order nodes
      links.push({
        source: sourceIndex,
        target: targetIndex,
        value: seller.quantity_int
      });
    });

    // Column 2 → 3: Actual matches
    matrix_edges.forEach(edge => {
      if (edge.match > 0) {
        const sellerIndex = sellers.findIndex(s => s.cid === edge.seller_cid);
        const buyerIndex = buyers.findIndex(b => b.cid === edge.buyer_cid);
        if (sellerIndex >= 0 && buyerIndex >= 0) {
          const sourceIndex = sellers.length + sellerIndex; // sell_order nodes
          const targetIndex = sellers.length * 2 + buyerIndex; // buy_order nodes
          links.push({
            source: sourceIndex,
            target: targetIndex,
            value: edge.match
          });
        }
      }
    });

    // Column 3 → 4: Orders to max constraints
    buyers.forEach((buyer, index) => {
      const sourceIndex = sellers.length * 2 + index; // buy_order nodes
      const targetIndex = sellers.length * 2 + buyers.length + index; // max_buy nodes
      links.push({
        source: sourceIndex,
        target: targetIndex,
        value: buyer.quantity_int
      });
    });

    return { nodes, links };
  }, [matrix_edges, round_trader_elements]);

  useEffect(() => {
    if (!svgRef.current || !d3Data.nodes.length) return;

    const svg = select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const margin = { top: 20, right: 20, bottom: 20, left: 20 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create sankey generator
    const sankey = d3
      .sankey()
      .nodeWidth(15)
      .nodePadding(10)
      .extent([[1, 1], [innerWidth - 1, innerHeight - 6]]);

    // Generate the sankey diagram
    const { nodes, links } = sankey({
      nodes: d3Data.nodes.map(d => ({ ...d })),
      links: d3Data.links.map(d => ({ ...d })),
    });

    // Add links
    const link = g
      .append('g')
      .selectAll('.link')
      .data(links)
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('d', d3.sankeyLinkHorizontal())
      .attr('stroke', (d: any) => d.color || SANKEY_COLORS.matchDimmed)
      .attr('stroke-width', (d: any) => Math.max(1, d.width))
      .attr('fill', 'none')
      .attr('opacity', 0.5)
      .on('mouseover', function(event, d: any) {
        select(this).attr('opacity', 0.8);

        // Show tooltip
        const tooltip = g
          .append('g')
          .attr('class', 'tooltip')
          .attr('transform', `translate(${event.layerX},${event.layerY})`);

        const rect = tooltip
          .append('rect')
          .attr('fill', 'white')
          .attr('stroke', '#ccc')
          .attr('rx', 4);

        const text = tooltip
          .append('text')
          .attr('font-size', '12px')
          .attr('fill', '#333')
          .attr('x', 8)
          .attr('y', 16);

        text.append('tspan')
          .text(`${d.source.label} → ${d.target.label}`)
          .attr('font-weight', 'bold');

        text.append('tspan')
          .text(`Flow: ${d.value} units`)
          .attr('x', 8)
          .attr('dy', 16);

        const bbox = text.node()?.getBBox();
        if (bbox) {
          rect
            .attr('width', bbox.width + 16)
            .attr('height', bbox.height + 8);
        }
      })
      .on('mouseout', function() {
        select(this).attr('opacity', 0.5);
        g.selectAll('.tooltip').remove();
      });

    // Add nodes
    const node = g
      .append('g')
      .selectAll('.node')
      .data(nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('transform', (d: any) => `translate(${d.x0},${d.y0})`);

    node
      .append('rect')
      .attr('height', (d: any) => d.y1 - d.y0)
      .attr('width', (d: any) => d.x1 - d.x0)
      .attr('fill', (d: any) => {
        if (d.id.startsWith('max_sell_') || d.id.startsWith('sell_order_')) {
          return getOrderColor(false, false);
        }
        if (d.id.startsWith('max_buy_') || d.id.startsWith('buy_order_')) {
          return getOrderColor(true, false);
        }
        return '#cccccc';
      })
      .attr('stroke', '#000')
      .attr('stroke-width', 0.5)
      .on('mouseover', function(event, d: any) {
        select(this).attr('opacity', 0.8);

        // Highlight connected links
        link
          .attr('opacity', (l: any) =>
            l.source === d || l.target === d ? 0.8 : 0.2
          );
      })
      .on('mouseout', function() {
        select(this).attr('opacity', 1);
        link.attr('opacity', 0.5);
      });

    // Add node labels
    node
      .append('text')
      .attr('x', (d: any) => (d.x1 - d.x0) / 2)
      .attr('y', (d: any) => (d.y1 - d.y0) / 2)
      .attr('dy', '0.35em')
      .attr('text-anchor', 'middle')
      .attr('font-size', '10px')
      .attr('fill', 'white')
      .attr('font-weight', 'bold')
      .text((d: any) => {
        const label = d.label || d.id;
        return label.length > 15 ? label.substring(0, 12) + '...' : label;
      });

    // Add external labels for better readability
    node
      .append('text')
      .attr('x', (d: any) => (d.x0 < innerWidth / 2) ? (d.x1 - d.x0) + 6 : -6)
      .attr('y', (d: any) => (d.y1 - d.y0) / 2)
      .attr('dy', '0.35em')
      .attr('text-anchor', (d: any) => (d.x0 < innerWidth / 2) ? 'start' : 'end')
      .attr('font-size', '11px')
      .attr('fill', '#333')
      .text((d: any) => d.label || d.id);

  }, [d3Data, width, height]);

  return (
    <div style={{ width, height }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ border: '1px solid #ddd' }}
      />
    </div>
  );
};

export default SankeyD3;
