/**
 * Common wrapper component for Sankey diagram stories
 * Provides data update functionality and consistent layout
 */

import React, { useState, useCallback } from 'react';
import { SankeyProps, createTestData, createEnhancedTestData } from './sankey-data';

interface SankeyStoryProps {
  title: string;
  children: (props: SankeyProps & { onDataUpdate?: () => void }) => React.ReactNode;
  width?: number;
  height?: number;
  showControls?: boolean;
}

export const SankeyStory: React.FC<SankeyStoryProps> = ({
  title,
  children,
  width = 800,
  height = 400,
  showControls = true,
}) => {
  const [dataVersion, setDataVersion] = useState<'simple' | 'enhanced'>('simple');
  const [updateKey, setUpdateKey] = useState(0);
  
  const currentData = dataVersion === 'simple' ? createTestData() : createEnhancedTestData();
  
  const handleDataUpdate = useCallback(() => {
    setUpdateKey(prev => prev + 1);
  }, []);
  
  const handleToggleData = useCallback(() => {
    setDataVersion(prev => prev === 'simple' ? 'enhanced' : 'simple');
    setUpdateKey(prev => prev + 1);
  }, []);
  
  const sankeyProps: SankeyProps = {
    width,
    height,
    matrix_edges: currentData.matrix_edges,
    round_trader_elements: currentData.round_trader_elements,
  };
  
  return (
    <div style={{ 
      padding: '20px', 
      border: '1px solid #ddd', 
      borderRadius: '8px', 
      marginBottom: '20px',
      backgroundColor: '#fafafa'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '15px' 
      }}>
        <h3 style={{ margin: 0, color: '#333' }}>{title}</h3>
        
        {showControls && (
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={handleToggleData}
              style={{
                padding: '8px 16px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
              }}
            >
              {dataVersion === 'simple' ? 'Use Enhanced Data' : 'Use Simple Data'}
            </button>
            
            <button
              onClick={handleDataUpdate}
              style={{
                padding: '8px 16px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
              }}
            >
              Trigger Update
            </button>
          </div>
        )}
      </div>
      
      <div style={{ 
        border: '1px solid #ccc', 
        borderRadius: '4px', 
        backgroundColor: 'white',
        overflow: 'hidden'
      }}>
        {children({ ...sankeyProps, onDataUpdate: handleDataUpdate })}
      </div>
      
      {showControls && (
        <div style={{ 
          marginTop: '10px', 
          fontSize: '12px', 
          color: '#666',
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          <span>
            Data: {dataVersion} | 
            Sellers: {currentData.round_trader_elements.filter(r => r.order_type === 'SELL').length} | 
            Buyers: {currentData.round_trader_elements.filter(r => r.order_type === 'BUY').length} | 
            Matches: {currentData.matrix_edges.filter(e => e.match > 0).length}
          </span>
          <span>Update Key: {updateKey}</span>
        </div>
      )}
    </div>
  );
};

// Error boundary for individual Sankey implementations
interface SankeyErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class SankeyErrorBoundary extends React.Component<
  { children: React.ReactNode; title: string },
  SankeyErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode; title: string }) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): SankeyErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error in ${this.props.title} Sankey implementation:`, error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          backgroundColor: '#ffe6e6',
          border: '1px solid #ff9999',
          borderRadius: '4px',
          color: '#cc0000'
        }}>
          <h4>Error in {this.props.title}</h4>
          <p>Something went wrong with this Sankey implementation.</p>
          {this.state.error && (
            <details style={{ marginTop: '10px' }}>
              <summary>Error Details</summary>
              <pre style={{ 
                fontSize: '12px', 
                backgroundColor: '#f5f5f5', 
                padding: '10px', 
                borderRadius: '4px',
                overflow: 'auto'
              }}>
                {this.state.error.message}
                {this.state.error.stack}
              </pre>
            </details>
          )}
        </div>
      );
    }
    
    return this.props.children;
  }
}

// Wrapper that combines SankeyStory with ErrorBoundary
export const SafeSankeyStory: React.FC<SankeyStoryProps> = (props) => {
  return (
    <SankeyErrorBoundary title={props.title}>
      <SankeyStory {...props} />
    </SankeyErrorBoundary>
  );
};
