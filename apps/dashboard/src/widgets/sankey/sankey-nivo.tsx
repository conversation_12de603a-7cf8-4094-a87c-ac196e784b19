/**
 * Sankey diagram implementation using Nivo
 * https://nivo.rocks/sankey/api/
 */

import React, { useMemo } from 'react';
import { ResponsiveSankey } from '@nivo/sankey';
import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType } from '../../api-client';
import { getOrderColor } from './sankey-colors';

// Mandatory interface - no library-specific imports
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

export const SankeyNivo: React.FC<SankeyProps> = ({
  width,
  height,
  matrix_edges,
  round_trader_elements,
}) => {
  // Internal data transformation - no external dependencies
  const sankeyData = useMemo(() => {
    // Separate buyers and sellers
    const sellers = round_trader_elements.filter(rte => rte.order_type === OrderType.SELL);
    const buyers = round_trader_elements.filter(rte => rte.order_type === OrderType.BUY);

    // Create nodes for Nivo format
    const nodes = [
      // Max sell constraints
      ...sellers.map(seller => ({
        id: `max_sell_${seller.cid}`,
        label: `${seller.company_shortname} (Max: ${seller.constraints.max_sell_quantity})`
      })),
      // Seller orders
      ...sellers.map(seller => ({
        id: `sell_order_${seller.cid}`,
        label: `${seller.company_shortname}: ${seller.quantity_int}`
      })),
      // Buyer orders
      ...buyers.map(buyer => ({
        id: `buy_order_${buyer.cid}`,
        label: `${buyer.company_shortname}: ${buyer.quantity_int}`
      })),
      // Max buy constraints
      ...buyers.map(buyer => ({
        id: `max_buy_${buyer.cid}`,
        label: `${buyer.company_shortname} (Max: ${buyer.constraints.max_buy_quantity})`
      }))
    ];

    // Create links
    const links: Array<{source: string; target: string; value: number}> = [];

    // Column 1 → 2: Max constraints to orders
    sellers.forEach(seller => {
      links.push({
        source: `max_sell_${seller.cid}`,
        target: `sell_order_${seller.cid}`,
        value: seller.quantity_int
      });
    });

    // Column 2 → 3: Actual matches
    matrix_edges.forEach(edge => {
      if (edge.match > 0) {
        links.push({
          source: `sell_order_${edge.seller_cid}`,
          target: `buy_order_${edge.buyer_cid}`,
          value: edge.match
        });
      }
    });

    // Column 3 → 4: Orders to max constraints
    buyers.forEach(buyer => {
      links.push({
        source: `buy_order_${buyer.cid}`,
        target: `max_buy_${buyer.cid}`,
        value: buyer.quantity_int
      });
    });

    return { nodes, links };
  }, [matrix_edges, round_trader_elements]);

  const getNodeColor = (node: any) => {
    // Color nodes based on their position/type
    if (node.id.startsWith('max_sell_') || node.id.startsWith('sell_order_')) {
      return getOrderColor(false, false); // Sell color
    }
    if (node.id.startsWith('max_buy_') || node.id.startsWith('buy_order_')) {
      return getOrderColor(true, false); // Buy color
    }
    return '#cccccc';
  };

  // Note: Link coloring not available in this Nivo version

  return (
    <div style={{ width, height }}>
      <ResponsiveSankey
        data={sankeyData}
        margin={{ top: 40, right: 160, bottom: 40, left: 50 }}
        align="justify"
        colors={getNodeColor}
        nodeOpacity={1}
        nodeHoverOthersOpacity={0.35}
        nodeThickness={18}
        nodeSpacing={24}
        nodeBorderWidth={0}
        nodeBorderColor={{
          from: 'color',
          modifiers: [['darker', 0.8]],
        }}
        nodeBorderRadius={3}
        linkOpacity={0.5}
        linkHoverOthersOpacity={0.1}
        linkContract={3}
        enableLinkGradient={true}
        labelPosition="outside"
        labelOrientation="vertical"
        labelPadding={16}
        labelTextColor={{
          from: 'color',
          modifiers: [['darker', 1]],
        }}
        legends={[
          {
            anchor: 'bottom-right',
            direction: 'column',
            translateX: 130,
            itemWidth: 100,
            itemHeight: 14,
            itemDirection: 'right-to-left',
            itemsSpacing: 2,
            itemTextColor: '#999',
            symbolSize: 14,
            effects: [
              {
                on: 'hover',
                style: {
                  itemTextColor: '#000',
                },
              },
            ],
          },
        ]}
        // Note: linkColor prop not supported in this version
        // Tooltip customization
        nodeTooltip={({ node }) => (
          <div
            style={{
              background: 'white',
              padding: '9px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '12px',
            }}
          >
            <strong>{node.label}</strong>
            <br />
            Value: {node.value}
          </div>
        )}
        linkTooltip={({ link }) => (
          <div
            style={{
              background: 'white',
              padding: '9px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '12px',
            }}
          >
            <strong>{link.source.label} → {link.target.label}</strong>
            <br />
            Flow: {link.value} units
          </div>
        )}
        // Animation
        animate={true}
        motionConfig="gentle"
      />
    </div>
  );
};

export default SankeyNivo;
