/**
 * Sankey diagram implementation - Pure SVG (custom implementation)
 * Self-contained with internal data transformation
 */

import React, { useMemo } from 'react';
import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType } from '../../api-client';
import { getOrderColor, SANKEY_COLORS } from './sankey-colors';

// Mandatory interface - no library-specific imports
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

export const SankeySvg: React.FC<SankeyProps> = ({
  width,
  height,
  matrix_edges,
  round_trader_elements,
}) => {
  // Internal data transformation - no external dependencies
  const sankeyData = useMemo(() => {
    // Separate buyers and sellers
    const sellers = round_trader_elements.filter(rte => rte.order_type === OrderType.SELL);
    const buyers = round_trader_elements.filter(rte => rte.order_type === OrderType.BUY);

    // Create 4 columns of nodes
    const columns = [
      // Column 1: Max Sell Constraints
      sellers.map(seller => ({
        id: `max_sell_${seller.cid}`,
        label: `${seller.company_shortname} (Max: ${seller.constraints.max_sell_quantity})`,
        value: seller.constraints.max_sell_quantity,
        type: 'max_sell'
      })),
      // Column 2: Seller Orders
      sellers.map(seller => ({
        id: `sell_order_${seller.cid}`,
        label: `${seller.company_shortname}: ${seller.quantity_int}`,
        value: seller.quantity_int,
        type: 'sell_order'
      })),
      // Column 3: Buyer Orders
      buyers.map(buyer => ({
        id: `buy_order_${buyer.cid}`,
        label: `${buyer.company_shortname}: ${buyer.quantity_int}`,
        value: buyer.quantity_int,
        type: 'buy_order'
      })),
      // Column 4: Max Buy Constraints
      buyers.map(buyer => ({
        id: `max_buy_${buyer.cid}`,
        label: `${buyer.company_shortname} (Max: ${buyer.constraints.max_buy_quantity})`,
        value: buyer.constraints.max_buy_quantity,
        type: 'max_buy'
      }))
    ];

    // Create links
    const links: Array<{source: string; target: string; value: number; type: string}> = [];

    // Column 1 → 2: Max constraints to orders
    sellers.forEach(seller => {
      links.push({
        source: `max_sell_${seller.cid}`,
        target: `sell_order_${seller.cid}`,
        value: seller.quantity_int,
        type: 'constraint'
      });
    });

    // Column 2 → 3: Actual matches
    matrix_edges.forEach(edge => {
      if (edge.match > 0) {
        links.push({
          source: `sell_order_${edge.seller_cid}`,
          target: `buy_order_${edge.buyer_cid}`,
          value: edge.match,
          type: 'match'
        });
      }
    });

    // Column 3 → 4: Orders to max constraints
    buyers.forEach(buyer => {
      links.push({
        source: `buy_order_${buyer.cid}`,
        target: `max_buy_${buyer.cid}`,
        value: buyer.quantity_int,
        type: 'constraint'
      });
    });

    return { columns, links };
  }, [matrix_edges, round_trader_elements]);

  const margin = { top: 20, left: 50, right: 100, bottom: 20 };
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  // Layout nodes in 4 columns
  const columnWidth = innerWidth / 4;
  const nodeWidth = 15;

  const positionedNodes = sankeyData.columns.map((column, columnIndex) => {
    const x = columnIndex * columnWidth + columnWidth / 2 - nodeWidth / 2;
    const totalValue = column.reduce((sum, node) => sum + node.value, 0);
    const scale = totalValue > 0 ? innerHeight / totalValue : 1;

    let currentY = 0;
    return column.map(node => {
      const nodeHeight = Math.max(10, node.value * scale * 0.8); // Scale down to fit
      const y = currentY;
      currentY += nodeHeight + 10; // 10px spacing

      return {
        ...node,
        x,
        y,
        width: nodeWidth,
        height: nodeHeight,
      };
    });
  }).flat();

  // Create node lookup
  const nodeMap = new Map(positionedNodes.map(node => [node.id, node]));

  // Position links
  const positionedLinks = sankeyData.links.map(link => {
    const sourceNode = nodeMap.get(link.source);
    const targetNode = nodeMap.get(link.target);

    if (!sourceNode || !targetNode) return null;

    return {
      ...link,
      sourceNode,
      targetNode,
      height: Math.max(2, link.value * 2)
    };
  }).filter(Boolean);

  const getNodeColor = (node: any) => {
    if (node.type === 'max_sell' || node.type === 'sell_order') {
      return getOrderColor(false, false);
    }
    if (node.type === 'max_buy' || node.type === 'buy_order') {
      return getOrderColor(true, false);
    }
    return '#cccccc';
  };

  const getLinkColor = (link: any) => {
    if (link.type === 'match') {
      return SANKEY_COLORS.matchDark;
    }
    return SANKEY_COLORS.matchDimmed;
  };

  // Create curved path for links
  const createLinkPath = (link: any) => {
    const { sourceNode, targetNode, height } = link;
    const x1 = sourceNode.x + sourceNode.width;
    const y1 = sourceNode.y + sourceNode.height / 2 - height / 2;
    const x2 = targetNode.x;
    const y2 = targetNode.y + targetNode.height / 2 - height / 2;

    const curvature = 0.5;
    const xi = (x1 + x2) * curvature;

    return `
      M${x1},${y1}
      C${xi},${y1} ${xi},${y2} ${x2},${y2}
      L${x2},${y2 + height}
      C${xi},${y2 + height} ${xi},${y1 + height} ${x1},${y1 + height}
      Z
    `;
  };

  return (
    <div style={{ width, height }}>
      <svg width={width} height={height} style={{ border: '1px solid #ddd' }}>
        <g transform={`translate(${margin.left},${margin.top})`}>
          {/* Render links first */}
          {positionedLinks.map((link, i) => (
            <path
              key={`link-${i}`}
              d={createLinkPath(link)}
              fill={getLinkColor(link)}
              opacity={0.5}
              stroke="none"
            >
              <title>
                {link.source} → {link.target}: {link.value} units
              </title>
            </path>
          ))}

          {/* Render nodes */}
          {positionedNodes.map((node, i) => (
            <g key={`node-${i}`}>
              <rect
                x={node.x}
                y={node.y}
                width={node.width}
                height={node.height}
                fill={getNodeColor(node)}
                stroke="#000"
                strokeWidth={0.5}
                rx={2}
              >
                <title>{node.label}: {node.value}</title>
              </rect>

              {/* Node labels */}
              <text
                x={node.x < innerWidth / 2 ? node.x + node.width + 6 : node.x - 6}
                y={node.y + node.height / 2}
                dy="0.35em"
                textAnchor={node.x < innerWidth / 2 ? 'start' : 'end'}
                fontSize={10}
                fill="#333"
              >
                {node.label}
              </text>
            </g>
          ))}

          {/* Column headers */}
          <text x={columnWidth * 0.5} y={-5} textAnchor="middle" fontSize={12} fontWeight="bold" fill="#666">
            Max Sell
          </text>
          <text x={columnWidth * 1.5} y={-5} textAnchor="middle" fontSize={12} fontWeight="bold" fill="#666">
            Sell Orders
          </text>
          <text x={columnWidth * 2.5} y={-5} textAnchor="middle" fontSize={12} fontWeight="bold" fill="#666">
            Buy Orders
          </text>
          <text x={columnWidth * 3.5} y={-5} textAnchor="middle" fontSize={12} fontWeight="bold" fill="#666">
            Max Buy
          </text>
        </g>
      </svg>
    </div>
  );
};

export default SankeySvg;
