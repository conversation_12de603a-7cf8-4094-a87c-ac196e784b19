/**
 * Sankey diagram implementation using Plotly.js
 * https://plotly.com/javascript/sankey-diagram/
 */

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType } from '../../api-client';

// Mandatory interface - no library-specific imports
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

export const SankeyPlotly: React.FC<SankeyProps> = ({
  width,
  height,
  matrix_edges,
  round_trader_elements,
}) => {
  // Internal data transformation - no external dependencies
  const plotlyData = useMemo(() => {
    // Separate buyers and sellers
    const sellers = round_trader_elements.filter(rte => rte.order_type === OrderType.SELL);
    const buyers = round_trader_elements.filter(rte => rte.order_type === OrderType.BUY);

    // Create node labels for Plotly format
    const labels = [
      // Max sell constraints
      ...sellers.map(seller => `${seller.company_shortname} (Max: ${seller.constraints.max_sell_quantity})`),
      // Seller orders
      ...sellers.map(seller => `${seller.company_shortname}: ${seller.quantity_int}`),
      // Buyer orders
      ...buyers.map(buyer => `${buyer.company_shortname}: ${buyer.quantity_int}`),
      // Max buy constraints
      ...buyers.map(buyer => `${buyer.company_shortname} (Max: ${buyer.constraints.max_buy_quantity})`)
    ];

    // Create links (source/target are indices into labels array)
    const source: number[] = [];
    const target: number[] = [];
    const value: number[] = [];

    // Column 1 → 2: Max constraints to orders
    sellers.forEach((seller, index) => {
      const sourceIndex = index; // max_sell nodes
      const targetIndex = sellers.length + index; // sell_order nodes
      source.push(sourceIndex);
      target.push(targetIndex);
      value.push(seller.quantity_int);
    });

    // Column 2 → 3: Actual matches
    matrix_edges.forEach(edge => {
      if (edge.match > 0) {
        const sellerIndex = sellers.findIndex(s => s.cid === edge.seller_cid);
        const buyerIndex = buyers.findIndex(b => b.cid === edge.buyer_cid);
        if (sellerIndex >= 0 && buyerIndex >= 0) {
          const sourceIndex = sellers.length + sellerIndex; // sell_order nodes
          const targetIndex = sellers.length * 2 + buyerIndex; // buy_order nodes
          source.push(sourceIndex);
          target.push(targetIndex);
          value.push(edge.match);
        }
      }
    });

    // Column 3 → 4: Orders to max constraints
    buyers.forEach((buyer, index) => {
      const sourceIndex = sellers.length * 2 + index; // buy_order nodes
      const targetIndex = sellers.length * 2 + buyers.length + index; // max_buy nodes
      source.push(sourceIndex);
      target.push(targetIndex);
      value.push(buyer.quantity_int);
    });

    return { labels, source, target, value };
  }, [matrix_edges, round_trader_elements]);

  const data = [
    {
      type: 'sankey',
      orientation: 'h',
      node: {
        label: plotlyData.labels,
        hovertemplate: '<b>%{label}</b><br>Value: %{value}<extra></extra>',
      },
      link: {
        source: plotlyData.source,
        target: plotlyData.target,
        value: plotlyData.value,
        hovertemplate: '<b>%{source.label} → %{target.label}</b><br>Flow: %{value} units<extra></extra>',
      },
    },
  ];

  const layout = {
    title: {
      text: 'Auction Flow Diagram',
      font: { size: 16 },
    },
    width,
    height,
    margin: { l: 50, r: 50, t: 50, b: 50 },
    font: { size: 12 },
    paper_bgcolor: 'white',
    plot_bgcolor: 'white',
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'pan2d',
      'select2d',
      'lasso2d',
      'resetScale2d',
      'autoScale2d',
    ],
    responsive: true,
  };

  return (
    <div style={{ width, height }}>
      <Plot
        data={data}
        layout={layout}
        config={config}
        style={{ width: '100%', height: '100%' }}
        useResizeHandler={true}
      />
    </div>
  );
};

export default SankeyPlotly;
