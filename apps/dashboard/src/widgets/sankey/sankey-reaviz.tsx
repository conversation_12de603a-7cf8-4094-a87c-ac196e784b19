/**
 * Sankey diagram implementation using <PERSON><PERSON><PERSON>
 * https://reaviz.dev/
 */

import React, { useMemo } from 'react';
import { Sankey, SankeyNode, SankeyLink } from 'reaviz';
import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType } from '../../api-client';
import { getOrderColor, SANKEY_COLORS } from './sankey-colors';

// Mandatory interface - no library-specific imports
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

export const SankeyReaviz: React.FC<SankeyProps> = ({
  width,
  height,
  matrix_edges,
  round_trader_elements,
}) => {
  // Create JSX nodes and links for Reaviz
  const { nodes, links } = useMemo(() => {
    // Separate buyers and sellers
    const sellers = round_trader_elements.filter(rte => rte.order_type === OrderType.SELL);
    const buyers = round_trader_elements.filter(rte => rte.order_type === OrderType.BUY);

    // Create JSX nodes using SankeyNode components
    const nodes = [
      // Max sell constraints
      ...sellers.map(seller => (
        <SankeyNode
          key={`max_sell_${seller.cid}`}
          id={`max_sell_${seller.cid}`}
          title={`${seller.company_shortname} (Max: ${seller.constraints.max_sell_quantity})`}
          color={getOrderColor(false, false)}
        />
      )),
      // Seller orders
      ...sellers.map(seller => (
        <SankeyNode
          key={`sell_order_${seller.cid}`}
          id={`sell_order_${seller.cid}`}
          title={`${seller.company_shortname}: ${seller.quantity_int}`}
          color={getOrderColor(false, false)}
        />
      )),
      // Buyer orders
      ...buyers.map(buyer => (
        <SankeyNode
          key={`buy_order_${buyer.cid}`}
          id={`buy_order_${buyer.cid}`}
          title={`${buyer.company_shortname}: ${buyer.quantity_int}`}
          color={getOrderColor(true, false)}
        />
      )),
      // Max buy constraints
      ...buyers.map(buyer => (
        <SankeyNode
          key={`max_buy_${buyer.cid}`}
          id={`max_buy_${buyer.cid}`}
          title={`${buyer.company_shortname} (Max: ${buyer.constraints.max_buy_quantity})`}
          color={getOrderColor(true, false)}
        />
      ))
    ];

    // Create JSX links using SankeyLink components
    const links: React.ReactElement[] = [];

    // Column 1 → 2: Max constraints to orders
    sellers.forEach(seller => {
      links.push(
        <SankeyLink
          key={`link_max_sell_${seller.cid}`}
          source={`max_sell_${seller.cid}`}
          target={`sell_order_${seller.cid}`}
          value={seller.quantity_int}
          color={SANKEY_COLORS.matchDimmed}
        />
      );
    });

    // Column 2 → 3: Actual matches
    matrix_edges.forEach(edge => {
      if (edge.match > 0) {
        links.push(
          <SankeyLink
            key={`link_match_${edge.seller_cid}_${edge.buyer_cid}`}
            source={`sell_order_${edge.seller_cid}`}
            target={`buy_order_${edge.buyer_cid}`}
            value={edge.match}
            color={SANKEY_COLORS.matchDark}
          />
        );
      }
    });

    // Column 3 → 4: Orders to max constraints
    buyers.forEach(buyer => {
      links.push(
        <SankeyLink
          key={`link_max_buy_${buyer.cid}`}
          source={`buy_order_${buyer.cid}`}
          target={`max_buy_${buyer.cid}`}
          value={buyer.quantity_int}
          color={SANKEY_COLORS.matchDimmed}
        />
      );
    });

    return { nodes, links };
  }, [matrix_edges, round_trader_elements]);

  return (
    <div style={{ width, height }}>
      <Sankey
        height={height}
        width={width}
        nodes={nodes as any}
        links={links as any}
        nodeWidth={15}
        nodePadding={10}
        animated={true}
      />
    </div>
  );
};

export default SankeyReaviz;
