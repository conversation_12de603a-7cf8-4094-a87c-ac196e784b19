/**
 * Sankey diagram colors extracted from the Vue AuColors implementation
 * 8 color combinations: buy/sell × highlighted/non-highlighted × colorblind/non-colorblind
 */

import chroma from 'chroma-js';

// Base colors from AuColors.ts
const AU_RED = 'hsl(0, 92%, 75%)';
const AU_GREEN = 'hsl(120, 47%, 64%)';
const SELL_PINK_4 = 'hsl(331, 95%, 70%)';
const BUY_GREEN_4 = 'hsl(91, 74%, 60%)';
const AU_BLUE = 'hsl(187, 50%, 60%)';
const AU_BLUE_BRIGHT = 'hsla(187, 73.2%, 70.8%, 1)';

enum COLOR_THEME {
  RED_GREEN = 'RED_GREEN',
  PINK_GREEN = 'PINK_GREEN'
}

export interface SankeyColors {
  // Buy colors
  buyNormal: string;
  buyNormalColorblind: string;
  buyHighlighted: string;
  buyHighlightedColorblind: string;
  
  // Sell colors
  sellNormal: string;
  sellNormalColorblind: string;
  sellHighlighted: string;
  sellHighlightedColorblind: string;
  
  // Match colors
  matchNormal: string;
  matchHighlighted: string;
  
  // Dimmed versions
  buyDimmed: string;
  sellDimmed: string;
  matchDimmed: string;
  
  // Dark versions
  buyDark: string;
  sellDark: string;
  matchDark: string;
}

function createColors(theme: COLOR_THEME = COLOR_THEME.RED_GREEN): SankeyColors {
  // Base buy/sell colors based on theme
  const buyBase = theme === COLOR_THEME.RED_GREEN ? AU_GREEN : BUY_GREEN_4;
  const sellBase = theme === COLOR_THEME.RED_GREEN ? AU_RED : SELL_PINK_4;
  const matchBase = theme === COLOR_THEME.RED_GREEN ? AU_BLUE : AU_BLUE_BRIGHT;
  
  // Bright versions for highlighting
  const buyBright = theme === COLOR_THEME.RED_GREEN ? 'hsl(120, 100%, 50%)' : 'hsl(91, 84%, 70%)';
  const sellBright = theme === COLOR_THEME.RED_GREEN ? 'hsl(0, 100%, 60%)' : 'hsl(331, 100%, 75%)';
  
  return {
    // Buy colors (8 combinations)
    buyNormal: buyBase,
    buyNormalColorblind: BUY_GREEN_4, // Always use green variant for colorblind
    buyHighlighted: buyBright,
    buyHighlightedColorblind: 'hsl(91, 84%, 70%)', // Bright green for colorblind
    
    // Sell colors
    sellNormal: sellBase,
    sellNormalColorblind: SELL_PINK_4, // Always use pink variant for colorblind
    sellHighlighted: sellBright,
    sellHighlightedColorblind: 'hsl(331, 100%, 75%)', // Bright pink for colorblind
    
    // Match colors
    matchNormal: matchBase,
    matchHighlighted: matchBase, // Same for now, could be brighter
    
    // Dimmed versions (50% alpha)
    buyDimmed: chroma(buyBase).alpha(0.50).hex(),
    sellDimmed: chroma(sellBase).alpha(0.50).hex(),
    matchDimmed: chroma(matchBase).alpha(0.30).hex(),
    
    // Dark versions (low luminance)
    buyDark: chroma(buyBase).luminance(0.1).hex(),
    sellDark: chroma(sellBase).luminance(0.1).hex(),
    matchDark: chroma(matchBase).luminance(0.1).hex(),
  };
}

// Default color palette
export const SANKEY_COLORS = createColors(COLOR_THEME.RED_GREEN);

// Alternative colorblind-friendly palette
export const SANKEY_COLORS_COLORBLIND = createColors(COLOR_THEME.PINK_GREEN);

// Palette array as used in Vue component
export const SANKEY_PALETTE = [
  SANKEY_COLORS.buyDimmed,    // Column 0
  SANKEY_COLORS.buyNormal,    // Column 1
  SANKEY_COLORS.sellNormal,   // Column 2
  SANKEY_COLORS.sellDimmed,   // Column 3
];

// Helper function to get color by order type and state
export function getOrderColor(
  isBuy: boolean, 
  isHighlighted: boolean = false, 
  isColorblind: boolean = false
): string {
  const colors = isColorblind ? SANKEY_COLORS_COLORBLIND : SANKEY_COLORS;
  
  if (isBuy) {
    return isHighlighted ? colors.buyHighlighted : colors.buyNormal;
  } else {
    return isHighlighted ? colors.sellHighlighted : colors.sellNormal;
  }
}

// Helper function to get dimmed color
export function getDimmedColor(isBuy: boolean): string {
  return isBuy ? SANKEY_COLORS.buyDimmed : SANKEY_COLORS.sellDimmed;
}

// Helper function to get dark color
export function getDarkColor(isBuy: boolean): string {
  return isBuy ? SANKEY_COLORS.buyDark : SANKEY_COLORS.sellDark;
}
