/**
 * Common interfaces and test data for Sankey diagram implementations
 */

import { DeMatrixEdgeElement, DeRoundTraderElement, OrderType, OrderSubmissionType } from '../../api-client';

// MANDATORY interface that ALL Sankey implementations must use - no exceptions
export interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}

// Test data that follows auction rules
export function createTestData(): { matrix_edges: DeMatrixEdgeElement[], round_trader_elements: DeRoundTraderElement[] } {
  const round_trader_elements: DeRoundTraderElement[] = [
    // Sellers
    {
      id: 'seller_a_round_1',
      round: 1,
      cid: 'seller_a',
      company_shortname: 'SellerA',
      order_type: OrderType.SELL,
      quantity_int: 10,
      quantity_str: '10',
      match: 10, // Fully matched
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.DEFAULT,
      order_submitted_by: 'user1',
      timestamp_formatted: '10:30:15',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 0,
        min_sell_quantity: 0,
        max_sell_quantity: 40, // Max constraint
      },
      buyer_credit_limit: 0,
      buyer_credit_limit_str: '0',
    },
    {
      id: 'seller_b_round_1',
      round: 1,
      cid: 'seller_b',
      company_shortname: 'SellerB',
      order_type: OrderType.SELL,
      quantity_int: 20,
      quantity_str: '20',
      match: 10, // Partially matched
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.DEFAULT,
      order_submitted_by: 'user2',
      timestamp_formatted: '10:30:20',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 0,
        min_sell_quantity: 0,
        max_sell_quantity: 30, // Max constraint
      },
      buyer_credit_limit: 0,
      buyer_credit_limit_str: '0',
    },
    // Buyers
    {
      id: 'buyer_c_round_1',
      round: 1,
      cid: 'buyer_c',
      company_shortname: 'BuyerC',
      order_type: OrderType.BUY,
      quantity_int: 5,
      quantity_str: '5',
      match: 5, // Fully matched
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.DEFAULT,
      order_submitted_by: 'user3',
      timestamp_formatted: '10:30:25',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 10, // Max constraint
        min_sell_quantity: 0,
        max_sell_quantity: 0,
      },
      buyer_credit_limit: 1000,
      buyer_credit_limit_str: '1000',
    },
    {
      id: 'buyer_d_round_1',
      round: 1,
      cid: 'buyer_d',
      company_shortname: 'BuyerD',
      order_type: OrderType.BUY,
      quantity_int: 15,
      quantity_str: '15',
      match: 15, // Fully matched
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.DEFAULT,
      order_submitted_by: 'user4',
      timestamp_formatted: '10:30:30',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 30, // Max constraint
        min_sell_quantity: 0,
        max_sell_quantity: 0,
      },
      buyer_credit_limit: 2000,
      buyer_credit_limit_str: '2000',
    },
  ];

  const matrix_edges: DeMatrixEdgeElement[] = [
    {
      id: 'edge_a_c',
      r: 1,
      seller_cid: 'seller_a',
      buyer_cid: 'buyer_c',
      seller_shortname: 'SellerA',
      buyer_shortname: 'BuyerC',
      match: 5, // SellerA -> BuyerC: 5 units
      buy_quantity_limit: 10,
      selling_quantity_limit: 40,
      capacity: 5,
      credit_quantity_limit: 1000,
      credit_str: '1000',
      value: 500,
      value_str: '500',
    },
    {
      id: 'edge_a_d',
      r: 1,
      seller_cid: 'seller_a',
      buyer_cid: 'buyer_d',
      seller_shortname: 'SellerA',
      buyer_shortname: 'BuyerD',
      match: 5, // SellerA -> BuyerD: 5 units
      buy_quantity_limit: 30,
      selling_quantity_limit: 40,
      capacity: 5,
      credit_quantity_limit: 2000,
      credit_str: '2000',
      value: 500,
      value_str: '500',
    },
    {
      id: 'edge_b_d',
      r: 1,
      seller_cid: 'seller_b',
      buyer_cid: 'buyer_d',
      seller_shortname: 'SellerB',
      buyer_shortname: 'BuyerD',
      match: 10, // SellerB -> BuyerD: 10 units
      buy_quantity_limit: 30,
      selling_quantity_limit: 30,
      capacity: 10,
      credit_quantity_limit: 2000,
      credit_str: '2000',
      value: 1000,
      value_str: '1000',
    },
  ];

  return { matrix_edges, round_trader_elements };
}

// Enhanced test data with more complexity
export function createEnhancedTestData(): { matrix_edges: DeMatrixEdgeElement[], round_trader_elements: DeRoundTraderElement[] } {
  const baseData = createTestData();

  // Add one more seller and buyer for more complexity
  const additionalTraders: DeRoundTraderElement[] = [
    {
      id: 'seller_e_round_1',
      round: 1,
      cid: 'seller_e',
      company_shortname: 'SellerE',
      order_type: OrderType.SELL,
      quantity_int: 8,
      quantity_str: '8',
      match: 3,
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.MANUAL,
      order_submitted_by: 'user5',
      timestamp_formatted: '10:30:35',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 0,
        min_sell_quantity: 0,
        max_sell_quantity: 25,
      },
      buyer_credit_limit: 0,
      buyer_credit_limit_str: '0',
    },
    {
      id: 'buyer_f_round_1',
      round: 1,
      cid: 'buyer_f',
      company_shortname: 'BuyerF',
      order_type: OrderType.BUY,
      quantity_int: 3,
      quantity_str: '3',
      match: 3,
      changed: false,
      bid_while_closed: false,
      order_submission_type: OrderSubmissionType.MANUAL,
      order_submitted_by: 'user6',
      timestamp_formatted: '10:30:40',
      constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 15,
        min_sell_quantity: 0,
        max_sell_quantity: 0,
      },
      buyer_credit_limit: 500,
      buyer_credit_limit_str: '500',
    },
  ];

  const additionalEdges: DeMatrixEdgeElement[] = [
    {
      id: 'edge_e_f',
      r: 1,
      seller_cid: 'seller_e',
      buyer_cid: 'buyer_f',
      seller_shortname: 'SellerE',
      buyer_shortname: 'BuyerF',
      match: 3,
      buy_quantity_limit: 15,
      selling_quantity_limit: 25,
      capacity: 3,
      credit_quantity_limit: 500,
      credit_str: '500',
      value: 300,
      value_str: '300',
    },
  ];

  return {
    matrix_edges: [...baseData.matrix_edges, ...additionalEdges],
    round_trader_elements: [...baseData.round_trader_elements, ...additionalTraders],
  };
}
