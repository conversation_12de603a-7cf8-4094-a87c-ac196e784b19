/**
 * Storybook stories comparing all Sankey diagram implementations
 */

import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { SafeSankeyStory } from './SankeyStory';

// Import all implementations
import SankeyNivo from './sankey-nivo';
import SankeyPlotly from './sankey-plotly';
import SankeyD3 from './sankey-d3';
import SankeyReaviz from './sankey-reaviz';
import SankeySvg from './sankey-svg';

const meta: Meta = {
  title: 'Widgets/Sankey Comparison',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# Sankey Diagram Library Comparison

This story compares 5 different implementations of Sankey diagrams for visualizing auction flow data.
Each implementation uses the same data structure and shows the flow from:

**Max Sell Constraints → Seller Orders → Buyer Orders → Max Buy Constraints**

## Test Data
- **Sellers**: SellerA (max: 40, order: 10), SellerB (max: 30, order: 20)
- **Buyers**: BuyerC (max: 10, order: 5), BuyerD (max: 30, order: 15)
- **Matches**: A→C: 5, A→D: 5, B→D: 10

## Libraries Tested
1. **Nivo** - Feature-rich React charting library
2. **Plotly** - Powerful scientific plotting library
3. **D3-Sankey** - Direct D3 implementation with React wrapper
4. **Reaviz** - React data visualization library
5. **Custom SVG** - Pure SVG implementation with manual layout

Use the controls in each story to test data updates and animations.
        `,
      },
    },
  },
};

export default meta;
type Story = StoryObj;

// Common dimensions
const STORY_WIDTH = 800;
const STORY_HEIGHT = 400;

export const AllImplementations: Story = {
  render: () => (
    <div style={{ padding: '20px' }}>
      <h1 style={{ marginBottom: '30px', color: '#333' }}>
        Sankey Diagram Library Comparison
      </h1>

      <p style={{ marginBottom: '30px', color: '#666', maxWidth: '800px' }}>
        Compare different library implementations of the same auction flow data.
        Each shows the flow from seller constraints through orders to buyer constraints.
        Use the controls to test data updates and animations.
      </p>

      {/* Nivo Implementation */}
      <SafeSankeyStory
        title="1. Nivo Sankey"
        width={STORY_WIDTH}
        height={STORY_HEIGHT}
      >
        {(props) => <SankeyNivo {...props} />}
      </SafeSankeyStory>

      {/* Plotly Implementation */}
      <SafeSankeyStory
        title="2. Plotly Sankey"
        width={STORY_WIDTH}
        height={STORY_HEIGHT}
      >
        {(props) => <SankeyPlotly {...props} />}
      </SafeSankeyStory>

      {/* D3 Implementation */}
      <SafeSankeyStory
        title="3. D3-Sankey + React"
        width={STORY_WIDTH}
        height={STORY_HEIGHT}
      >
        {(props) => <SankeyD3 {...props} />}
      </SafeSankeyStory>

      {/* Reaviz Implementation */}
      <SafeSankeyStory
        title="4. Reaviz Sankey"
        width={STORY_WIDTH}
        height={STORY_HEIGHT}
      >
        {(props) => <SankeyReaviz {...props} />}
      </SafeSankeyStory>

      {/* Custom SVG Implementation */}
      <SafeSankeyStory
        title="5. Custom SVG"
        width={STORY_WIDTH}
        height={STORY_HEIGHT}
      >
        {(props) => <SankeySvg {...props} />}
      </SafeSankeyStory>

      {/* Summary */}
      <div style={{
        marginTop: '40px',
        padding: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <h3 style={{ marginTop: 0, color: '#333' }}>Implementation Notes</h3>
        <ul style={{ color: '#666', lineHeight: 1.6 }}>
          <li><strong>Nivo</strong>: Full-featured with built-in animations and interactions</li>
          <li><strong>Plotly</strong>: Powerful with scientific-grade features and zoom/pan</li>
          <li><strong>D3-Sankey</strong>: Most flexible, direct control over rendering</li>
          <li><strong>Reaviz</strong>: React data visualization library (experimental)</li>
          <li><strong>Custom SVG</strong>: Pure SVG implementation with manual layout</li>
        </ul>
      </div>
    </div>
  ),
};

// Individual stories for focused testing
export const NivoOnly: Story = {
  render: () => (
    <SafeSankeyStory title="Nivo Sankey" width={STORY_WIDTH} height={STORY_HEIGHT}>
      {(props) => <SankeyNivo {...props} />}
    </SafeSankeyStory>
  ),
};

export const PlotlyOnly: Story = {
  render: () => (
    <SafeSankeyStory title="Plotly Sankey" width={STORY_WIDTH} height={STORY_HEIGHT}>
      {(props) => <SankeyPlotly {...props} />}
    </SafeSankeyStory>
  ),
};

export const D3Only: Story = {
  render: () => (
    <SafeSankeyStory title="D3-Sankey" width={STORY_WIDTH} height={STORY_HEIGHT}>
      {(props) => <SankeyD3 {...props} />}
    </SafeSankeyStory>
  ),
};

export const ReavizOnly: Story = {
  render: () => (
    <SafeSankeyStory title="Reaviz Sankey" width={STORY_WIDTH} height={STORY_HEIGHT}>
      {(props) => <SankeyReaviz {...props} />}
    </SafeSankeyStory>
  ),
};

export const SvgOnly: Story = {
  render: () => (
    <SafeSankeyStory title="Custom SVG" width={STORY_WIDTH} height={STORY_HEIGHT}>
      {(props) => <SankeySvg {...props} />}
    </SafeSankeyStory>
  ),
};


