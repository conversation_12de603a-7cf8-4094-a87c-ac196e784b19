import React from 'react';
import { useSnapshot } from 'valtio';
import { getDomainStore } from '@/api-client';
import type { DeCommonStatusValue, DeSettingsValue, DeCommonState } from '@/api-client';
import { CountdownTimer } from './CountdownTimer';

export interface CommonStatusWidgetProps {
  /** Custom className */
  className?: string;
  /** Custom style */
  style?: React.CSSProperties;
  /** Override timer diameter */
  timerDiameter?: number;
  /** Whether to show detailed info */
  showDetails?: boolean;
}

/**
 * CommonStatusWidget - Displays current auction status with animated countdown
 * 
 * Shows:
 * - Current auction state (Setup, Round Open, etc.)
 * - Round number and price
 * - Animated countdown timer with color coding
 * - Price direction indicator
 * 
 * Data comes from getDomainStore().de_auction.common_status and settings
 */
export const CommonStatusWidget: React.FC<CommonStatusWidgetProps> = ({
  className = '',
  style,
  timerDiameter = 80,
  showDetails = true,
}) => {
  const store = useSnapshot(getDomainStore());
  const commonStatus = store.de_auction?.common_status;
  const settings = store.de_auction?.settings;

  // If no data available, show placeholder
  if (!commonStatus || !settings) {
    return (
      <div className={`p-4 bg-gray-800 rounded-lg ${className}`} style={style}>
        <div className="text-center text-gray-400">
          <div className="text-sm">Auction Status</div>
          <div className="text-xs mt-1">No data available</div>
        </div>
      </div>
    );
  }

  // Get state-based styling
  const getStateColor = (state: DeCommonState) => {
    switch (state) {
      case 'SETUP':
        return 'text-red-400 bg-red-900/20';
      case 'STARTING_PRICE_ANNOUNCED':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'ROUND_OPEN':
        return 'text-green-400 bg-green-900/20';
      case 'ROUND_CLOSED':
        return 'text-orange-400 bg-orange-900/20';
      case 'AUCTION_CLOSED':
        return 'text-red-400 bg-red-900/20';
      default:
        return 'text-gray-400 bg-gray-900/20';
    }
  };

  const stateColorClass = getStateColor(commonStatus.common_state);

  // Format price with proper decimal places
  const formatPrice = (price: string) => {
    const num = parseFloat(price);
    if (isNaN(num)) return price;
    return num.toFixed(settings.price_decimal_places);
  };

  // Get price direction indicator
  const getPriceDirectionIcon = () => {
    if (!commonStatus.price_direction) return null;
    return commonStatus.price_direction === 'UP' ? '↗' : '↘';
  };

  return (
    <div 
      className={`p-4 bg-gray-900 rounded-lg border border-gray-700 ${className}`} 
      style={style}
    >
      {/* Header - Auction State */}
      <div className="text-center mb-4">
        <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${stateColorClass}`}>
          {commonStatus.common_state_text}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-between gap-4">
        {/* Left Side - Round & Price Info */}
        {showDetails && (
          <div className="flex-1 space-y-2">
            {/* Round Number */}
            <div className="text-center">
              <div className="text-xs text-gray-400 uppercase tracking-wide">Round</div>
              <div className="text-xl font-bold text-white">
                {commonStatus.round_number}
              </div>
            </div>

            {/* Current Price */}
            <div className="text-center">
              <div className="text-xs text-gray-400 uppercase tracking-wide">
                {settings.price_label}
              </div>
              <div className="flex items-center justify-center gap-1">
                <span className="text-lg font-mono text-white">
                  {formatPrice(commonStatus.round_price)}
                </span>
                {getPriceDirectionIcon() && (
                  <span className={`text-sm ${
                    commonStatus.price_direction === 'UP' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {getPriceDirectionIcon()}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Right Side - Countdown Timer */}
        <div className="flex-shrink-0">
          <CountdownTimer
            seconds={commonStatus.round_seconds}
            warningSecs={settings.round_orange_secs}
            dangerSecs={settings.round_red_secs}
            diameter={timerDiameter}
          />
        </div>
      </div>

      {/* Footer - Additional Info */}
      {showDetails && commonStatus.starting_time_text && (
        <div className="mt-4 pt-3 border-t border-gray-700">
          <div className="text-center text-xs text-gray-400">
            Started: {commonStatus.starting_time_text}
          </div>
        </div>
      )}
    </div>
  );
};

export default CommonStatusWidget;
