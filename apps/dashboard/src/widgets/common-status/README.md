# Common Status Widget

A React widget that displays the current auction status with an animated countdown timer. This widget shows the auction state, round information, current price, and time remaining with color-coded warnings.

## Features

- **Real-time countdown timer** with circular progress indicator
- **Color-coded states** based on time remaining:
  - Normal: Gray
  - Warning: Orange (when seconds >= `round_orange_secs`)
  - Danger: Red with pulse animation (when seconds >= `round_red_secs`)
- **Auction state display** with color-coded status badges
- **Round and price information** with direction indicators
- **Responsive design** with compact and full modes
- **Live data integration** using `getDomainStore()` pattern

## Components

### CommonStatusWidget

Main widget component that displays the complete auction status.

```tsx
import { CommonStatusWidget } from '@/widgets/common-status';

<CommonStatusWidget 
  timerDiameter={80}
  showDetails={true}
  className="my-custom-class"
/>
```

**Props:**
- `timerDiameter?: number` - Diameter of the countdown timer (default: 80)
- `showDetails?: boolean` - Whether to show round/price details (default: true)
- `className?: string` - Custom CSS classes
- `style?: React.CSSProperties` - Custom inline styles

### CountdownTimer

Standalone circular countdown timer component.

```tsx
import { CountdownTimer } from '@/widgets/common-status';

<CountdownTimer
  seconds={30}
  warningSecs={15}
  dangerSecs={30}
  diameter={60}
/>
```

**Props:**
- `seconds: number` - Current seconds remaining
- `warningSecs: number` - Warning threshold in seconds
- `dangerSecs: number` - Danger threshold in seconds
- `diameter?: number` - Diameter of the timer (default: 60)
- `showUnits?: boolean` - Whether to show "secs" label (default: true)
- `className?: string` - Custom CSS classes

## Data Sources

The widget automatically retrieves data from the domain store:

- **DeCommonStatusValue** (`store.de_auction.common_status`):
  - `common_state` - Current auction state (SETUP, ROUND_OPEN, etc.)
  - `common_state_text` - Human-readable state description
  - `round_number` - Current round number
  - `round_price` - Current round price
  - `round_seconds` - Seconds remaining in current round
  - `price_direction` - Price movement direction (UP/DOWN)
  - `starting_time_text` - Auction start time

- **DeSettingsValue** (`store.de_auction.settings`):
  - `price_label` - Price unit label (e.g., "Price ($/MWh)")
  - `price_decimal_places` - Number of decimal places for price display
  - `round_orange_secs` - Warning threshold in seconds
  - `round_red_secs` - Danger threshold in seconds

## State Colors

The widget uses different colors to indicate auction states:

- **SETUP**: Red background with red text
- **STARTING_PRICE_ANNOUNCED**: Yellow background with yellow text
- **ROUND_OPEN**: Green background with green text
- **ROUND_CLOSED**: Orange background with orange text
- **AUCTION_CLOSED**: Red background with red text

## Timer Colors

The countdown timer changes color based on time remaining:

- **Normal** (gray): `seconds < warningSecs`
- **Warning** (orange): `warningSecs <= seconds < dangerSecs`
- **Danger** (red + pulse): `seconds >= dangerSecs`

## Usage Examples

### Basic Usage

```tsx
// Simple status widget
<CommonStatusWidget />

// Compact version
<CommonStatusWidget 
  timerDiameter={60} 
  showDetails={false} 
/>

// Large version with custom styling
<CommonStatusWidget 
  timerDiameter={120}
  className="border-2 border-blue-500"
  style={{ maxWidth: '400px' }}
/>
```

### With Mock Data (for testing)

```tsx
import { setMockDomainStore } from '@/api-client';
import { createTest__DeAuctionValue } from '@/api-client/helpers/demo/DeAuctionValue.helper';

// Set up mock data
const mockAuction = createTest__DeAuctionValue();
mockAuction.common_status.round_seconds = 25;
mockAuction.common_status.common_state = DeCommonState.ROUND_OPEN;

setMockDomainStore({ de_auction: mockAuction });

// Widget will now display mock data
<CommonStatusWidget />
```

## Storybook Stories

The widget includes comprehensive Storybook stories for testing and documentation:

- **Default** - Standard widget with sample data
- **Setup** - Widget in setup state
- **Warning State** - Timer in warning (orange) state
- **Danger State** - Timer in danger (red) state with pulse
- **Auction Closed** - Widget showing closed auction
- **Compact** - Minimal version without details
- **Animated Countdown** - Live countdown demonstration
- **Timer Only** - Standalone timer components

## Demo Component

A full interactive demo is available at `/status` in the dashboard app:

```tsx
import { CommonStatusDemo } from '@/components/CommonStatusDemo';
```

The demo includes:
- Live countdown timer
- Interactive state controls
- Price direction toggles
- Round progression simulation
- Real-time value display

## Technical Notes

- Uses Valtio's `useSnapshot` for reactive state management
- Follows the `getDomainStore()` pattern for data access
- Supports both real and mock data for testing
- Fully typed with TypeScript
- Uses Tailwind CSS for styling
- Compatible with Storybook for documentation

## Inspiration

This widget is inspired by the Vue `DeClock` component from the original auction system, providing similar functionality with modern React patterns and improved UX.
