import React from 'react';

export interface CountdownTimerProps {
  /** Current seconds remaining */
  seconds: number;
  /** Warning threshold in seconds */
  warningSecs: number;
  /** Danger threshold in seconds */
  dangerSecs: number;
  /** Diameter of the circular timer */
  diameter?: number;
  /** Whether to show the "secs" label */
  showUnits?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * CountdownTimer - Circular countdown timer with color-coded states
 * 
 * Inspired by the Vue DeClock component, this shows a circular timer
 * that changes color based on warning and danger thresholds.
 * 
 * Colors:
 * - Normal: Gray
 * - Warning: Orange (when seconds >= warningSecs)
 * - Danger: Red with pulse animation (when seconds >= dangerSecs)
 */
export const CountdownTimer: React.FC<CountdownTimerProps> = ({
  seconds,
  warningSecs,
  dangerSecs,
  diameter = 60,
  showUnits = true,
  className = '',
}) => {
  // Determine color state
  const isWarning = seconds >= warningSecs && seconds < dangerSecs;
  const isDanger = seconds >= dangerSecs;

  // Calculate stroke color
  const getStrokeColor = () => {
    if (isDanger) return '#ef4444'; // red-500
    if (isWarning) return '#f97316'; // orange-500
    return '#6b7280'; // gray-500
  };

  return (
    <div
      className={`relative inline-flex items-center justify-center ${className}`}
      style={{
        width: diameter,
        height: diameter,
      }}
    >
      {/* SVG Circle */}
      <svg
        className="absolute inset-0 transform -scale-x-100"
        viewBox="0 0 50 50"
        width={diameter}
        height={diameter}
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="25"
          cy="25"
          r="19"
          fill="none"
          stroke={getStrokeColor()}
          strokeWidth="3"
          className={`transition-all duration-500 ease-in-out ${
            isDanger ? 'animate-pulse' : ''
          }`}
        />
      </svg>

      {/* Timer Value */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <div className="text-lg font-bold text-white leading-none">
          {seconds}
        </div>
        {showUnits && (
          <div className="text-xs text-gray-400 leading-none mt-0.5">
            secs
          </div>
        )}
      </div>
    </div>
  );
};

export default CountdownTimer;
