import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React, { useEffect, useState } from 'react';
import { CommonStatusWidget } from './CommonStatusWidget';
import { CountdownTimer } from './CountdownTimer';
import { setMockDomainStore, clearMockDomainStore } from '@/api-client';
import { createTest__DeAuctionValue } from '@/api-client/helpers/demo/DeAuctionValue.helper';
import { DeCommonState, PriceDirection } from '@/api-client';

// Wrapper component to handle mock data setup and cleanup with dark theme
const StoryWrapper: React.FC<{
  children: React.ReactNode;
  mockData: any;
}> = ({ children, mockData }) => {
  useEffect(() => {
    // Clear any existing mock data first
    clearMockDomainStore();

    // Set up new mock data
    const mockAuction = createTest__DeAuctionValue();
    if (mockData.common_status) {
      mockAuction.common_status = { ...mockAuction.common_status, ...mockData.common_status };
    }
    if (mockData.settings) {
      mockAuction.settings = { ...mockAuction.settings, ...mockData.settings };
    }

    setMockDomainStore({ de_auction: mockAuction });

    // Cleanup on unmount
    return () => {
      clearMockDomainStore();
    };
  }, [mockData]);

  return (
    <div className="dark bg-gray-950 p-4 min-h-[400px] flex items-center justify-center">
      {children}
    </div>
  );
};

const meta: Meta<typeof CommonStatusWidget> = {
  title: 'Widgets/CommonStatusWidget',
  component: CommonStatusWidget,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Displays current auction status with animated countdown timer. Shows auction state, round info, current price, and time remaining with color-coded warnings.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    timerDiameter: {
      control: { type: 'range', min: 40, max: 120, step: 10 },
    },
    showDetails: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CommonStatusWidget>;

export const Default: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.ROUND_OPEN,
          common_state_text: 'Round Open',
          round_number: 5,
          round_price: '125.75',
          round_seconds: 25,
          price_direction: PriceDirection.UP,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

export const Setup: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.SETUP,
          common_state_text: 'Setup',
          round_number: 0,
          round_price: '100.00',
          round_seconds: 0,
          price_direction: null,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

export const WarningState: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.ROUND_OPEN,
          common_state_text: 'Round Open',
          round_number: 3,
          round_price: '98.25',
          round_seconds: 20, // Warning level (orange_secs = 15)
          price_direction: PriceDirection.DOWN,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

export const DangerState: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.ROUND_OPEN,
          common_state_text: 'Round Open',
          round_number: 7,
          round_price: '110.50',
          round_seconds: 35, // Danger level (red_secs = 30)
          price_direction: PriceDirection.UP,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

export const AuctionClosed: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.AUCTION_CLOSED,
          common_state_text: 'Auction Closed',
          round_number: 10,
          round_price: '105.00',
          round_seconds: 0,
          price_direction: null,
          isClosed: true,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

export const Compact: Story = {
  args: {
    timerDiameter: 60,
    showDetails: false,
  },
  render: (args) => (
    <StoryWrapper
      mockData={{
        common_status: {
          common_state: DeCommonState.ROUND_OPEN,
          common_state_text: 'Round Open',
          round_number: 5,
          round_price: '125.75',
          round_seconds: 15,
          price_direction: PriceDirection.UP,
        },
      }}
    >
      <CommonStatusWidget {...args} />
    </StoryWrapper>
  ),
};

// Animated countdown demo - needs special handling for live updates
export const AnimatedCountdown: Story = {
  args: {
    timerDiameter: 100,
    showDetails: true,
  },
  render: (args) => {
    const AnimatedWrapper: React.FC = () => {
      const [seconds, setSeconds] = useState(45);

      // Update mock store when seconds change
      React.useEffect(() => {
        clearMockDomainStore();
        const mockAuction = createTest__DeAuctionValue();
        mockAuction.common_status = {
          ...mockAuction.common_status!,
          common_state: DeCommonState.ROUND_OPEN,
          common_state_text: 'Round Open',
          round_number: 5,
          round_price: '125.75',
          round_seconds: seconds,
          price_direction: PriceDirection.UP,
        };
        setMockDomainStore({ de_auction: mockAuction });
      }, [seconds]);

      // Countdown timer
      React.useEffect(() => {
        const interval = setInterval(() => {
          setSeconds(prev => {
            if (prev <= 0) return 45; // Reset to 45
            return prev - 1;
          });
        }, 1000);

        return () => {
          clearInterval(interval);
          clearMockDomainStore();
        };
      }, []);

      return (
        <div className="space-y-4">
          <CommonStatusWidget {...args} />
          <div className="text-center text-sm text-gray-400">
            Countdown resets every 45 seconds (Current: {seconds}s)
          </div>
        </div>
      );
    };

    return <AnimatedWrapper />;
  },
};

// Standalone CountdownTimer stories - no mock data needed
export const TimerOnly: Story = {
  render: () => {
    const TimerWrapper: React.FC = () => {
      const [seconds, setSeconds] = useState(30);

      React.useEffect(() => {
        const interval = setInterval(() => {
          setSeconds(prev => {
            if (prev <= 0) return 60;
            return prev - 1;
          });
        }, 1000);

        return () => clearInterval(interval);
      }, []);

      return (
        <div className="dark bg-gray-950 p-4 min-h-[400px] flex items-center justify-center">
          <div className="space-y-4">
            <div className="flex gap-4 items-center justify-center">
              <div className="text-center">
                <CountdownTimer seconds={seconds} warningSecs={15} dangerSecs={30} diameter={60} />
                <div className="text-xs text-gray-500 mt-2">Small (60px)</div>
              </div>
              <div className="text-center">
                <CountdownTimer seconds={seconds} warningSecs={15} dangerSecs={30} diameter={80} />
                <div className="text-xs text-gray-500 mt-2">Medium (80px)</div>
              </div>
              <div className="text-center">
                <CountdownTimer seconds={seconds} warningSecs={15} dangerSecs={30} diameter={100} />
                <div className="text-xs text-gray-500 mt-2">Large (100px)</div>
              </div>
            </div>
            <div className="text-center text-sm text-gray-400">
              Timer resets every 60 seconds (Current: {seconds}s)
            </div>
          </div>
        </div>
      );
    };

    return <TimerWrapper />;
  },
};

// Additional useful stories
export const NoData: Story = {
  args: {
    timerDiameter: 80,
    showDetails: true,
  },
  render: (args) => {
    // Don't set any mock data to test the "no data" state
    React.useEffect(() => {
      clearMockDomainStore();
    }, []);

    return (
      <div className="dark bg-gray-950 p-4 min-h-[400px] flex items-center justify-center">
        <CommonStatusWidget {...args} />
      </div>
    );
  },
};

export const AllStates: Story = {
  render: () => {
    const states = [
      {
        state: DeCommonState.SETUP,
        text: 'Setup',
        seconds: 0,
        price: '100.00',
        round: 0,
        direction: null,
      },
      {
        state: DeCommonState.STARTING_PRICE_ANNOUNCED,
        text: 'Starting Price Announced',
        seconds: 30,
        price: '100.00',
        round: 1,
        direction: null,
      },
      {
        state: DeCommonState.ROUND_OPEN,
        text: 'Round Open',
        seconds: 25,
        price: '125.75',
        round: 5,
        direction: PriceDirection.UP,
      },
      {
        state: DeCommonState.ROUND_CLOSED,
        text: 'Round Closed',
        seconds: 0,
        price: '130.25',
        round: 5,
        direction: PriceDirection.UP,
      },
      {
        state: DeCommonState.AUCTION_CLOSED,
        text: 'Auction Closed',
        seconds: 0,
        price: '135.00',
        round: 10,
        direction: null,
      },
    ];

    return (
      <div className="dark bg-gray-950 p-4 min-h-[600px]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {states.map((stateData, index) => (
            <StoryWrapper
              key={index}
              mockData={{
                common_status: {
                  common_state: stateData.state,
                  common_state_text: stateData.text,
                  round_number: stateData.round,
                  round_price: stateData.price,
                  round_seconds: stateData.seconds,
                  price_direction: stateData.direction,
                  isClosed: stateData.state === DeCommonState.AUCTION_CLOSED,
                },
              }}
            >
              <div className="text-center">
                <CommonStatusWidget timerDiameter={70} showDetails={true} />
                <div className="text-xs text-gray-500 mt-2">{stateData.text}</div>
              </div>
            </StoryWrapper>
          ))}
        </div>
      </div>
    );
  },
};
