# Dashboard Demo System

The dashboard uses a centralized demo registry system that makes it easy to add new demo components and automatically updates the navigation.

## Current Demo Structure

```
Dashboard Navigation: [Status Widget] [Round Table] [Auctioneer] [Users]
                           ↑ (newest demo appears first)
```

## How to Add a New Demo

### 1. Create Your Demo Component

Create your demo component in `src/components/`:

```tsx
// src/components/MyNewDemo.tsx
import React from 'react';

export const MyNewDemo: React.FC = () => {
  return (
    <div className="p-6 space-y-6 bg-gray-950 min-h-full">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">My New Demo</h1>
        {/* Your demo content */}
      </div>
    </div>
  );
};

export default MyNewDemo;
```

### 2. Register in Demo Registry

Add your demo to `src/demo-registry.ts` at the **beginning** of the `demoPages` array:

```tsx
// src/demo-registry.ts
import { MyNewDemo } from './components/MyNewDemo';

export const demoPages: DemoPage[] = [
  { 
    id: 'my-new-demo', 
    label: 'My Demo', 
    component: MyNewDemo,
    description: 'Description of what this demo shows'
  },
  // ... existing demos
];
```

### 3. That's It!

The navigation will automatically update with your new demo button, and it will appear first (leftmost) in the navigation bar.

## Demo Component Guidelines

### Layout Standards

- Use `min-h-full` instead of `min-h-screen` (the main app handles screen height)
- Use consistent padding: `p-6 space-y-6`
- Use dark theme colors: `bg-gray-950` for backgrounds
- Center content with: `max-w-4xl mx-auto`

### Example Template

```tsx
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const MyDemo: React.FC = () => {
  return (
    <div className="p-6 space-y-6 bg-gray-950 min-h-full">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Demo Title</h1>
        
        <Card className="p-6 bg-gray-900 border-gray-700">
          <h2 className="text-lg font-semibold text-white mb-4">Section</h2>
          {/* Demo content */}
        </Card>
        
        <Card className="p-6 bg-gray-900 border-gray-700">
          <h2 className="text-lg font-semibold text-white mb-4">Controls</h2>
          <div className="flex gap-2">
            <Button variant="default">Action</Button>
            <Button variant="outline">Secondary</Button>
          </div>
        </Card>
      </div>
    </div>
  );
};
```

## Current Demos

### 1. Status Widget (`status`)
- **Component**: `CommonStatusDemo`
- **Description**: Auction status widget with animated countdown timer
- **Features**: Interactive controls, live countdown, state changes

### 2. Round Table (`round-table`)
- **Component**: `DeRoundTableDemo`
- **Description**: Interactive round table with real-time updates and scrolling
- **Features**: 200×100 data grid, performance testing, scroll controls

### 3. Auctioneer (`auctioneer`)
- **Component**: `AuctioneerPage`
- **Description**: Auctioneer dashboard with various demo components
- **Features**: Demo selection, round table integration

### 4. Users (`users`)
- **Component**: `UserManagementPage`
- **Description**: User and company management interface
- **Features**: CRUD operations, AG-Grid tables, form dialogs

## Navigation Features

- **Thin navbar**: Minimal space usage
- **Small buttons**: Compact design
- **Tooltips**: Hover over buttons to see descriptions
- **Current description**: Shows description of active demo on larger screens
- **Auto-selection**: New demos automatically become the default

## Technical Details

### Demo Registry (`demo-registry.ts`)

```tsx
export type DemoPage = {
  id: string;           // Unique identifier
  label: string;        // Button text
  component: React.ComponentType;  // React component
  description?: string; // Optional description for tooltips
};
```

### App Structure (`App.tsx`)

```tsx
// Centralized navigation with automatic demo discovery
const currentPage = getDemoPage(currentPageId) || getDefaultDemoPage();
const CurrentComponent = currentPage.component;

return (
  <div className="min-h-screen flex flex-col bg-gray-950">
    <nav>{/* Auto-generated navigation */}</nav>
    <main className="flex-1">
      <CurrentComponent />
    </main>
  </div>
);
```

## Benefits

1. **Easy to add demos**: Just create component + register
2. **Automatic navigation**: No manual nav updates needed
3. **Consistent layout**: Standardized demo structure
4. **New demos first**: Latest work appears prominently
5. **Preserves existing demos**: Nothing gets lost
6. **Tooltips & descriptions**: Self-documenting interface

## Future Enhancements

- Add demo categories/grouping
- Search/filter demos
- Demo metadata (author, date, tags)
- Keyboard shortcuts for navigation
- Demo-specific URL routing
