import { useSnapshot } from 'valtio';
import { appStore } from '../store/AppStore';

/**
 * Hook to subscribe to app store changes
 * 
 * Uses valtio's useSnapshot to automatically re-render
 * when store data changes
 */
export function useAppStore() {
  const snapshot = useSnapshot(appStore);
  
  return {
    users: snapshot.users,
    companies: snapshot.companies,
    sessionUser: snapshot.session_user,
    time: snapshot.time
  };
}
