import { useMemo } from 'react';
import { createUserCompanyCommands } from '@/api-client';
import { dispatchCommand } from '../store/AppStore';

/**
 * Hook to get user and company command handlers
 *
 * This follows the CQRS pattern:
 * - Returns command dispatchers that send commands to server
 * - No direct state mutation
 * - Store updates come asynchronously from server
 */
export function useUserCompanyCommands() {
  return useMemo(() => {
    return createUserCompanyCommands(dispatchCommand);
  }, []);
}
