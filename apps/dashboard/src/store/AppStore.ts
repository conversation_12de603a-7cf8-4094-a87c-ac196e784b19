import { proxy } from 'valtio';
import { LiveClientStore, LiveClientStoreBuilder } from '@/api-client';

/**
 * Application Store using Valtio
 *
 * This follows the CQRS pattern:
 * - Store is immutable from client perspective
 * - Commands are dispatched to server
 * - Store updates come from server asynchronously
 * - UI subscribes to store changes via valtio
 */

// Create initial store with sample data
const initialStore = new LiveClientStoreBuilder()
  .withScenario('trader_logged_in')
  .withCompanies(5)
  .withTraders(8)
  .withAuctioneers(2)
  .build();

// Create valtio proxy store
export const appStore = proxy<LiveClientStore>(initialStore);

/**
 * Command dispatcher function
 * In a real app, this would send commands to the server via WebSocket
 * For demo purposes, we'll simulate server responses
 */
export function dispatchCommand(command: any): void {
  console.log('Dispatching command:', command);

  // In real app: send to server via WebSocket
  // Connector.publish(command);

  // For demo: simulate server processing and store updates
  simulateServerResponse(command);
}

/**
 * Simulate server processing commands and updating store
 * In real app, these updates would come from server via WebSocket
 */
function simulateServerResponse(command: any): void {
  // Simulate async server processing
  setTimeout(() => {
    const { simplename, command: cmd } = command;

    switch (simplename) {
      case 'UserSaveCommand':
        handleUserSave(cmd);
        break;
      case 'UserDeleteCommand':
        handleUserDelete(cmd);
        break;
      case 'CompanySaveCommand':
        handleCompanySave(cmd);
        break;
      case 'CompanyDeleteCommand':
        handleCompanyDelete(cmd);
        break;
      default:
        console.warn('Unknown command:', simplename);
    }
  }, 500); // Simulate network delay
}

function handleUserSave(cmd: any): void {
  const existingUserIndex = appStore.users.findIndex(u => u.user_id === cmd.user_id);

  if (existingUserIndex >= 0) {
    // Update existing user
    const existingUser = appStore.users[existingUserIndex];
    const company = appStore.companies.find(c => c.company_id === cmd.company_id);

    appStore.users[existingUserIndex] = {
      ...existingUser,
      username: cmd.username,
      email: cmd.email,
      phone: cmd.phone,
      role: cmd.role,
      company_id: cmd.company_id,
      company_shortname: company?.company_shortname || '',
      company_longname: company?.company_longname || '',
      // Update password only if provided
      ...(cmd.password && { password: cmd.password })
    };
  } else {
    // Create new user
    const company = appStore.companies.find(c => c.company_id === cmd.company_id);
    const newUser = {
      id: `user-${Date.now()}`,
      user_id: cmd.user_id,
      username: cmd.username,
      email: cmd.email,
      password: cmd.password,
      phone: cmd.phone,
      role: cmd.role,
      company_id: cmd.company_id,
      company_shortname: company?.company_shortname || '',
      company_longname: company?.company_longname || '',
      current_auction_id: null,
      has_connection_problem: false,
      isAuctioneer: cmd.role === 'AUCTIONEER',
      isObserver: false,
      isOnline: Math.random() > 0.3, // Random online status
      isTester: false,
      socket_state: null,
      socket_state_last_closed: null,
      termination_reason: null
    };

    appStore.users.push(newUser);
  }
}

function handleUserDelete(cmd: any): void {
  const userIndex = appStore.users.findIndex(u => u.user_id === cmd.user_id);
  if (userIndex >= 0) {
    appStore.users.splice(userIndex, 1);
  }
}

function handleCompanySave(cmd: any): void {
  const existingCompanyIndex = appStore.companies.findIndex(c => c.company_id === cmd.company_id);

  if (existingCompanyIndex >= 0) {
    // Update existing company
    appStore.companies[existingCompanyIndex] = {
      ...appStore.companies[existingCompanyIndex],
      company_shortname: cmd.company_shortname,
      company_longname: cmd.company_longname
    };

    // Update all users with this company
    appStore.users.forEach((user, index) => {
      if (user.company_id === cmd.company_id) {
        appStore.users[index] = {
          ...user,
          company_shortname: cmd.company_shortname,
          company_longname: cmd.company_longname
        };
      }
    });
  } else {
    // Create new company
    const newCompany = {
      id: `company-${Date.now()}`,
      company_id: cmd.company_id,
      company_shortname: cmd.company_shortname,
      company_longname: cmd.company_longname
    };

    appStore.companies.push(newCompany);
  }
}

function handleCompanyDelete(cmd: any): void {
  const companyIndex = appStore.companies.findIndex(c => c.company_id === cmd.company_id);
  if (companyIndex >= 0) {
    appStore.companies.splice(companyIndex, 1);

    // Note: In real app, server would handle user reassignment or prevent deletion
    // For demo, we'll just remove the company
  }
}
