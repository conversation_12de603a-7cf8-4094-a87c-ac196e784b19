import type { ColDef } from 'ag-grid-community';
import { themeA<PERSON>pine } from 'ag-grid-community';
import { BaseAgGrid } from '@/data-grids';
import type { UserElement } from '@/api-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';

interface ResponsiveUsersTableProps {
  users: readonly UserElement[];
  onUserSelect?: (user: UserElement) => void;
  onEditUser?: (user: UserElement) => void;
  onDeleteUser?: (user: UserElement) => void;
  height: number | string;
  width: number | string;
}

export function ResponsiveUsersTable({
  users,
  onUserSelect,
  onEditUser,
  onDeleteUser,
  height,
  width
}: ResponsiveUsersTableProps) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Define breakpoints
  const isMobile = windowWidth < 640;
  const isTablet = windowWidth < 1024;

  const columnDefs: ColDef<UserElement>[] = [
    {
      field: 'username',
      headerName: 'Username',
      sortable: true,
      filter: true,
      flex: 1,
      minWidth: 100,
    },
    {
      field: 'email',
      headerName: 'Email',
      sortable: true,
      filter: true,
      flex: 1.5,
      minWidth: 150,
      hide: isMobile, // Hide on mobile
    },
    {
      field: 'company_shortname',
      headerName: 'Company',
      sortable: true,
      filter: true,
      flex: 0.8,
      minWidth: 80,
    },
    {
      field: 'role',
      headerName: 'Role',
      sortable: true,
      filter: true,
      flex: 0.8,
      minWidth: 80,
      hide: isTablet && !isMobile, // Hide on tablets
      cellRenderer: (params: any) => {
        const role = params.value;
        return (
          <Badge
            variant={role === 'AUCTIONEER' ? 'default' : 'secondary'}
            className="text-xs"
          >
            {role.substring(0, 4)} {/* Abbreviated on mobile */}
          </Badge>
        );
      },
    },
    {
      field: 'isOnline',
      headerName: 'Status',
      sortable: true,
      filter: true,
      flex: 0.7,
      minWidth: 70,
      hide: isMobile, // Hide on mobile
      cellRenderer: (params: any) => {
        const isOnline = params.value;
        return (
          <Badge
            variant={isOnline ? 'default' : 'secondary'}
            className="text-xs"
          >
            {isOnline ? 'On' : 'Off'}
          </Badge>
        );
      },
    },
    {
      headerName: 'Actions',
      cellRenderer: (params: any) => {
        const user = params.data as UserElement;
        return (
          <div className="flex gap-1">
            {onEditUser && (
              <Button
                size="sm"
                variant="ghost"
                className="h-7 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditUser(user);
                }}
              >
                Edit
              </Button>
            )}
            {onDeleteUser && (
              <Button
                size="sm"
                variant="ghost"
                className="h-7 px-2 text-xs text-destructive hover:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteUser(user);
                }}
              >
                Delete
              </Button>
            )}
          </div>
        );
      },
      width: isMobile ? 100 : 120,
      maxWidth: 120,
      sortable: false,
      filter: false,
      resizable: false,
      pinned: 'right', // Pin actions to the right
    },
  ];

  // Create a custom theme with better styling
  const customTheme = themeAlpine.withParams({
    borderRadius: 6,
    headerHeight: 36,
    rowHeight: 42,
  });

  return (
    <BaseAgGrid<UserElement>
      rowData={[...users]}
      columnDefs={columnDefs}
      theme={customTheme}
      gridOptions={{
        rowSelection: { mode: "singleRow" },
        defaultColDef: {
          resizable: true,
          sortable: true,
        },
        suppressHorizontalScroll: false,
        suppressColumnVirtualisation: true,
      }}
      onSelectionChanged={(event) => {
        const selectedUser = event.api.getSelectedRows()[0];
        if (selectedUser && onUserSelect) {
          onUserSelect(selectedUser);
        }
      }}
      height={height}
      width={width}
      className="users-table"
    />
  );
}