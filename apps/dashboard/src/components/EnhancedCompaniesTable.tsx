import type { ColDef } from 'ag-grid-community';
import { themeAlpine } from 'ag-grid-community';
import { BaseAgGrid } from '@/data-grids';
import type { CompanyElement } from '@/api-client';
import { Button } from '@/components/ui/button';

interface EnhancedCompaniesTableProps {
  companies: readonly CompanyElement[];
  onCompanySelect?: (company: CompanyElement) => void;
  onEditCompany?: (company: CompanyElement) => void;
  onDeleteCompany?: (company: CompanyElement) => void;
  height: number | string;
  width: number | string;
}

export function EnhancedCompaniesTable({
  companies,
  onCompanySelect,
  onEditCompany,
  onDeleteCompany,
  height,
  width,
}: EnhancedCompaniesTableProps) {
  const columnDefs: ColDef<CompanyElement>[] = [
    {
      field: 'company_shortname',
      headerName: 'Short Name',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'company_longname',
      headerName: 'Long Name',
      sortable: true,
      filter: true,
      flex: 2,
    },
    {
      field: 'company_id',
      headerName: 'Company ID',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      headerName: 'Actions',
      cellRenderer: (params: any) => {
        const company = params.data as CompanyElement;
        return (
          <div className="flex gap-2">
            {onEditCompany && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditCompany(company);
                }}
              >
                Edit
              </Button>
            )}
            {onDeleteCompany && (
              <Button
                size="sm"
                variant="destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCompany(company);
                }}
              >
                Delete
              </Button>
            )}
          </div>
        );
      },
      width: 150,
      sortable: false,
      filter: false,
    },
  ];

  // Create a custom theme with better styling
  const customTheme = themeAlpine.withParams({
    borderRadius: 6,
    headerHeight: 40,
    rowHeight: 48,
  });

  return (
    <BaseAgGrid<CompanyElement>
      rowData={[...companies]}
      columnDefs={columnDefs}
      theme={customTheme}
      gridOptions={{
        rowSelection: { mode: "singleRow" },
      }}
      onSelectionChanged={(event) => {
        const selectedCompany = event.api.getSelectedRows()[0];
        if (selectedCompany && onCompanySelect) {
          onCompanySelect(selectedCompany);
        }
      }}
      height={height}
      width={width}
      className="companies-table"
    />
  );
}
