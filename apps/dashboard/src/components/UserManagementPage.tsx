import * as React from 'react';
import { EnhancedUsersTable } from './EnhancedUsersTable';
import { EnhancedCompaniesTable } from './EnhancedCompaniesTable';
import { UserFormDialog, type UserFormData } from './UserFormDialog';
import { CompanyFormDialog, type CompanyFormData } from './CompanyFormDialog';
import { useAppStore } from '../hooks/useAppStore';
import { useUserCompanyCommands } from '../hooks/useUserCompanyCommands';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import type { UserElement, CompanyElement } from '@/api-client';

export function UserManagementPage() {
  // Subscribe to store changes (valtio pattern)
  const { users, companies } = useAppStore();

  // Get command dispatchers (CQRS pattern)
  const commands = useUserCompanyCommands();

  // Modal states
  const [userFormOpen, setUserFormOpen] = React.useState(false);
  const [companyFormOpen, setCompanyFormOpen] = React.useState(false);
  const [editingUser, setEditingUser] = React.useState<UserElement | undefined>();
  const [editingCompany, setEditingCompany] = React.useState<CompanyElement | undefined>();
  const [deletingUser, setDeletingUser] = React.useState<UserElement | undefined>();
  const [deletingCompany, setDeletingCompany] = React.useState<CompanyElement | undefined>();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Calculate statistics
  const stats = React.useMemo(() => {
    const onlineUsers = users.filter(user => user.isOnline).length;
    const auctioneers = users.filter(user => user.role === 'AUCTIONEER').length;
    const traders = users.filter(user => user.role === 'TRADER').length;

    return {
      totalUsers: users.length,
      onlineUsers,
      totalCompanies: companies.length,
      auctioneers,
      traders
    };
  }, [users, companies]);

  // User handlers
  const handleCreateUser = async (data: UserFormData) => {
    setIsSubmitting(true);
    try {
      commands.users.createUser(data);
      setUserFormOpen(false);
      setEditingUser(undefined);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateUser = async (data: UserFormData) => {
    if (!editingUser) return;
    setIsSubmitting(true);
    try {
      commands.users.updateUser(editingUser, data);
      setUserFormOpen(false);
      setEditingUser(undefined);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditUser = (user: UserElement) => {
    setEditingUser(user);
    setUserFormOpen(true);
  };

  const handleDeleteUser = (user: UserElement) => {
    setDeletingUser(user);
  };

  const confirmDeleteUser = () => {
    if (deletingUser) {
      commands.users.deleteUser(deletingUser.user_id);
      setDeletingUser(undefined);
    }
  };

  // Company handlers
  const handleCreateCompany = async (data: CompanyFormData) => {
    setIsSubmitting(true);
    try {
      commands.companies.createCompany(data);
      setCompanyFormOpen(false);
      setEditingCompany(undefined);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateCompany = async (data: CompanyFormData) => {
    if (!editingCompany) return;
    setIsSubmitting(true);
    try {
      commands.companies.updateCompany(editingCompany, data);
      setCompanyFormOpen(false);
      setEditingCompany(undefined);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditCompany = (company: CompanyElement) => {
    setEditingCompany(company);
    setCompanyFormOpen(true);
  };

  const handleDeleteCompany = (company: CompanyElement) => {
    setDeletingCompany(company);
  };

  const confirmDeleteCompany = () => {
    if (deletingCompany) {
      commands.companies.deleteCompany(deletingCompany.company_id);
      setDeletingCompany(undefined);
    }
  };

  return (
    <div className="container mx-auto px-4 py-3 space-y-4 max-w-7xl">
      {/* Compact Statistics Bar */}
      <div className="bg-muted/50 rounded-lg p-3">
        <div className="flex flex-wrap gap-x-6 gap-y-2 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Users:</span>
            <span className="font-semibold">{stats.totalUsers}</span>
            <Badge variant="outline" className="text-xs ml-1">
              {stats.onlineUsers} online
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Companies:</span>
            <span className="font-semibold">{stats.totalCompanies}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Auctioneers:</span>
            <span className="font-semibold">{stats.auctioneers}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Traders:</span>
            <span className="font-semibold">{stats.traders}</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="companies">Company Management</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">User Management</h2>
            <Button onClick={() => setUserFormOpen(true)}>
              Create User
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <EnhancedUsersTable
                users={users}
                onEditUser={handleEditUser}
                onDeleteUser={handleDeleteUser}
                height="calc(100vh - 280px)"
                width="100%"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="companies" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Company Management</h2>
            <Button onClick={() => setCompanyFormOpen(true)}>
              Create Company
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <EnhancedCompaniesTable
                companies={companies}
                onEditCompany={handleEditCompany}
                onDeleteCompany={handleDeleteCompany}
                height="calc(100vh - 280px)"
                width="100%"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* User Form Dialog */}
      <UserFormDialog
        isOpen={userFormOpen}
        onClose={() => {
          setUserFormOpen(false);
          setEditingUser(undefined);
        }}
        user={editingUser}
        companies={companies}
        onSubmit={editingUser ? handleUpdateUser : handleCreateUser}
        isSubmitting={isSubmitting}
      />

      {/* Company Form Dialog */}
      <CompanyFormDialog
        isOpen={companyFormOpen}
        onClose={() => {
          setCompanyFormOpen(false);
          setEditingCompany(undefined);
        }}
        company={editingCompany}
        onSubmit={editingCompany ? handleUpdateCompany : handleCreateCompany}
        isSubmitting={isSubmitting}
      />

      {/* Delete User Confirmation */}
      <Dialog open={!!deletingUser} onOpenChange={() => setDeletingUser(undefined)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete user <strong>{deletingUser?.username}</strong>?</p>
          <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeletingUser(undefined)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteUser}>
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Company Confirmation */}
      <Dialog open={!!deletingCompany} onOpenChange={() => setDeletingCompany(undefined)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Company</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete company <strong>{deletingCompany?.company_shortname}</strong>?</p>
          <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeletingCompany(undefined)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCompany}>
              Delete Company
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
