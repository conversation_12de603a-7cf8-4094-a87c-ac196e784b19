import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import type { CompanyElement } from '@/api-client';

export interface CompanyFormData {
  company_shortname: string;
  company_longname: string;
}

interface CompanyFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  company?: CompanyElement; // If provided, form is in edit mode
  onSubmit: (data: CompanyFormData) => void;
  isSubmitting?: boolean;
}

export function CompanyFormDialog({
  isOpen,
  onClose,
  company,
  onSubmit,
  isSubmitting = false,
}: CompanyFormDialogProps) {
  const isEditMode = Boolean(company);

  const [formData, setFormData] = React.useState<CompanyFormData>({
    company_shortname: company?.company_shortname || '',
    company_longname: company?.company_longname || ''
  });

  // Reset form when company changes
  React.useEffect(() => {
    setFormData({
      company_shortname: company?.company_shortname || '',
      company_longname: company?.company_longname || ''
    });
  }, [company]);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof CompanyFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit Company' : 'Create New Company'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="shortname" className="text-sm font-medium">
              Company Short Name *
            </label>
            <Input
              id="shortname"
              value={formData.company_shortname}
              onChange={(e) => handleInputChange('company_shortname', e.target.value)}
              required
              disabled={isSubmitting}
              placeholder="e.g., ACME"
            />
            <p className="text-xs text-muted-foreground">
              Short identifier for the company
            </p>
          </div>

          <div className="space-y-2">
            <label htmlFor="longname" className="text-sm font-medium">
              Company Long Name *
            </label>
            <Input
              id="longname"
              value={formData.company_longname}
              onChange={(e) => handleInputChange('company_longname', e.target.value)}
              required
              disabled={isSubmitting}
              placeholder="e.g., ACME Corporation Ltd."
            />
            <p className="text-xs text-muted-foreground">
              Full legal name of the company
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (isEditMode ? 'Update Company' : 'Create Company')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
