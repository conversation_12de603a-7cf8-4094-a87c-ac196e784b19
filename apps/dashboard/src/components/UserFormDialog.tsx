import * as React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import type { UserElement, CompanyElement, AuUserRole } from '@/api-client';
import { AuUserRole as UserRole } from '@/api-client';

export interface UserFormData {
  username: string;
  email: string;
  password: string;
  phone: string;
  role: AuUserRole;
  company_id: string;
}

interface UserFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  user?: UserElement; // If provided, form is in edit mode
  companies: readonly CompanyElement[];
  onSubmit: (data: UserFormData) => void;
  isSubmitting?: boolean;
}

export function UserFormDialog({
  isOpen,
  onClose,
  user,
  companies,
  onSubmit,
  isSubmitting = false,
}: UserFormDialogProps) {
  const isEditMode = Boolean(user);

  const [formData, setFormData] = React.useState<UserFormData>({
    username: user?.username || '',
    email: user?.email || '',
    password: '', // Always empty for security
    phone: user?.phone || '',
    role: user?.role || UserRole.TRADER,
    company_id: user?.company_id || ''
  });

  // Reset form when user changes
  React.useEffect(() => {
    setFormData({
      username: user?.username || '',
      email: user?.email || '',
      password: '',
      phone: user?.phone || '',
      role: user?.role || UserRole.TRADER,
      company_id: user?.company_id || ''
    });
  }, [user]);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit User' : 'Create New User'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="username" className="text-sm font-medium">
                Username *
              </label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                required
                disabled={isSubmitting}
                placeholder="Enter username"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                Email *
              </label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                disabled={isSubmitting}
                placeholder="Enter email address"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">
                {isEditMode ? "New Password" : "Password *"}
              </label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required={!isEditMode}
                disabled={isSubmitting}
                placeholder={isEditMode ? "Leave blank to keep current" : "Enter password"}
              />
              {isEditMode && (
                <p className="text-xs text-muted-foreground">
                  Leave blank to keep current password
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="phone" className="text-sm font-medium">
                Phone
              </label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={isSubmitting}
                placeholder="Enter phone number"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="role" className="text-sm font-medium">
                Role *
              </label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleInputChange('role', value as AuUserRole)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={UserRole.TRADER}>Trader</SelectItem>
                  <SelectItem value={UserRole.AUCTIONEER}>Auctioneer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="company" className="text-sm font-medium">
                Company *
              </label>
              <Select
                value={formData.company_id}
                onValueChange={(value) => handleInputChange('company_id', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a company" />
                </SelectTrigger>
                <SelectContent>
                  {companies.map((company) => (
                    <SelectItem key={company.company_id} value={company.company_id}>
                      {company.company_shortname} - {company.company_longname}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (isEditMode ? 'Update User' : 'Create User')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
