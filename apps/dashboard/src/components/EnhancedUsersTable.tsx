import type { ColDef } from 'ag-grid-community';
import { themeA<PERSON>pine } from 'ag-grid-community';
import { BaseAgGrid } from '@/data-grids';
import type { UserElement } from '@/api-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface EnhancedUsersTableProps {
  users: readonly UserElement[];
  onUserSelect?: (user: UserElement) => void;
  onEditUser?: (user: UserElement) => void;
  onDeleteUser?: (user: UserElement) => void;
  height: number | string;
  width: number | string;
}

export function EnhancedUsersTable({
  users,
  onUserSelect,
  onEditUser,
  onDeleteUser,
  height,
  width
}: EnhancedUsersTableProps) {
  const columnDefs: ColDef<UserElement>[] = [
    {
      field: 'username',
      headerName: 'Username',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'email',
      headerName: 'Email',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'company_shortname',
      headerName: 'Company',
      sortable: true,
      filter: true,
      flex: 1,
    },
    {
      field: 'role',
      headerName: 'Role',
      sortable: true,
      filter: true,
      flex: 1,
      cellRenderer: (params: any) => {
        const role = params.value;
        return (
          <Badge variant={role === 'AUCTIONEER' ? 'default' : 'secondary'}>
            {role}
          </Badge>
        );
      },
    },
    {
      field: 'isOnline',
      headerName: 'Status',
      sortable: true,
      filter: true,
      flex: 1,
      cellRenderer: (params: any) => {
        const isOnline = params.value;
        return (
          <Badge variant={isOnline ? 'default' : 'secondary'}>
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
        );
      },
    },
    {
      headerName: 'Actions',
      cellRenderer: (params: any) => {
        const user = params.data as UserElement;
        return (
          <div className="flex gap-2">
            {onEditUser && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditUser(user);
                }}
              >
                Edit
              </Button>
            )}
            {onDeleteUser && (
              <Button
                size="sm"
                variant="destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteUser(user);
                }}
              >
                Delete
              </Button>
            )}
          </div>
        );
      },
      width: 150,
      sortable: false,
      filter: false,
    },
  ];

  // Create a custom theme with better styling
  const customTheme = themeAlpine.withParams({
    borderRadius: 6,
    headerHeight: 40,
    rowHeight: 48,
  });

  return (
    <BaseAgGrid<UserElement>
      rowData={[...users]}
      columnDefs={columnDefs}
      theme={customTheme}
      gridOptions={{
        rowSelection: { mode: "singleRow" },
      }}
      onSelectionChanged={(event) => {
        const selectedUser = event.api.getSelectedRows()[0];
        if (selectedUser && onUserSelect) {
          onUserSelect(selectedUser);
        }
      }}
      height={height}
      width={width}
      className="users-table"
    />
  );
}
