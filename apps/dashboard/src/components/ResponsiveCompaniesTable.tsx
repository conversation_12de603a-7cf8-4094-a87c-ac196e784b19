import type { ColDef } from 'ag-grid-community';
import { themeAlpine } from 'ag-grid-community';
import { BaseAgGrid } from '@/data-grids';
import type { CompanyElement } from '@/api-client';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';

interface ResponsiveCompaniesTableProps {
  companies: readonly CompanyElement[];
  onCompanySelect?: (company: CompanyElement) => void;
  onEditCompany?: (company: CompanyElement) => void;
  onDeleteCompany?: (company: CompanyElement) => void;
  height: number | string;
  width: number | string;
}

export function ResponsiveCompaniesTable({
  companies,
  onCompanySelect,
  onEditCompany,
  onDeleteCompany,
  height,
  width
}: ResponsiveCompaniesTableProps) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Define breakpoints
  const isMobile = windowWidth < 640;
  const isTablet = windowWidth < 1024;

  const columnDefs: ColDef<CompanyElement>[] = [
    {
      field: 'company_shortname',
      headerName: 'Short Name',
      sortable: true,
      filter: true,
      flex: 1,
      minWidth: 80,
    },
    {
      field: 'company_longname',
      headerName: 'Long Name',
      sortable: true,
      filter: true,
      flex: 2,
      minWidth: 150,
      hide: isMobile, // Hide on mobile
    },
    {
      field: 'company_id',
      headerName: 'Company ID',
      sortable: true,
      filter: true,
      flex: 0.5,
      minWidth: 70,
      hide: isTablet, // Hide on tablets and mobile
    },
    {
      headerName: 'Actions',
      cellRenderer: (params: any) => {
        const company = params.data as CompanyElement;
        return (
          <div className="flex gap-1">
            {onEditCompany && (
              <Button
                size="sm"
                variant="ghost"
                className="h-7 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditCompany(company);
                }}
              >
                Edit
              </Button>
            )}
            {onDeleteCompany && (
              <Button
                size="sm"
                variant="ghost"
                className="h-7 px-2 text-xs text-destructive hover:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCompany(company);
                }}
              >
                Delete
              </Button>
            )}
          </div>
        );
      },
      width: isMobile ? 100 : 120,
      maxWidth: 120,
      sortable: false,
      filter: false,
      resizable: false,
      pinned: 'right', // Pin actions to the right
    },
  ];

  // Create a custom theme with better styling
  const customTheme = themeAlpine.withParams({
    borderRadius: 6,
    headerHeight: 36,
    rowHeight: 42,
  });

  return (
    <BaseAgGrid<CompanyElement>
      rowData={[...companies]}
      columnDefs={columnDefs}
      theme={customTheme}
      gridOptions={{
        rowSelection: { mode: "singleRow" },
        defaultColDef: {
          resizable: true,
          sortable: true,
        },
        suppressHorizontalScroll: false,
        suppressColumnVirtualisation: true,
      }}
      onSelectionChanged={(event) => {
        const selectedCompany = event.api.getSelectedRows()[0];
        if (selectedCompany && onCompanySelect) {
          onCompanySelect(selectedCompany);
        }
      }}
      height={height}
      width={width}
      className="companies-table"
    />
  );
}