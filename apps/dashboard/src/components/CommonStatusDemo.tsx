import React, { useEffect, useState } from 'react';
import { CommonStatusWidget } from '@/widgets/common-status';
import { setMockDomainStore, clearMockDomainStore } from '@/api-client';
import { createTest__DeAuctionValue } from '@/api-client/helpers/demo/DeAuctionValue.helper';
import { DeCommonState, PriceDirection } from '@/api-client';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

/**
 * CommonStatusDemo - Interactive demo of the CommonStatusWidget
 *
 * This component demonstrates the CommonStatusWidget with:
 * - Live countdown timer
 * - Different auction states
 * - Price direction changes
 * - Interactive controls
 */
export const CommonStatusDemo: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [seconds, setSeconds] = useState(30);
  const [currentState, setCurrentState] = useState<DeCommonState>(DeCommonState.ROUND_OPEN);
  const [priceDirection, setPriceDirection] = useState<PriceDirection>(PriceDirection.UP);
  const [roundNumber, setRoundNumber] = useState(5);
  const [price, setPrice] = useState(125.75);

  // Set up mock data
  useEffect(() => {
    const mockAuction = createTest__DeAuctionValue();
    mockAuction.common_status = {
      ...mockAuction.common_status!,
      common_state: currentState,
      common_state_text: getStateText(currentState),
      round_number: roundNumber,
      round_price: price.toFixed(2),
      round_seconds: seconds,
      price_direction: priceDirection,
    };

    setMockDomainStore({ de_auction: mockAuction });

    return () => clearMockDomainStore();
  }, [seconds, currentState, priceDirection, roundNumber, price]);

  // Countdown timer
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setSeconds(prev => {
        if (prev <= 0) {
          // Simulate round progression
          setRoundNumber(r => r + 1);
          setPriceDirection(prev => prev === PriceDirection.UP ? PriceDirection.DOWN : PriceDirection.UP);
          setPrice(p => p + (Math.random() - 0.5) * 10);
          return 45; // Reset to 45 seconds
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning]);

  const getStateText = (state: DeCommonState): string => {
    switch (state) {
      case DeCommonState.SETUP: return 'Setup';
      case DeCommonState.STARTING_PRICE_ANNOUNCED: return 'Starting Price Announced';
      case DeCommonState.ROUND_OPEN: return 'Round Open';
      case DeCommonState.ROUND_CLOSED: return 'Round Closed';
      case DeCommonState.AUCTION_CLOSED: return 'Auction Closed';
      default: return 'Unknown';
    }
  };

  const states = [
    DeCommonState.SETUP,
    DeCommonState.STARTING_PRICE_ANNOUNCED,
    DeCommonState.ROUND_OPEN,
    DeCommonState.ROUND_CLOSED,
    DeCommonState.AUCTION_CLOSED,
  ];

  return (
    <div className="p-6 space-y-6 bg-gray-950 min-h-full">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Common Status Widget Demo</h1>

        {/* Main Widget Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="p-6 bg-gray-900 border-gray-700">
            <h2 className="text-lg font-semibold text-white mb-4">Full Widget</h2>
            <CommonStatusWidget timerDiameter={100} showDetails={true} />
          </Card>

          <Card className="p-6 bg-gray-900 border-gray-700">
            <h2 className="text-lg font-semibold text-white mb-4">Compact Widget</h2>
            <CommonStatusWidget timerDiameter={80} showDetails={false} />
          </Card>
        </div>

        {/* Controls */}
        <Card className="p-6 bg-gray-900 border-gray-700">
          <h2 className="text-lg font-semibold text-white mb-4">Controls</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Timer Controls */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Timer</label>
              <div className="space-y-2">
                <Button
                  onClick={() => setIsRunning(!isRunning)}
                  variant={isRunning ? "destructive" : "default"}
                  className="w-full"
                >
                  {isRunning ? 'Stop' : 'Start'} Timer
                </Button>
                <Button
                  onClick={() => setSeconds(45)}
                  variant="outline"
                  className="w-full"
                >
                  Reset to 45s
                </Button>
              </div>
            </div>

            {/* State Controls */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Auction State</label>
              <div className="space-y-1">
                {states.map(state => (
                  <Button
                    key={state}
                    onClick={() => setCurrentState(state)}
                    variant={currentState === state ? "default" : "outline"}
                    size="sm"
                    className="w-full text-xs"
                  >
                    {getStateText(state)}
                  </Button>
                ))}
              </div>
            </div>

            {/* Price Controls */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Price Direction</label>
              <div className="space-y-2">
                <Button
                  onClick={() => setPriceDirection(PriceDirection.UP)}
                  variant={priceDirection === PriceDirection.UP ? "default" : "outline"}
                  className="w-full"
                >
                  ↗ Up
                </Button>
                <Button
                  onClick={() => setPriceDirection(PriceDirection.DOWN)}
                  variant={priceDirection === PriceDirection.DOWN ? "default" : "outline"}
                  className="w-full"
                >
                  ↘ Down
                </Button>
              </div>
            </div>

            {/* Round Controls */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Round & Price</label>
              <div className="space-y-2">
                <Button
                  onClick={() => setRoundNumber(r => r + 1)}
                  variant="outline"
                  className="w-full"
                >
                  Next Round
                </Button>
                <Button
                  onClick={() => setPrice(p => p + 5)}
                  variant="outline"
                  className="w-full"
                >
                  Price +$5
                </Button>
                <Button
                  onClick={() => setPrice(p => p - 5)}
                  variant="outline"
                  className="w-full"
                >
                  Price -$5
                </Button>
              </div>
            </div>
          </div>

          {/* Current Values Display */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Seconds:</span>
                <span className="ml-2 text-white font-mono">{seconds}</span>
              </div>
              <div>
                <span className="text-gray-400">Round:</span>
                <span className="ml-2 text-white font-mono">{roundNumber}</span>
              </div>
              <div>
                <span className="text-gray-400">Price:</span>
                <span className="ml-2 text-white font-mono">${price.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-gray-400">Direction:</span>
                <span className="ml-2 text-white">{priceDirection}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CommonStatusDemo;
