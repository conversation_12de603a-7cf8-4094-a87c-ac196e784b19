import * as React from 'react';
import { Button } from '@/components/ui/button';

export interface AdminLayoutProps {
  children: React.ReactNode;
  currentPage: 'users' | 'auctioneer' | 'status';
  onPageChange: (page: 'users' | 'auctioneer' | 'status') => void;
}

export function AdminLayout({ children, currentPage, onPageChange }: AdminLayoutProps): React.JSX.Element {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-1">AU25 Admin Dashboard</h1>
          <p className="text-gray-600">User & Company Management</p>
        </div>
      </header> */}

      {/* Navigation Bar */}
      <nav className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="max-w-7xl mx-auto flex items-center gap-4">
          <span className="text-sm font-medium text-gray-700">Page:</span>
          <div className="flex gap-2">
            <Button
              variant={currentPage === 'status' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPageChange('status')}
            >
              Status Widget
            </Button>
            <Button
              variant={currentPage === 'auctioneer' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPageChange('auctioneer')}
            >
              Auctioneer
            </Button>
            <Button
              variant={currentPage === 'users' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPageChange('users')}
            >
              Users
            </Button>
          </div>
        </div>
      </nav>

      <main className="flex-1 w-full">
        {children}
      </main>

      <footer className="bg-white border-t border-gray-200 px-6 py-4 text-center">
        <p className="text-sm text-gray-600">AU25 Auction Platform - Admin Dashboard</p>
      </footer>
    </div>
  );
}

AdminLayout.displayName = "AdminLayout";
