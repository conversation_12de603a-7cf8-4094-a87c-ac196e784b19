import * as React from 'react';
import { DeRoundTableDemo } from './DeRoundTableDemo';

export function AuctioneerPage() {
  const [activeDemo, setActiveDemo] = React.useState<'round-table' | null>('round-table');

  return (
    <div className="container mx-auto px-4 py-6 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Auctioneer Dashboard</h1>
        <div className="text-sm text-gray-500">
          Demo Components & Testing Tools
        </div>
      </div>

      {/* Demo Selection */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Demos</h2>
        <div className="flex gap-3">
          <button
            onClick={() => setActiveDemo('round-table')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeDemo === 'round-table'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Round Table
          </button>
          {/* Future demo buttons can be added here */}
        </div>
      </div>

      {/* Demo Content */}
      {activeDemo === 'round-table' && <DeRoundTableDemo />}
    </div>
  );
}
