import * as React from 'react';
import { DeRoundTable, useBlinkContext, BlinkProvider } from '@/data-grids';
import {
  setMockDomainStore,
  LiveClientStoreBuilder,
  OrderType,
  OrderSubmissionType,
  PriceDirection,
  DeRoundElement,
  DeRoundTraderElement,
  getDomainStore,
  createTest__DeRoundElement,
  createTest__DeRoundTraderElement
} from '@/api-client';

// Inner component that has access to blink context
function DeRoundTableDemoInner() {
  const blinkContext = useBlinkContext();
  const [gridApi, setGridApi] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [dataReady, setDataReady] = React.useState(false);
  const [currentRound, setCurrentRound] = React.useState(100); // Track current round number

  // Performance tracking state
  const [performanceMetrics, setPerformanceMetrics] = React.useState({
    initialLoadTime: 0,
    dataCreationTime: 0,
    gridRenderTime: 0,
    lastUpdateTime: 0,
    totalUpdates: 0,
    averageUpdateTime: 0,
    totalCells: 0,
    populatedCells: 0,
    fillRate: 0
  });

  // Set up mock store with substantial data for testing scrolling
  React.useEffect(() => {
    console.log('Setting up mock store for rounds table...');
    setIsLoading(true);

    // Start performance measurement
    const startTime = performance.now();

    // Create dataset with 200 traders × 100 rounds for performance testing
    const mockStore = new LiveClientStoreBuilder()
      .withScenario('auction_in_progress')
      .withCompanies(200) // 200 companies for vertical scrolling
      .withTraders(200)   // All companies have traders
      .withAuctioneers(1)
      .withDeAuctionConfig({ maxRound: 20 }) // Start with 20 fully populated rounds
      .build();

    const dataCreationTime = performance.now();
    console.log('Initial mock store created:', mockStore);
    console.log(`Data creation time: ${(dataCreationTime - startTime).toFixed(2)}ms`);

    // Enhance the blotter with more rounds and varied data
    if (mockStore.de_auction?.blotter) {
      const blotter = mockStore.de_auction.blotter;

      // Debug: Check initial state
      console.log('=== INITIAL BLOTTER STATE ===');
      console.log(`Initial rounds: ${blotter.rounds.length}`);
      console.log(`Initial traders: ${blotter.traders.length}`);
      console.log(`Initial round_traders: ${blotter.round_traders.length}`);
      console.log('Initial rounds:', blotter.rounds.map(r => r.round_number));
      console.log('Initial round_traders by round:', blotter.round_traders.reduce((acc, rt) => {
        acc[rt.round] = (acc[rt.round] || 0) + 1;
        return acc;
      }, {} as Record<number, number>));
      console.log('================================');

      // Extend to 100 rounds for horizontal scrolling
      const additionalRounds = [] as DeRoundElement[];
      const additionalRoundTraders = [] as DeRoundTraderElement[];

      for (let roundNum = blotter.rounds.length + 1; roundNum <= 100; roundNum++) {
        // Create round with realistic price progression
        const basePrice = 100;
        const priceIncrement = roundNum * 2.5;
        const roundPrice = basePrice + priceIncrement;

        const round = createTest__DeRoundElement(roundNum, {
          round_price_str: roundPrice.toFixed(2),
          round_direction: roundNum % 3 === 0 ? PriceDirection.UP : PriceDirection.DOWN,
          buy_quantity: Math.floor(Math.random() * 1000) + 100,
          sell_quantity: Math.floor(Math.random() * 1000) + 100,
          matched: Math.floor(Math.random() * 500) + 50,
          excess_indicator: Math.random() > 0.7 ? (Math.floor(Math.random() * 200) - 100).toString() : "0"
        });

        additionalRounds.push(round);

        // Create round trader elements for EVERY trader in this round (100% population)
        blotter.traders.forEach((trader) => {
          const quantity = Math.floor(Math.random() * 500) + 50;
          const isBuy = Math.random() > 0.5;

          // Find the actual company element for this trader
          const company = mockStore.companies.find(c => c.company_id === trader.company_id);
          if (!company) {
            console.error(`No company found for trader ${trader.company_id}`);
            return;
          }

          const roundTrader = createTest__DeRoundTraderElement(roundNum, company, {
            quantity_int: quantity,
            quantity_str: quantity.toString(),
            order_type: isBuy ? OrderType.BUY : OrderType.SELL,
            order_submission_type: Math.random() > 0.8 ?
              OrderSubmissionType.MANUAL : OrderSubmissionType.DEFAULT
          });

          additionalRoundTraders.push(roundTrader);
        });
      }

      // Add the additional rounds and round traders
      blotter.rounds.push(...additionalRounds);
      blotter.round_traders.push(...additionalRoundTraders);

      // Log detailed cell population statistics
      const initialRoundTraders = blotter.round_traders.length - additionalRoundTraders.length;
      const totalCells = blotter.traders.length * blotter.rounds.length;
      const populatedCells = blotter.round_traders.length;
      const fillRate = (populatedCells / totalCells * 100).toFixed(1);

      console.log('=== CELL POPULATION STATISTICS ===');
      console.log(`Total traders: ${blotter.traders.length}`);
      console.log(`Total rounds: ${blotter.rounds.length}`);
      console.log(`Total possible cells: ${totalCells}`);
      console.log(`Initial round traders (rounds 1-${blotter.rounds.length - additionalRounds.length}): ${initialRoundTraders}`);
      console.log(`Additional round traders (rounds ${blotter.rounds.length - additionalRounds.length + 1}-100): ${additionalRoundTraders.length}`);
      console.log(`Total populated cells: ${populatedCells}`);
      console.log(`Overall fill rate: ${fillRate}%`);
      console.log('=====================================');

      // Update common status to reflect current round
      if (mockStore.de_auction.common_status) {
        mockStore.de_auction.common_status.round_number = 100;
        mockStore.de_auction.common_status.round_price = "350.00";
      }
    }

    // Set the mock store
    const storeSetTime = performance.now();
    setMockDomainStore(mockStore);
    const storeSetEndTime = performance.now();

    console.log('Mock store set with 200 traders × 100 rounds');
    console.log(`Store setting time: ${(storeSetEndTime - storeSetTime).toFixed(2)}ms`);
    console.log('Blotter data:', mockStore.de_auction?.blotter);
    console.log('Rounds count:', mockStore.de_auction?.blotter?.rounds?.length);
    console.log('Traders count:', mockStore.de_auction?.blotter?.traders?.length);

    // Calculate total initial load time
    const totalLoadTime = storeSetEndTime - startTime;

    // Update performance metrics
    setPerformanceMetrics(prev => ({
      ...prev,
      initialLoadTime: totalLoadTime,
      dataCreationTime: dataCreationTime - startTime,
      gridRenderTime: 0, // Will be measured when grid is ready
      totalCells: mockStore.de_auction?.blotter ?
        mockStore.de_auction.blotter.traders.length * mockStore.de_auction.blotter.rounds.length : 0,
      populatedCells: mockStore.de_auction?.blotter?.round_traders?.length || 0,
      fillRate: mockStore.de_auction?.blotter ?
        (mockStore.de_auction.blotter.round_traders.length /
         (mockStore.de_auction.blotter.traders.length * mockStore.de_auction.blotter.rounds.length) * 100) : 0
    }));

    // Mark data as ready and stop loading
    setIsLoading(false);
    setDataReady(true);

    // Enable blinks after initial load (suppress during load to avoid entire table blinking)
    setTimeout(() => {
      blinkContext.setSuppressBlinks(false);
      console.log('Blinks enabled after initial load');
    }, 2000);

    console.log(`Total initial load time: ${totalLoadTime.toFixed(2)}ms`);

    // Verify the store is accessible
    setTimeout(() => {
      const verifyStore = getDomainStore();
      console.log('Verification - getDomainStore():', verifyStore);
      console.log('Verification - blotter exists:', !!verifyStore.de_auction?.blotter);
      console.log('Verification - rounds:', verifyStore.de_auction?.blotter?.rounds?.length);
      console.log('Verification - traders:', verifyStore.de_auction?.blotter?.traders?.length);
    }, 1000);
  }, []);

  // Timer to change trader online status and data every second - only start when data is ready
  React.useEffect(() => {
    if (!dataReady) return; // Don't start timer until data is ready

    const changeDataAndStatus = () => {
      // Start timing the update operation
      const updateStartTime = performance.now();

      // Get current domain store and modify it instead of creating new one
      const domainStore = getDomainStore();

      if (domainStore.users && domainStore.de_auction?.blotter) {
        const blotter = domainStore.de_auction.blotter;

        // 1. Change trader online status (based on au21-frontend implementation)
        const numUsersToChange = Math.floor(Math.random() * 5) + 1; // Change 1-5 users

        for (let i = 0; i < numUsersToChange; i++) {
          const randomIndex = Math.floor(Math.random() * domainStore.users.length);
          const user = domainStore.users[randomIndex];
          if (user) {
            // Randomly change online status and connection problems
            const wasOnline = user.isOnline;
            user.isOnline = Math.random() > 0.2; // 80% chance to be online
            user.has_connection_problem = Math.random() < 0.1; // 10% chance of connection problems

            // Set current auction if online
            if (user.isOnline && !user.has_connection_problem) {
              user.current_auction_id = domainStore.de_auction.auction_id;
            } else {
              user.current_auction_id = null;
            }

            if (wasOnline !== user.isOnline) {
              console.log(`User ${user.username}: ${user.isOnline ? 'ONLINE' : 'OFFLINE'}${user.has_connection_problem ? ' (CONNECTION PROBLEM)' : ''}`);
            }
          }
        }

        // 2. Change 50% of cells in the LAST ROUND ONLY for blinker effects
        const currentRound = blotter.rounds.length; // Last round number
        const numCellsToChange = Math.floor(blotter.traders.length * 0.5); // 50% of traders

        console.log(`Attempting to update ${numCellsToChange} cells (50%) in round ${currentRound}`);
        let updatedCount = 0;

        // Randomly select 50% of traders to update
        const tradersToUpdate = blotter.traders
          .sort(() => Math.random() - 0.5) // Shuffle
          .slice(0, numCellsToChange); // Take first 50%

        tradersToUpdate.forEach(trader => {
          // Find round trader element for the CURRENT (last) round
          let roundTrader = blotter.round_traders.find(rt =>
            rt.round === currentRound && rt.cid === trader.company_id
          );

          if (roundTrader) {
            // Change quantity to trigger blinker
            const newQuantity = Math.floor(Math.random() * 500) + 50;
            const newOrderType = Math.random() > 0.5 ? OrderType.BUY : OrderType.SELL;

            roundTrader.quantity_int = newQuantity;
            roundTrader.quantity_str = newQuantity.toString();
            roundTrader.order_type = newOrderType;
            updatedCount++;

            // Trigger blink effect
            const cellId = `${trader.company_id}-${currentRound}`;
            blinkContext.addBlinkingCell(cellId);

            console.log(`Blinker: ${trader.shortname} Round ${currentRound} → ${newQuantity} (${newOrderType})`);
          } else {
            console.log(`No round trader found for ${trader.shortname} in round ${currentRound}`);
          }
        });

        // 3. Flash trader online status for random 25% of traders
        const tradersToFlash = blotter.traders
          .sort(() => Math.random() - 0.5) // Shuffle
          .slice(0, Math.floor(blotter.traders.length * 0.25)); // Take 25%

        tradersToFlash.forEach(trader => {
          blinkContext.addBlinkingTrader(trader.company_id);
        });

        console.log(`Successfully updated ${updatedCount} out of ${numCellsToChange} attempted cells in round ${currentRound}`);
        console.log(`Flashed ${tradersToFlash.length} trader online status indicators`);
      }

      // Calculate update time and update metrics
      const updateEndTime = performance.now();
      const updateTime = updateEndTime - updateStartTime;

      setPerformanceMetrics(prev => {
        const newTotalUpdates = prev.totalUpdates + 1;
        const newAverageUpdateTime = ((prev.averageUpdateTime * prev.totalUpdates) + updateTime) / newTotalUpdates;

        return {
          ...prev,
          lastUpdateTime: updateTime,
          totalUpdates: newTotalUpdates,
          averageUpdateTime: newAverageUpdateTime
        };
      });

      console.log(`Update completed in ${updateTime.toFixed(2)}ms`);

      // Schedule next change using setTimeout (better than setInterval for cleanup)
      setTimeout(changeDataAndStatus, 1000); // Every 1 second
    };

    // Start the timer after initial data is loaded
    const timeoutId = setTimeout(changeDataAndStatus, 2000); // Start after 2 seconds

    return () => {
      clearTimeout(timeoutId);
    };
  }, [dataReady]); // Depend on dataReady so timer starts when data is loaded

  // Scroll functions using AG Grid API
  const scrollToRow = React.useCallback((rowIndex: number, position: 'top' | 'middle' | 'bottom' = 'top') => {
    if (gridApi) {
      console.log(`Scrolling to row ${rowIndex} at position ${position}`);
      gridApi.ensureIndexVisible(rowIndex, position);
    } else {
      console.warn('Grid API not available for scrolling');
    }
  }, [gridApi]);

  const scrollToColumn = React.useCallback((colKey: string, position: 'auto' | 'start' | 'middle' | 'end' = 'auto') => {
    if (gridApi) {
      console.log(`Scrolling to column ${colKey} at position ${position}`);
      gridApi.ensureColumnVisible(colKey, position);
    } else {
      console.warn('Grid API not available for scrolling');
    }
  }, [gridApi]);

  // Add Round function
  const addNewRound = React.useCallback(() => {
    const domainStore = getDomainStore();

    if (domainStore.de_auction?.blotter) {
      const blotter = domainStore.de_auction.blotter;
      const newRoundNumber = currentRound + 1;

      // Create new round
      const basePrice = 100;
      const priceIncrement = newRoundNumber * 2.5;
      const roundPrice = basePrice + priceIncrement;

      const newRound = createTest__DeRoundElement(newRoundNumber, {
        round_price_str: roundPrice.toFixed(2),
        round_direction: newRoundNumber % 3 === 0 ? PriceDirection.UP : PriceDirection.DOWN,
        buy_quantity: Math.floor(Math.random() * 1000) + 100,
        sell_quantity: Math.floor(Math.random() * 1000) + 100,
        matched: Math.floor(Math.random() * 500) + 50,
        excess_indicator: Math.random() > 0.7 ? (Math.floor(Math.random() * 200) - 100).toString() : "0"
      });

      // Create round trader elements for ALL traders in the new round
      const newRoundTraders: DeRoundTraderElement[] = [];
      blotter.traders.forEach((trader) => {
        const quantity = Math.floor(Math.random() * 500) + 50;
        const isBuy = Math.random() > 0.5;

        // Find the actual company element for this trader
        const company = domainStore.companies.find(c => c.company_id === trader.company_id);
        if (!company) {
          console.error(`No company found for trader ${trader.company_id}`);
          return;
        }

        const roundTrader = createTest__DeRoundTraderElement(newRoundNumber, company, {
          quantity_int: quantity,
          quantity_str: quantity.toString(),
          order_type: isBuy ? OrderType.BUY : OrderType.SELL,
          order_submission_type: Math.random() > 0.8 ?
            OrderSubmissionType.MANUAL : OrderSubmissionType.DEFAULT
        });

        newRoundTraders.push(roundTrader);
      });

      // Add to blotter
      blotter.rounds.push(newRound);
      blotter.round_traders.push(...newRoundTraders);

      // Update current round state
      setCurrentRound(newRoundNumber);

      // Update common status
      if (domainStore.de_auction.common_status) {
        domainStore.de_auction.common_status.round_number = newRoundNumber;
        domainStore.de_auction.common_status.round_price = roundPrice.toFixed(2);
      }

      // Auto-scroll to the new round
      setTimeout(() => {
        scrollToColumn(`round-${newRoundNumber}`, 'end');
      }, 100);

      console.log(`Added new round ${newRoundNumber} with ${newRoundTraders.length} traders`);
    }
  }, [currentRound, scrollToColumn]);

  // Auto-scroll to current round when it changes (similar to Vue @Watch)
  React.useEffect(() => {
    if (gridApi && currentRound) {
      setTimeout(() => {
        scrollToColumn(`round-${currentRound}`, 'end');
      }, 100);
    }
  }, [currentRound, gridApi, scrollToColumn]);

  // Auto-scroll to last round when grid is first ready
  React.useEffect(() => {
    if (gridApi && dataReady) {
      setTimeout(() => {
        console.log('Auto-scrolling to last round on initial load');
        scrollToColumn(`round-${currentRound}`, 'end');
      }, 500); // Longer delay for initial load
    }
  }, [gridApi, dataReady, currentRound, scrollToColumn]);

  // Show loading state while data is being prepared
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-3 space-y-4 max-w-7xl">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-lg text-gray-600">Loading 200 traders × 100 rounds...</p>
            <p className="text-sm text-gray-500">This may take a moment</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-3 space-y-4 max-w-7xl">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900">DeRoundTable Demo</h2>
          <div className="text-sm text-gray-600">
            200 Traders × {currentRound} Rounds - Performance Testing
            {dataReady && <span className="ml-2 text-green-600">✓ Data Ready</span>}
            <span className="ml-2 text-blue-600">Current Round: {currentRound}</span>
          </div>
        </div>

        {/* Table and Controls Layout */}
        <div className="flex gap-4">
          {/* Table */}
          <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <DeRoundTable
              height={600} // Fixed height to force vertical scrolling
              buyMax={500}
              sellMax={500}
              selectedRound={currentRound} // Current round selected
              onSelectedRoundChange={(round) => console.log('Selected round:', round)}
              onCompanyClick={(companyId) => console.log('Company clicked:', companyId)}
              onGridReady={(api) => setGridApi(api)} // Capture grid API for scrolling
            />
          </div>

          {/* Scroll Controls - Vertical Stack on the Right */}
          <div className="w-48 bg-gray-50 rounded-lg p-4 space-y-3">
            <h3 className="text-lg font-semibold text-gray-900 text-center">Scroll Controls</h3>
            <div className="space-y-2">
              <button
                onClick={() => scrollToRow(0)}
                className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                First Row
              </button>
              <button
                onClick={() => scrollToRow(199)}
                className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Last Row
              </button>
              <button
                onClick={() => scrollToRow(100, 'middle')}
                className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Middle Row
              </button>
              <button
                onClick={() => scrollToRow(Math.floor(Math.random() * 200))}
                className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Random Row
              </button>

              <div className="border-t pt-2 mt-3">
                <button
                  onClick={() => scrollToColumn('round-1')}
                  className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm mb-2"
                >
                  First Round
                </button>
                <button
                  onClick={() => scrollToColumn(`round-${currentRound}`)}
                  className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm mb-2"
                >
                  Current Round ({currentRound})
                </button>
                <button
                  onClick={() => scrollToColumn('round-50', 'middle')}
                  className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm mb-2"
                >
                  Middle Round
                </button>
                <button
                  onClick={() => scrollToColumn(`round-${Math.floor(Math.random() * currentRound) + 1}`)}
                  className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm mb-2"
                >
                  Random Round
                </button>

                <div className="border-t pt-2 mt-3">
                  <button
                    onClick={addNewRound}
                    className="w-full px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm font-semibold"
                  >
                    ➕ Add Round {currentRound + 1}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-yellow-50 rounded-lg p-4 space-y-3">
          <h3 className="text-lg font-semibold text-yellow-900">Performance Metrics</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-white rounded p-3">
              <h4 className="font-semibold text-yellow-800 mb-2">Initial Load</h4>
              <div className="space-y-1 text-sm">
                <div>Data Creation: <span className="font-mono">{performanceMetrics.dataCreationTime.toFixed(2)}ms</span></div>
                <div>Total Load: <span className="font-mono">{performanceMetrics.initialLoadTime.toFixed(2)}ms</span></div>
                <div>Grid Render: <span className="font-mono">{performanceMetrics.gridRenderTime.toFixed(2)}ms</span></div>
              </div>
            </div>
            <div className="bg-white rounded p-3">
              <h4 className="font-semibold text-yellow-800 mb-2">Updates</h4>
              <div className="space-y-1 text-sm">
                <div>Last Update: <span className="font-mono">{performanceMetrics.lastUpdateTime.toFixed(2)}ms</span></div>
                <div>Average: <span className="font-mono">{performanceMetrics.averageUpdateTime.toFixed(2)}ms</span></div>
                <div>Total Updates: <span className="font-mono">{performanceMetrics.totalUpdates}</span></div>
              </div>
            </div>
            <div className="bg-white rounded p-3">
              <h4 className="font-semibold text-yellow-800 mb-2">Dataset</h4>
              <div className="space-y-1 text-sm">
                <div>Grid Size: <span className="font-mono">200×100</span></div>
                <div>Total Cells: <span className="font-mono">{performanceMetrics.totalCells.toLocaleString()}</span></div>
                <div>Populated: <span className="font-mono">{performanceMetrics.populatedCells.toLocaleString()}</span></div>
                <div>Fill Rate: <span className="font-mono">{performanceMetrics.fillRate.toFixed(1)}%</span></div>
                <div className={`font-semibold ${performanceMetrics.initialLoadTime > 1500 ? 'text-red-600' : performanceMetrics.initialLoadTime > 1000 ? 'text-yellow-600' : 'text-green-600'}`}>
                  {performanceMetrics.initialLoadTime > 1500 ? '⚠️ Slow' : performanceMetrics.initialLoadTime > 1000 ? '⚡ Moderate' : '✅ Fast'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Testing Information */}
        <div className="bg-blue-50 rounded-lg p-4 space-y-3">
          <h3 className="text-lg font-semibold text-blue-900">Round Table Functionality Testing</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Currently Testing:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-blue-700">
                <li>✅ Vertical scrolling (200 trader rows)</li>
                <li>✅ Horizontal scrolling (100 rounds)</li>
                <li>✅ Pinned price row (top) and totals (bottom)</li>
                <li>✅ Pinned company names column (left)</li>
                <li>✅ Round selection (click headers)</li>
                <li>✅ Company selection (click names)</li>
                <li>✅ Trader connection status changes (every 1s)</li>
                <li>✅ Cell content blinker effects (every 1s)</li>
                <li>✅ Programmatic scrolling (AG Grid API)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Additional Features to Test:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-blue-700">
                <li>🔄 Order type visualization (BUY/SELL bars)</li>
                <li>🔄 Price direction indicators (UP/DOWN arrows)</li>
                <li>🔄 Excess demand indicators</li>
                <li>🔄 Order submission type indicators</li>
                <li>🔄 Sorting by trader name vs quantity</li>
                <li>🔄 Row height variations (footer vs normal)</li>
                <li>🔄 Cell hover effects</li>
                <li>🔄 Responsive column widths</li>
                <li>🔄 Performance with large datasets</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Wrapper component that provides the DeRoundTable with blink context
export function DeRoundTableDemo() {
  return (
    <BlinkProvider>
      <DeRoundTableDemoInner />
    </BlinkProvider>
  );
}
