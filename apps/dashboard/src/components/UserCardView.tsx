import type { UserElement } from '@/api-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface UserCardViewProps {
  users: readonly UserElement[];
  onEditUser?: (user: UserElement) => void;
  onDeleteUser?: (user: UserElement) => void;
}

export function UserCardView({
  users,
  onEditUser,
  onDeleteUser,
}: UserCardViewProps) {
  return (
    <div className="space-y-3 p-4">
      {users.map((user) => (
        <Card key={user.id} className="overflow-hidden">
          <CardContent className="p-4">
            <div className="flex justify-between items-start gap-4">
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{user.username}</h3>
                  <Badge
                    variant={user.isOnline ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {user.isOnline ? 'Online' : 'Offline'}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{user.email}</p>
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-medium">{user.company_shortname}</span>
                  <span className="text-muted-foreground">•</span>
                  <Badge
                    variant={user.role === 'AUCTIONEER' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {user.role}
                  </Badge>
                </div>
              </div>
              <div className="flex flex-col gap-1">
                {onEditUser && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onEditUser(user)}
                  >
                    Edit
                  </Button>
                )}
                {onDeleteUser && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-destructive hover:text-destructive"
                    onClick={() => onDeleteUser(user)}
                  >
                    Delete
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}