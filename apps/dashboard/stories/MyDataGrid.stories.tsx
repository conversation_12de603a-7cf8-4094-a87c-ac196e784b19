import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MyDataGrid } from '@/data-grids';
import type { ColDef } from 'ag-grid-community'; // Import ColDef

const meta: Meta<typeof MyDataGrid> = {
  title: 'Data Grid/MyDataGrid',
  component: MyDataGrid,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MyDataGrid>;

// Sample data for the grid
const sampleData = [
  { id: 1, name: 'Product 1', price: 100, category: 'Electronics' },
  { id: 2, name: 'Product 2', price: 200, category: 'Clothing' },
  { id: 3, name: 'Product 3', price: 300, category: 'Electronics' },
  { id: 4, name: 'Product 4', price: 400, category: 'Books' },
  { id: 5, name: 'Product 5', price: 500, category: 'Electronics' },
];

// Define column definitions for the sample data
const sampleColDefs: ColDef[] = [
  { field: 'id', headerName: 'ID', width: 90 },
  { field: 'name', headerName: 'Product Name', sortable: true, filter: true },
  { field: 'price', headerName: 'Price', valueFormatter: p => '$' + p.value.toLocaleString(), sortable: true },
  { field: 'category', headerName: 'Category', sortable: true, filter: true },
];

// Basic story with default data
export const Default: Story = {
  args: {
    rowData: sampleData,
    columnDefs: sampleColDefs, // Pass columnDefs
    width: 800,
    height: 400
  },
};

// Story with empty data
export const Empty: Story = {
  args: {
    rowData: [],
    columnDefs: sampleColDefs, // Pass columnDefs even for empty data (for headers)
    width: 800,
    height: 300
  },
};

// Story with selection callback
export const WithSelection: Story = {
  args: {
    rowData: sampleData,
    columnDefs: sampleColDefs, // Pass columnDefs
    width: 800,
    height: 400,
    onRowSelected: (event: any) => { // AG Grid typically passes an event object
      console.log('Selected row data:', event.data);
    },
  },
};
