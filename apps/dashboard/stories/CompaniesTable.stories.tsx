import type { Meta, StoryObj } from '@storybook/react';
import { CompaniesTable } from '@/data-grids';
import { createTest__CompanyElement, type CompanyElement } from '@/api-client';

const meta: Meta<typeof CompaniesTable> = {
  title: 'Data Grid/CompaniesTable',
  component: CompaniesTable,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // Define argTypes for props if needed, e.g., for controls
    // companies: { control: 'object' },
    height: { control: 'text' }, // Or 'number'
    width: { control: 'text' },  // Or 'number'
  },
};

export default meta;

type Story = StoryObj<typeof CompaniesTable>;

// Helper function to generate test company data
const generateCompanyTestData = (numCompanies: number = 3): CompanyElement[] => {
  const companyNames = [
    { short: 'ACMECorp', long: 'ACME Corporation Ltd.' },
    { short: 'TechSol', long: 'Technology Solutions Inc.' },
    { short: 'GlobalEnt', long: 'Global Enterprises LLC' },
    { short: 'InnovInc', long: 'Innovative Industries Co.' },
    { short: 'NextGen', long: 'Next Generation Systems' },
  ];

  const companies: CompanyElement[] = [];
  for (let i = 0; i < numCompanies; i++) {
    const nameIndex = i % companyNames.length;
    companies.push(
      createTest__CompanyElement(i + 1, {
        company_shortname: companyNames[nameIndex].short,
        company_longname: companyNames[nameIndex].long,
      })
    );
  }
  return companies;
};

// Basic story with default data
export const Default: Story = {
  args: {
    companies: generateCompanyTestData(),
    height: 500,
    width: 600, // Adjusted width slightly for company names
  },
};

// Story with more companies
export const ManyCompanies: Story = {
  args: {
    companies: generateCompanyTestData(10),
    height: 600,
    width: 600,
  },
};

// Story with no data (to show empty state)
export const Empty: Story = {
  args: {
    companies: [],
    height: 300,
    width: 600,
  },
};

// Story with specific company data
export const SpecificCompanies: Story = {
  args: {
    companies: [
      createTest__CompanyElement(101, { company_shortname: 'AlphaCo', company_longname: 'Alpha Company Inc.' }),
      createTest__CompanyElement(102, { company_shortname: 'BetaSys', company_longname: 'Beta Systems LLC' }),
    ],
    height: 300,
    width: 600,
  },
};
