import type { Meta, StoryObj } from '@storybook/react';
import { UsersTable } from '@/data-grids';
import { createTest__CompanyElement, createTest__UserElement, AuUserRole, type UserElement } from '@/api-client';

const meta: Meta<typeof UsersTable> = {
  title: 'Data Grid/UsersTable',
  component: UsersTable,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof UsersTable>;

// Helper function to generate test data
const generateTestData = (numUsers: number = 5) => {
  // Create some test companies
  const companies = [
    createTest__CompanyElement(1, { company_shortname: 'ACME Corp' }),
    createTest__CompanyElement(2, { company_shortname: 'TechCorp' }),
    createTest__CompanyElement(3, { company_shortname: 'StartupX' }),
  ];

  // Create users
  const users: UserElement[] = [];
  for (let i = 0; i < numUsers; i++) {
    const companyIndex = i % companies.length;
    const company = companies[companyIndex];
    const role = i === 0 ? AuUserRole.AUCTIONEER : AuUserRole.TRADER; // First user is an auctioneer
    users.push(createTest__UserElement(i + 1, company, { role }));
  }

  return users;
};

// Basic story with default data
export const Default: Story = {
  args: {
    users: generateTestData(),
    height: 500,
    width: 800
  },
};

// Story with more users
export const ManyUsers: Story = {
  args: {
    users: generateTestData(10),
    height: 600,
    width: 800
  },
};

// Story with selection callback
export const WithSelection: Story = {
  args: {
    users: generateTestData(),
    onUserSelect: (user) => {
      console.log('Selected user:', user);
    },
    height: 500,
    width: 800
  },
};

// Story with no users
export const Empty: Story = {
  args: {
    users: [],
    height: 400,
    width: 800
  },
};
