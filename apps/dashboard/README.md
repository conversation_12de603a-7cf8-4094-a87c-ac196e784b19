# Admin Dashboard - User & Company Management

A comprehensive admin dashboard demonstrating the User & Company Management organism from `@repo/ui`.

## Features

This dashboard showcases all the functionality requested:

### User Management
- ✅ **CRUD Operations**: Create, read, update, delete users
- ✅ **Role Management**: Assign AUCTIONEER or TRADER roles
- ✅ **Company Assignment**: Assign users to companies
- ✅ **Bulk Operations**: Bulk delete and bulk assign users to companies
- ✅ **User Table**: Sortable, filterable table with user selection

### Company Management
- ✅ **CRUD Operations**: Create, read, update, delete companies
- ✅ **User Assignment View**: See which users are assigned to each company
- ✅ **Deletion Protection**: Prevent deletion of companies with assigned users

### Dashboard Elements
- ✅ **Summary Cards**: Total users, companies, online users, role breakdown
- ✅ **Real-time Updates**: Statistics update as data changes
- ✅ **Tabbed Interface**: Clean separation between user and company management

### Assignment Management
- ✅ **Visual Assignment**: See user-company relationships
- ✅ **Assignment Interface**: Easy assignment/reassignment of users
- ✅ **Bulk Assignment**: Assign multiple users to a company at once

## Architecture

This app demonstrates the **CQRS (Command Query Responsibility Segregation)** pattern:

### Commands (Write Side)
- Commands are dispatched via `useUserCompanyCommands()` hook
- Commands are sent to server (simulated in this demo)
- No direct state mutation from UI

### Queries (Read Side)
- UI subscribes to valtio store via `useAppStore()` hook
- Store updates come from server asynchronously
- UI automatically re-renders when store changes

### Key Files
- `src/store/AppStore.ts` - Valtio store with simulated server responses
- `src/hooks/useUserCompanyCommands.ts` - Command dispatchers
- `src/hooks/useAppStore.ts` - Store subscription
- `src/App.tsx` - Main app with UserCompanyManagement organism

## Running the App

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The app will be available at `http://localhost:3001`

## Usage

1. **User Management Tab**:
   - View all users in the sortable/filterable table
   - Click "Create User" to add new users
   - Select users to edit, delete, or bulk assign
   - Use bulk operations for multiple users

2. **Company Management Tab**:
   - View all companies in the table
   - Click "Create Company" to add new companies
   - Select a company to see assigned users
   - Edit or delete companies (deletion blocked if users assigned)

3. **Statistics**:
   - View real-time statistics in the top cards
   - Statistics update automatically as you make changes

## Demo Data

The app starts with sample data:
- 5 companies
- 8 traders
- 2 auctioneers
- Realistic user/company relationships

## CQRS Pattern Notes

This app strictly follows CQRS principles:
- ❌ No direct API calls or request/response patterns
- ✅ Commands dispatched to server
- ✅ Store updates from server
- ✅ UI subscribes to store changes
- ✅ Async command processing with simulated delays

The simulated server responses in `AppStore.ts` demonstrate how real server updates would work.
