# Technical Context - Admin Dashboard

## Technology Stack
- **Framework:** React 18 with TypeScript
- **Build Tool:** Vite
- **State Management:** Valtio (proxy-based state)
- **UI Components:** Custom components with Tailwind CSS
- **Data Grid:** AG-Grid Community Edition
- **UI Library:** shadcn/ui components
- **HTTP Client:** TBD
- **Routing:** None (single page currently)

## Architecture Patterns

### State Management (CQRS)
Uses Command Query Responsibility Segregation pattern:
- **Commands:** User actions that modify state (create, update, delete)
- **Queries:** Reading state from Valtio store
- **Store:** Centralized state in `AppStore.ts`

### Component Structure
```
components/
├── ui/                    # shadcn/ui base components
├── AdminLayout.tsx        # Layout wrapper
├── Enhanced*Table.tsx     # AG-Grid table wrappers
├── *FormDialog.tsx        # Modal forms
└── UserCardView.tsx       # Alternative view (not used)
```

### Key Dependencies
- `@repo/api-client` - Shared types and interfaces
- `@repo/data-grid` - AG-Grid wrapper component
- `valtio` - State management
- `ag-grid-react` - Data grid

## Data Flow
1. User actions trigger commands via `useUserCompanyCommands` hook
2. Commands update Valtio store
3. Components re-render automatically due to Valtio proxy
4. AG-Grid handles its own internal state for sorting/filtering

## API Integration
Currently using mock data. Real API integration points:
- `UserSaveCommand` - Create/update users
- `UserDeleteCommand` - Delete users  
- `CompanySaveCommand` - Create/update companies
- `CompanyDeleteCommand` - Delete companies

## Environment
- Development server: `http://localhost:3001`
- No environment variables currently required