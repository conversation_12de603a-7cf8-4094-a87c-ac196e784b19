# Architectural Decisions - Admin Dashboard

## 2025-01-22: Compact Statistics Display

**Decision:** Replace card-based statistics with inline stats bar

**Rationale:**
- Cards consumed ~20% of vertical screen space
- Statistics are secondary information, not primary content
- Inline display maintains visibility while maximizing table space

**Trade-offs:**
- Less visual prominence for statistics
- More space for primary content (tables)

---

## 2025-01-22: Remove Company Users Sidebar

**Decision:** Remove the sidebar showing users for selected company

**Rationale:**
- Feature was consuming 33% of horizontal space in company view
- Low value feature - users can see company assignment in users table
- Simplifies UI and improves table readability

**Trade-offs:**
- Lost ability to quickly see all users in a company
- Could be replaced with a filter in the future

---

## Initial: Valtio for State Management

**Decision:** Use Valtio instead of Redux/Zustand

**Rationale:**
- Simpler API with proxy-based reactivity
- Less boilerplate than Redux
- Automatic re-renders without selectors
- Good TypeScript support

**Trade-offs:**
- Less ecosystem support than Redux
- Proxy-based system may have edge cases

---

## Initial: AG-Grid for Tables

**Decision:** Use AG-Grid instead of building custom tables

**Rationale:**
- Enterprise-grade features out of the box
- Excellent performance with large datasets
- Built-in sorting, filtering, selection
- Consistent behavior across browsers

**Trade-offs:**
- Larger bundle size
- Learning curve for customization
- Some features require paid license