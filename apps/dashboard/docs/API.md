# API Documentation - Admin Dashboard

## Overview
The Admin Dashboard communicates with the backend via a command-based API pattern.

## Commands

### User Commands

#### UserSaveCommand
Creates or updates a user.

**Payload:**
```typescript
{
  user_id?: number;      // Omit for create, include for update
  username: string;
  email: string;
  password?: string;     // Required for create, optional for update
  phone?: string;
  role: 'TRADER' | 'AUCTIONEER';
  company_id: number;
}
```

#### UserDeleteCommand
Deletes a user.

**Payload:**
```typescript
{
  user_id: number;
}
```

### Company Commands

#### CompanySaveCommand
Creates or updates a company.

**Payload:**
```typescript
{
  company_id?: number;   // Omit for create, include for update
  company_shortname: string;
  company_longname: string;
}
```

#### CompanyDeleteCommand
Deletes a company.

**Payload:**
```typescript
{
  company_id: number;
}
```

## Data Types

### UserElement
```typescript
interface UserElement {
  id: string;
  user_id: number;
  username: string;
  email: string;
  phone?: string;
  role: 'TRADER' | 'AUCTIONEER';
  company_id: number;
  company_shortname: string;
  isOnline: boolean;
}
```

### CompanyElement
```typescript
interface CompanyElement {
  id: string;
  company_id: number;
  company_shortname: string;
  company_longname: string;
}
```

## Response Handling
Currently using optimistic updates - the UI updates immediately while commands are dispatched.
Error handling to be implemented.