# Product Requirements Document - Admin Dashboard

## Product Overview
An administrative dashboard for managing users and companies within the AU25 auction platform.

## Target Users
- System administrators
- Auction platform operators
- Support staff

## Core Requirements

### User Management
- **View** all users in a searchable, sortable table
- **Create** new users with required fields:
  - Username
  - Email
  - Password
  - Role (Trader/Auctioneer)
  - Company assignment
- **Edit** existing user details
- **Delete** users with confirmation
- **Filter** by role, status, company

### Company Management
- **View** all companies in a table
- **Create** new companies with:
  - Short name
  - Long name
  - Company ID
- **Edit** company details
- **Delete** companies with confirmation

### Statistics Dashboard
- Display real-time counts:
  - Total users (with online count)
  - Total companies
  - Auctioneers count
  - Traders count

## User Experience Requirements
- Clean, professional interface
- Responsive design for various screen sizes
- Fast load times
- Clear feedback for all actions
- Confirmation dialogs for destructive actions

## Technical Constraints
- Must work in modern browsers (Chrome, Firefox, Safari, Edge)
- Must integrate with existing AU25 backend systems
- Must use existing design system components

## Out of Scope
- User authentication/login (handled elsewhere)
- Audit logs
- Bulk operations
- Data export functionality