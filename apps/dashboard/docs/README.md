# Admin Dashboard

## Overview
The Admin Dashboard is a web application for managing users and companies in the AU25 auction platform.

## Features
- User management (CRUD operations)
- Company management (CRUD operations)
- Real-time statistics display
- Role-based user classification (Traders/Auctioneers)

## Documentation
- [Product Requirements](./PRD.md) - What we're building and why
- [Tasks](./TASKS.md) - Current work items and backlog
- [Technical Context](./CONTEXT.md) - Technical details and architecture
- [API Documentation](./API.md) - API contracts and interfaces
- [Decision Log](./DECISIONS.md) - Architectural decisions and rationale

## Quick Start
```bash
# From repository root
turbo run dev --filter=aug-admin-dashboard

# Or from this directory
pnpm dev
```

Application will be available at `http://localhost:3001`