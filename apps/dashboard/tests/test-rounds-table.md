# Rounds Table Test Plan

## Overview
Successfully implemented a rounds table on the auctioneer page in aug-admin-dashboard with sufficient data to test row and column scrolling.

## Implementation Details

### Components Used
- **DeRoundTable** from `@repo/data-grid` package
- **LiveClientStoreBuilder** from `@repo/api-client` for mock data generation
- **setMockDomainStore** from `@repo/api-client` for store management

### Data Configuration
- **18 Companies/Traders** - Ensures vertical scrolling is required
- **14 Rounds** - Ensures horizontal scrolling is required
- **Varied Order Data** - Mix of BUY, SELL, and NONE orders with realistic quantities
- **Price Progression** - Realistic price movement across rounds (100.00 to 135.00)
- **Random Variations** - Different order types, quantities, and timing per trader/round

### Table Configuration
- **Height**: 600px (fixed to force vertical scrolling)
- **BuyMax/SellMax**: 500 (for proper bar chart scaling)
- **Selected Round**: 7 (middle round highlighted)
- **Event Handlers**: Console logging for round selection and company clicks

## Testing Instructions

### Access the Table
1. Open http://localhost:3001 in browser
2. Click the **"Auctioneer"** button in the navigation bar
3. The rounds table should load with 18 traders × 14 rounds

### Test Vertical Scrolling
- The table height is fixed at 600px
- With 18 trader rows + header/footer, scrolling should be required
- Scroll down to see all traders
- Price row should remain pinned at top
- Footer totals should remain pinned at bottom

### Test Horizontal Scrolling
- 14 rounds should require horizontal scrolling
- Scroll right to see rounds 8-14
- Company names column should remain pinned on left
- Round headers should scroll with content

### Test Interactions
- **Round Selection**: Click on round headers (1-14) to select different rounds
- **Company Selection**: Click on company names to trigger selection
- **Console Output**: Check browser console for click event logs

### Expected Data Patterns
- **Price Progression**: 100.00, 102.50, 105.00, ..., 135.00
- **Order Variety**: Mix of buy (green), sell (red), and no orders
- **Realistic Quantities**: 50-550 range with varied distributions
- **Price Directions**: Mix of UP/DOWN indicators in round headers

## Verification Checklist

- [ ] Application starts without errors
- [ ] Navigation to Auctioneer page works
- [ ] Table renders with 18 rows of traders
- [ ] Table shows 14 columns of rounds
- [ ] Vertical scrolling works (can see all 18 traders)
- [ ] Horizontal scrolling works (can see all 14 rounds)
- [ ] Round selection works (click headers)
- [ ] Company selection works (click names)
- [ ] Price row stays pinned at top
- [ ] Footer row stays pinned at bottom
- [ ] Company names column stays pinned on left
- [ ] Data appears realistic and varied

## Technical Notes

### Mock Store Setup
- Uses `LiveClientStoreBuilder` with 'auction_in_progress' scenario
- Extends default blotter data with additional rounds
- Generates realistic round trader elements for each company/round combination
- Updates common status to reflect current round state

### Data Generation Strategy
- **Deterministic Base**: Consistent company/trader setup
- **Random Variations**: Order types, quantities, timing vary per cell
- **Realistic Constraints**: Quantities in 50-550 range, proper price progression
- **Complete Coverage**: Every trader has data for every round

### Performance Considerations
- 18 × 14 = 252 data cells plus headers/footers
- AG Grid handles virtualization for smooth scrolling
- Mock data generated once on component mount
- No real-time updates (static test data)

## Success Criteria
✅ Table displays with proper dimensions (18 traders × 14 rounds)
✅ Both vertical and horizontal scrolling work smoothly
✅ Pinned rows/columns remain fixed during scrolling
✅ Interactive elements (round/company selection) function
✅ Data appears realistic and varied across all cells
✅ No console errors or TypeScript issues
