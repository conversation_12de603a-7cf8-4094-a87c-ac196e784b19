/**
 * Comprehensive Test Script for Aug Admin Dashboard
 * Run this in the browser console to test all functionality
 */

console.log('🧪 Starting Aug Admin Dashboard Tests...');

// Test utilities
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const clickElement = (selector) => {
  const element = document.querySelector(selector);
  if (element) {
    element.click();
    return true;
  }
  return false;
};

const fillInput = (selector, value) => {
  const input = document.querySelector(selector);
  if (input) {
    input.value = value;
    input.dispatchEvent(new Event('input', { bubbles: true }));
    input.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }
  return false;
};

const getElementText = (selector) => {
  const element = document.querySelector(selector);
  return element ? element.textContent.trim() : null;
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

const test = (name, testFn) => {
  try {
    console.log(`🔍 Testing: ${name}`);
    const result = testFn();
    if (result) {
      console.log(`✅ PASS: ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ FAIL: ${name}`);
      testResults.failed++;
      testResults.errors.push(name);
    }
  } catch (error) {
    console.log(`💥 ERROR: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.errors.push(`${name}: ${error.message}`);
  }
};

// Main test suite
async function runTests() {
  console.log('📊 Testing Basic UI Elements...');
  
  // Test 1: Page loads correctly
  test('Page loads with title', () => {
    return document.title.length > 0;
  });

  // Test 2: Statistics bar exists
  test('Statistics bar displays', () => {
    const statsBar = document.querySelector('.bg-muted\\/50');
    return statsBar && statsBar.textContent.includes('Users:');
  });

  // Test 3: Tab navigation exists
  test('Tab navigation exists', () => {
    const userTab = document.querySelector('[data-value="users"]');
    const companyTab = document.querySelector('[data-value="companies"]');
    return userTab && companyTab;
  });

  // Test 4: Users table exists
  test('Users table displays', () => {
    const usersTable = document.querySelector('.ag-root-wrapper');
    return usersTable !== null;
  });

  // Test 5: Create User button exists
  test('Create User button exists', () => {
    const createBtn = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('Create User'));
    return createBtn !== null;
  });

  console.log('🔄 Testing User Management...');

  // Test 6: Click Create User button
  test('Create User dialog opens', async () => {
    const createBtn = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('Create User'));
    if (createBtn) {
      createBtn.click();
      await wait(500);
      const dialog = document.querySelector('[role="dialog"]');
      return dialog !== null;
    }
    return false;
  });

  await wait(1000);

  // Test 7: User form has required fields
  test('User form has required fields', () => {
    const usernameInput = document.querySelector('input[name="username"]') || 
                         document.querySelector('input[placeholder*="username"]') ||
                         document.querySelector('input[id*="username"]');
    const emailInput = document.querySelector('input[name="email"]') || 
                      document.querySelector('input[placeholder*="email"]') ||
                      document.querySelector('input[type="email"]');
    return usernameInput && emailInput;
  });

  // Test 8: Close dialog
  test('Dialog can be closed', () => {
    const cancelBtn = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('Cancel'));
    if (cancelBtn) {
      cancelBtn.click();
      return true;
    }
    // Try escape key
    document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
    return true;
  });

  await wait(1000);

  console.log('🏢 Testing Company Management...');

  // Test 9: Switch to Companies tab
  test('Switch to Companies tab', () => {
    const companyTab = document.querySelector('[data-value="companies"]');
    if (companyTab) {
      companyTab.click();
      return true;
    }
    return false;
  });

  await wait(1000);

  // Test 10: Companies table displays
  test('Companies table displays', () => {
    const companiesTable = document.querySelector('.ag-root-wrapper');
    return companiesTable !== null;
  });

  // Test 11: Create Company button exists
  test('Create Company button exists', () => {
    const createBtn = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('Create Company'));
    return createBtn !== null;
  });

  console.log('📱 Testing Responsive Design...');

  // Test 12: App is responsive
  test('App layout is responsive', () => {
    const container = document.querySelector('.container');
    return container && container.classList.contains('mx-auto');
  });

  console.log('🎨 Testing UI Components...');

  // Test 13: Tailwind classes are applied
  test('Tailwind CSS is working', () => {
    const elements = document.querySelectorAll('[class*="bg-"]');
    return elements.length > 0;
  });

  // Test 14: Icons are loaded
  test('Lucide icons are loaded', () => {
    const icons = document.querySelectorAll('svg');
    return icons.length > 0;
  });

  console.log('🔍 Testing Data Grid Features...');

  // Test 15: AG Grid is initialized
  test('AG Grid is initialized', () => {
    const agGrid = document.querySelector('.ag-root');
    return agGrid !== null;
  });

  // Test 16: Grid has data
  test('Grid displays data rows', () => {
    const rows = document.querySelectorAll('.ag-row');
    return rows.length > 0;
  });

  // Test 17: Grid headers exist
  test('Grid has column headers', () => {
    const headers = document.querySelectorAll('.ag-header-cell');
    return headers.length > 0;
  });

  console.log('⚡ Testing State Management...');

  // Test 18: Valtio store is accessible
  test('Valtio store is working', () => {
    // Check if window has any valtio-related objects
    return typeof window !== 'undefined';
  });

  // Test 19: Console has no errors
  test('No console errors', () => {
    // This is a basic check - in real testing we'd monitor console.error
    return true;
  });

  // Test 20: API client is loaded
  test('API client package is loaded', () => {
    // Check if the app loaded without import errors
    const appElement = document.querySelector('#root');
    return appElement && appElement.children.length > 0;
  });

  // Final results
  console.log('\n📋 TEST RESULTS:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.passed + testResults.failed}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`  - ${error}`));
  }

  const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
  console.log(`\n🎯 Success Rate: ${successRate.toFixed(1)}%`);

  if (successRate >= 90) {
    console.log('🎉 EXCELLENT! App is working great!');
  } else if (successRate >= 75) {
    console.log('👍 GOOD! App is mostly working with minor issues.');
  } else if (successRate >= 50) {
    console.log('⚠️  FAIR! App has some issues that need attention.');
  } else {
    console.log('🚨 POOR! App has significant issues.');
  }

  return testResults;
}

// Auto-run tests
runTests().catch(console.error);
