# Augment Code ignore file
# Exclude files and directories from Augment's workspace indexing
# Uses gitignore syntax: https://git-scm.com/docs/gitignore

# Node.js dependencies
node_modules/

# Build outputs
dist/
dist-ssr/
.next/
.turbo/
storybook-static/

# Cache directories
.cache/

# Environment files
.env
.env.local
.env.*.local

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp

# IDE files (already in .gitignore but being explicit)
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/

# OS files
.DS_Store
Thumbs.db

# Archive directory (moved packages during consolidation)
.old/
