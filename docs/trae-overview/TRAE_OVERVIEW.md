# AU25 Auction System Overview

This codebase implements **AU25 Auction**, a sophisticated real-time auction platform that facilitates a novel auction design called **Double-Sided Price-Reversing Clock-Auction**.

## What This System Does

The AU25 Auction system is a distributed real-time platform designed to run complex auction processes where:

- **Bidders submit quantities** (not prices) they want to buy or sell at announced round prices
- **Prices adjust automatically** based on supply/demand imbalance between rounds
- **Auctions terminate** when equilibrium is reached or when buy/sell activity ratios reverse
- **Rational bidding constraints** prevent strategic manipulation and ensure economic rationality

## Key Features

### Novel Auction Mechanics
- **Double-sided**: Both buyers and sellers participate simultaneously
- **Price-reversing**: Prices can move up or down based on market dynamics
- **Clock auction**: Progresses through timed rounds with announced prices
- **Quantity-based bidding**: Participants bid quantities, not prices
- **Rationality enforcement**: Built-in rules prevent irrational bidding behavior

### Technical Architecture

**Backend (Java/Kotlin)**:
- <mcfile name="au21-engine" path="/Users/<USER>/au/codebase/auctions/au25-auction/java/au21-engine"></mcfile> - Quarkus-based engine
- **Unique approach**: No traditional request/response or SQL database
- **Single-threaded command processor** for consistency
- **Full state reconstruction** instead of incremental updates
- **WebSocket-only communication** after initial page load
- **In-memory state management** with ObjectDB persistence
- **Role-based state generation** for security and performance

**Frontend**:
- **Legacy**: Vue 2 with Pinia store
- **Current**: React with Valtio state management
- **Real-time updates** via WebSocket connector
- **AG Grid** for data visualization
- **Responsive design** with modern UI components

### Real-Time Communication Flow

1. **Client subscribes** to WebSocket for server events
2. **Client publishes commands** over WebSocket to server
3. **Server processes commands** through single-threaded processor
4. **Server validates** and creates action objects
5. **Server applies actions** to global state
6. **Server reconstructs** complete client state for each connected user
7. **Server sends** full JSON state to each client
8. **Client patches** reactive store (Pinia/Valtio)
9. **UI responds** automatically to store changes

## Project Structure

- <mcfolder name="java/au21-engine" path="/Users/<USER>/au/codebase/auctions/au25-auction/java/au21-engine"></mcfolder> - Backend Quarkus application
- <mcfolder name="nodejs" path="/Users/<USER>/au/codebase/auctions/au25-auction/nodejs"></mcfolder> - Frontend applications and packages
- <mcfolder name="docs" path="/Users/<USER>/au/codebase/auctions/au25-auction/docs"></mcfolder> - Comprehensive documentation
- <mcfolder name="scripts" path="/Users/<USER>/au/codebase/auctions/au25-auction/scripts"></mcfolder> - Development and deployment scripts
- <mcfolder name="devops" path="/Users/<USER>/au/codebase/auctions/au25-auction/devops"></mcfolder> - Docker and deployment configurations

## Current Status

The project is in **Phase 1: Backend Stabilization** with focus on:
- Updating dependencies (Quarkus, Kotlin)
- Optimizing state reconstruction performance
- Enhancing command validation
- Improving error handling and logging
- Setting up comprehensive testing

## Performance Characteristics

- Supports **200+ simultaneous clients**
- Handles **10 different concurrent auctions**
- Each auction supports **50+ rounds**
- Achieves **sub-100ms response times**
- Efficient bandwidth usage despite full state reconstruction

This system represents an innovative approach to auction technology, combining novel economic mechanisms with cutting-edge real-time distributed architecture to create a fair, efficient, and manipulation-resistant auction platform.