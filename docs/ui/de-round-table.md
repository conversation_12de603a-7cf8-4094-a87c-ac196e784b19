# DeRoundTable Component Documentation

## Overview

The DeRoundTable component is a high-performance data grid for displaying auction round data with traders and their orders. It provides a comprehensive view of trading activity across multiple rounds with real-time updates and interactive features.

## Features

### Core Functionality

#### **Data Display**
- **Trader Rows**: Displays all traders (companies) vertically
- **Round Columns**: Shows auction rounds horizontally
- **Cell Data**: Each cell contains trader order information for a specific round
- **100% Population**: All cells are populated with realistic trading data

#### **Pinned Elements**
- **Top Row**: Price information and round headers (pinned)
- **Bottom Row**: Totals and summary data (pinned)
- **Left Column**: Company/trader names (pinned)
- **Scrollable Area**: Round data cells with virtualization

#### **Real-time Updates**
- **Live Data**: Updates every second with new order information
- **Blinker Effects**: Light yellow background flash (200ms) when cell values change
- **Online Status**: Trader connection status updates with blinking indicators
- **Current Round Focus**: Only the latest round changes during updates
- **50% Cell Updates**: Changes 50% of last round cells per update for visibility
- **25% Trader Blinks**: Flashes 25% of trader online status indicators per second

### Interactive Features

#### **Navigation**
- **Vertical Scrolling**: Navigate through 200+ traders
- **Horizontal Scrolling**: Navigate through 100+ rounds
- **Round Selection**: Click round headers to select/highlight
- **Company Selection**: Click company names for details

#### **Programmatic Scrolling**
- **Scroll to Row**: Jump to specific trader positions
- **Scroll to Column**: Jump to specific rounds
- **Auto-scroll**: Automatically scroll to new rounds when added

#### **Round Management**
- **Add Round**: Button to add new rounds dynamically
- **Auto-scroll to New**: Automatically scroll to newly added rounds
- **Current Round Tracking**: Visual indication of the current round

## Technical Implementation

### Performance Optimizations

#### **AG Grid Virtualization**
- Only renders visible cells (~300-450 out of 20,000 total)
- Efficient scrolling with row/column buffers
- Minimal DOM manipulation

#### **Data Structure**
- **DeBlotter**: Contains traders, rounds, and round_traders
- **DeRoundElement**: Round-level data (price, direction, totals)
- **DeRoundTraderElement**: Individual trader orders per round
- **DeTraderElement**: Trader/company information

#### **Update Patterns**
- **Store Patching**: Modify existing store instead of recreation
- **Targeted Updates**: Only change current round data
- **Batch Operations**: Group multiple changes together

### Component Architecture

#### **Props**
```typescript
interface DeRoundTableProps {
  height: number;           // Fixed height for virtualization
  buyMax: number;          // Maximum buy quantity for scaling
  sellMax: number;         // Maximum sell quantity for scaling
  selectedRound: number;   // Currently selected round
  onSelectedRoundChange: (round: number) => void;
  onCompanyClick: (companyId: string) => void;
  onGridReady: (api: GridApi) => void;
}
```

#### **Data Flow**
1. **Store Creation**: LiveClientStoreBuilder creates initial data
2. **Data Enhancement**: Additional rounds and traders added
3. **Store Setting**: setMockDomainStore() makes data available
4. **Component Rendering**: DeRoundTable renders with AG Grid
5. **Real-time Updates**: Timer modifies store data every second

## Usage Examples

### Basic Implementation
```typescript
<DeRoundTable
  height={600}
  buyMax={500}
  sellMax={500}
  selectedRound={currentRound}
  onSelectedRoundChange={(round) => setSelectedRound(round)}
  onCompanyClick={(companyId) => console.log('Company:', companyId)}
  onGridReady={(api) => setGridApi(api)}
/>
```

### With Scroll Controls
```typescript
// Scroll to specific row
gridApi.ensureIndexVisible(rowIndex, 'middle');

// Scroll to specific column
gridApi.ensureColumnVisible('round-50', 'middle');

// Auto-scroll to latest round
gridApi.ensureColumnVisible(`round-${latestRound}`, 'end');
```

### Adding New Rounds
```typescript
const addNewRound = () => {
  const domainStore = getDomainStore();
  const blotter = domainStore.de_auction.blotter;
  const newRoundNumber = blotter.rounds.length + 1;

  // Create new round and traders
  const newRound = createTest__DeRoundElement(newRoundNumber, {...});
  const newRoundTraders = traders.map(trader =>
    createTest__DeRoundTraderElement(newRoundNumber, trader, {...})
  );

  // Add to store
  blotter.rounds.push(newRound);
  blotter.round_traders.push(...newRoundTraders);

  // Auto-scroll to new round
  setTimeout(() => {
    gridApi.ensureColumnVisible(`round-${newRoundNumber}`, 'end');
  }, 100);
};
```

## Performance Metrics

### Expected Performance (M3 Max, 128GB RAM)
- **Initial Load**: 500-2000ms for 200×100 dataset
- **Data Creation**: 50-200ms
- **Updates**: 5-50ms per update
- **Memory Usage**: ~21MB for full dataset
- **Fill Rate**: 100% (all cells populated)

### Optimization Targets
- **Fast**: <1000ms initial load
- **Moderate**: 1000-1500ms initial load
- **Slow**: >1500ms initial load

## Cell Renderers

### BuySellVBar Component
- **Visual Representation**: SVG bars showing buy/sell quantities
- **Color Coding**: Different colors for buy vs sell orders
- **Scaling**: Proportional to buyMax/sellMax values
- **Performance**: React Function Component (optimization target)

### Order Type Indicators
- **BUY Orders**: Displayed with specific styling
- **SELL Orders**: Displayed with different styling
- **NONE Orders**: Empty or minimal display

## Testing & Debugging

### Performance Testing
- **Metrics Dashboard**: Real-time performance monitoring
- **Console Logging**: Detailed timing information
- **Cell Population Stats**: Fill rate and data distribution
- **Update Tracking**: Individual and average update times

### Debug Features
- **Scroll Controls**: Manual navigation testing
- **Add Round Button**: Dynamic data addition
- **Performance Metrics**: Live performance dashboard
- **Console Logs**: Detailed operation timing

### Known Issues & Debugging

#### **Current Issues (Under Investigation)**
1. **Initial Rounds Not Visible**: First 20 rounds may not display properly
   - **Cause**: LiveClientStoreBuilder may not create initial round_traders
   - **Debug**: Check console for "INITIAL BLOTTER STATE" logs
   - **Expected**: Should show 20 rounds with 200 traders each (4,000 cells)

2. **Last Round Updates Not Visible**: Changes to round 100 not appearing
   - **Cause**: Update logic may not find round_traders for last round
   - **Debug**: Check console for "Attempting to update X cells" logs
   - **Expected**: Should show "Successfully updated X out of Y" messages

3. **Auto-scroll Not Working**: Table doesn't scroll to last round on load
   - **Cause**: Grid API timing or column naming issues
   - **Debug**: Check console for "Auto-scrolling to last round" message
   - **Expected**: Should automatically show round 100 on initial load

#### **Debugging Console Logs**
```
=== INITIAL BLOTTER STATE ===
Initial rounds: 20
Initial traders: 200
Initial round_traders: 4000
Initial rounds: [1,2,3...20]
Initial round_traders by round: {1: 200, 2: 200, ...}
================================

Attempting to update 8 cells in round 100
Blinker: Company-1 Round 100 → 234 (BUY)
No round trader found for Company-5 in round 100
Successfully updated 5 out of 8 attempted cells in round 100

Auto-scrolling to last round on initial load
```

#### **Testing with Playwright MCP**
- **Browser Automation**: Use Playwright MCP server for systematic testing
- **Visual Verification**: Check actual table rendering vs expected
- **Console Monitoring**: Verify debug logs match expected patterns
- **Scroll Testing**: Confirm auto-scroll and manual scroll functionality

#### **Troubleshooting Steps**
1. **Check Console Logs**: Verify data creation and update patterns
2. **Test Scroll Controls**: Use manual scroll buttons to verify grid API
3. **Inspect Network**: Ensure no errors in data loading
4. **Browser DevTools**: Check AG Grid DOM structure and column names

## Migration from Vue.js

### Key Differences
- **Vue Reactivity**: Automatic vs manual store updates
- **Component Lifecycle**: Vue watchers vs React useEffect
- **Performance**: Vue integration more efficient with AG Grid
- **Auto-scroll**: Vue @Watch vs React useEffect patterns

### Compatibility
- **Data Structures**: Identical between Vue and React versions
- **AG Grid API**: Same API calls and methods
- **Visual Output**: Pixel-perfect compatibility maintained
- **Feature Parity**: All Vue features implemented in React

## Future Enhancements

### Performance Optimizations
- **React.memo()**: Memoize cell renderers
- **Vanilla JS Renderers**: Replace React components
- **Web Workers**: Offload data processing
- **Canvas Rendering**: Alternative to DOM for cells

### Feature Additions
- **Sorting**: Multiple sort criteria
- **Filtering**: Data filtering capabilities
- **Export**: Data export functionality
- **Themes**: Multiple visual themes
