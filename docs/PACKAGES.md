# Packages Guide

## Monorepo Organization

### Applications (`/apps`)
Deployable applications that consume shared packages.

- **dashboard**

### Packages (`/packages`)
Shared libraries used across applications.

[To be documented as packages are created]

## Workspace Management

### Adding a New Package
1. Create directory under `/apps` or `/packages`
2. Add `package.json` with proper name scope
3. Update root `pnpm-workspace.yaml` if needed
4. Add documentation in `[package]/docs/`

### Dependencies
- Internal packages: Use workspace protocol `"@repo/package-name": "workspace:*"`
- External packages: Add to specific package, not root

### Building
```bash
turbo run build                        # Build all packages
turbo run build --filter=package-name  # Build specific package
```

### Development
```bash
turbo run dev --filter=package-name    # Run specific package
```