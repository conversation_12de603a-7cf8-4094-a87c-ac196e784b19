# Technical Debt & Deferred Work

This document tracks technical debt, deferred work, and known issues that need to be addressed in future iterations.

## High Priority

### 1. API Client Package Extraction

**Context:**
The api-client code is currently embedded in apps/dashboard/src/api-client/ but originates from another repository where it's used by both Vue and React applications in a pnpm workspace (not turborepo).

**Problem:**
- api-client needs to be shared across multiple applications/repositories
- Current consolidation into dashboard breaks reusability
- Changes to api-client in this repo won't sync back to the original repo

**Required Work:**
- [ ] Research successful monorepo patterns for TypeScript packages
- [ ] Solve the tsconfig/vite/turborepo configuration issues that led to consolidation
- [ ] Extract api-client back to packages/api-client with proper build setup
- [ ] Ensure reliable IDE navigation to source files (not dist/)
- [ ] Implement proper sourcemap generation and consumption
- [ ] Test turborepo cache invalidation with source changes
- [ ] Document working configuration for future packages

**Success Criteria:**
- api-client can be built as a package and consumed by dashboard
- IDE "Go to Definition" navigates to src/ files, not dist/
- Turborepo reliably rebuilds when source changes
- No "whack-a-mole" configuration issues
- Can be published/shared with other repositories

**Estimated Effort:** 2-3 weeks
**Priority:** High (required for multi-repo sharing)

### 2. Monorepo Toolchain Reliability

**Context:**
The original monorepo setup with separate packages was abandoned due to unreliable toolchain behavior.

**Problems Encountered:**
- TypeScript composite projects with references were brittle
- Vite sourcemap generation/consumption inconsistent
- Turborepo cache invalidation unreliable
- Shared typescript-config package caused build failures
- Problems only surfaced during clean rebuilds

**Required Research:**
- [ ] Study successful TypeScript monorepo configurations
- [ ] Investigate alternative tools (Nx, Rush, Lerna) vs Turborepo
- [ ] Research Vite + TypeScript + monorepo best practices
- [ ] Test different sourcemap strategies
- [ ] Document minimal working configuration

**Success Criteria:**
- Reliable builds across clean/incremental scenarios
- Proper IDE navigation to source files
- Predictable cache behavior
- Maintainable configuration

**Estimated Effort:** 1-2 weeks research + 1 week implementation
**Priority:** High (blocks package extraction)

## Medium Priority

### 3. Bundle Size Optimization

**Current State:**
Dashboard bundle is 1.37MB (394KB gzipped) - acceptable but could be optimized.

**Potential Improvements:**
- [ ] Implement code splitting for routes/features
- [ ] Analyze bundle composition with webpack-bundle-analyzer
- [ ] Lazy load AG Grid and other heavy dependencies
- [ ] Tree-shake unused lodash functions
- [ ] Consider lighter alternatives to heavy dependencies

**Estimated Effort:** 1 week
**Priority:** Medium (performance optimization)

### 4. Storybook Performance

**Current State:**
Storybook sometimes takes longer to start than expected.

**Investigation Needed:**
- [ ] Profile Storybook startup time
- [ ] Optimize story discovery patterns
- [ ] Consider Storybook 9.x upgrade
- [ ] Investigate addon performance impact

**Estimated Effort:** 2-3 days
**Priority:** Medium (developer experience)

## Low Priority

### 5. Config Package Cleanup

**Current State:**
Moved typescript-config, eslint-config, tailwind-config to .old/packages/ due to issues.

**Future Work:**
- [ ] Determine if shared configs are needed for multi-package setup
- [ ] Create minimal, working shared configurations
- [ ] Document when/how to use shared configs safely

**Estimated Effort:** 1-2 days
**Priority:** Low (only needed if extracting packages)

### 6. Testing Infrastructure

**Current State:**
Tests exist but could be improved.

**Improvements:**
- [ ] Add component testing for all UI components
- [ ] Improve API client test coverage
- [ ] Add E2E tests for critical user flows
- [ ] Set up visual regression testing for Storybook

**Estimated Effort:** 1-2 weeks
**Priority:** Low (quality improvement)

## Lessons Learned

### Monorepo Complexity
- Monorepos with TypeScript, Vite, and complex build chains are significantly more complex than they appear
- Configuration issues often don't surface until clean rebuilds
- "Magic" configurations are fragile and hard to debug
- Simple, explicit setups are more reliable than clever optimizations

### Toolchain Selection
- Turborepo + Vite + TypeScript composite projects = high complexity
- Consider simpler alternatives before complex setups
- Document working configurations immediately
- Test clean rebuild scenarios frequently

### Development Experience
- IDE navigation to source files is critical for productivity
- Sourcemap reliability is more important than build speed
- Predictable behavior trumps optimization

## Decision Framework

When considering whether to extract packages vs keep consolidated:

**Extract if:**
- Package needs to be shared across repositories
- Clear ownership and API boundaries exist
- Team has solved the toolchain reliability issues
- Maintenance overhead is acceptable

**Keep consolidated if:**
- Single application use case
- Rapid development/iteration needed
- Toolchain complexity outweighs benefits
- Team size doesn't justify package overhead

---

*Last updated: January 2025*
*Next review: When api-client sharing becomes required*
