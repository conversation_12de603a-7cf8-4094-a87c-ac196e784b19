# MCP Server Tools Available

This document lists all MCP (Model Context Protocol) server tools available to Augment <PERSON> for development tasks.

## 🌐 Browser Automation (Playwright MCP Server)

The Playwright MCP server provides comprehensive browser automation capabilities for testing and interaction:

### Navigation & Page Control
- `browser_navigate_playwright` - Navigate to URLs
- `browser_navigate_back_playwright` - Go back to previous page
- `browser_navigate_forward_playwright` - Go forward to next page
- `browser_close_playwright` - Close the browser page
- `browser_resize_playwright` - Resize browser window

### Element Interaction
- `browser_click_playwright` - Click on elements
- `browser_type_playwright` - Type text into input fields
- `browser_hover_playwright` - Hover over elements
- `browser_select_option_playwright` - Select options from dropdowns
- `browser_drag_playwright` - Drag and drop between elements
- `browser_press_key_playwright` - Press keyboard keys

### Content Capture & Analysis
- `browser_snapshot_playwright` - Take accessibility snapshots (better than screenshots for actions)
- `browser_take_screenshot_playwright` - Take visual screenshots
- `browser_network_requests_playwright` - Monitor network requests
- `browser_console_messages_playwright` - Read browser console logs

### Advanced Features
- `browser_wait_for_playwright` - Wait for text/conditions/time
- `browser_file_upload_playwright` - Upload files
- `browser_handle_dialog_playwright` - Handle alerts/dialogs
- `browser_pdf_save_playwright` - Save page as PDF
- `browser_generate_playwright_test_playwright` - Generate test code

### Tab Management
- `browser_tab_list_playwright` - List open tabs
- `browser_tab_new_playwright` - Open new tab
- `browser_tab_select_playwright` - Switch to tab by index
- `browser_tab_close_playwright` - Close tab

## 🔍 Search & Web Tools

### Search APIs
- `brave_web_search_brave-search` - Web search using Brave Search API
  - General queries, news, articles, online content
  - Supports pagination (max 20 results, offset for pagination)
  - Content filtering and freshness controls
- `brave_local_search_brave-search` - Local business search
  - Physical locations, businesses, restaurants, services
  - Returns addresses, ratings, phone numbers, hours
  - Auto-fallback to web search if no local results
- `web-search` - Google Custom Search API
  - Alternative web search with different result format
  - Returns results in markdown format

### Content Retrieval
- `web-fetch` - Fetch webpage content and convert to Markdown
- `open-browser` - Open URLs in user's default browser (for visual inspection)

## 📁 File System Tools

### File Operations
- `view` - View files and directories with optional line ranges
  - Supports both files and directories (up to 2 levels deep)
  - Line range viewing for large files
  - Efficient for scanning code with large ranges (500+ lines recommended)
- `str-replace-editor` - Edit files with precise string replacements
  - **Primary tool for code editing**
  - Supports multiple replacements in one call
  - Line-number based for disambiguation
  - Insert and replace operations
- `save-file` - Create new files with content
  - For new files only (use str-replace-editor for existing files)
  - 300-line limit per file
- `remove-files` - Safely delete files (user can undo)

## 💻 Process Management Tools

### Process Control
- `launch-process` - Run shell commands
  - Waiting (`wait=true`) or background (`wait=false`) processes
  - Interactive terminal support
  - Working directory specification
- `read-process` - Read output from processes
  - Can wait for completion or read current output
  - Useful for monitoring long-running processes
- `write-process` - Write input to process stdin
- `kill-process` - Terminate processes by terminal ID
- `list-processes` - List all active processes and their states
- `read-terminal` - Read from active VSCode terminal

## 🔧 Development Tools

### Code Intelligence
- `codebase-retrieval` - **Augment's world-class context engine**
  - Natural language code search across entire codebase
  - Real-time index with current state of code
  - Cross-language retrieval capabilities
  - Highest-quality recall of relevant code snippets
- `diagnostics` - Get IDE errors, warnings, and issues
  - Optional file path filtering
  - Essential for fixing TypeScript/build errors

### Memory & Documentation
- `remember` - Store long-term memories for future sessions
  - Use for information that will be useful long-term
  - Avoid for temporary information

## 📊 Visualization Tools

- `render-mermaid` - Render Mermaid diagrams
  - Interactive diagrams with pan/zoom controls
  - Copy functionality
  - Support for all Mermaid diagram types

## 🎯 Most Relevant for Our React Project

### Primary Development Workflow
1. **`codebase-retrieval`** - Find existing patterns and understand code structure
2. **`str-replace-editor`** - Edit code files with precision
3. **`diagnostics`** - Check for and fix TypeScript/build errors
4. **`launch-process`** - Run builds, tests, and development servers

### Testing & Validation
1. **`browser_snapshot_playwright`** - Test components in browser
2. **`browser_navigate_playwright`** - Navigate to Storybook/app
3. **`browser_console_messages_playwright`** - Check for runtime errors
4. **`browser_click_playwright`** - Test interactive functionality

### Research & Documentation
1. **`brave_web_search_brave-search`** - Research best practices
2. **`view`** - Examine existing code and documentation
3. **`remember`** - Store important decisions and patterns

## 🚀 Example Usage Patterns

### Testing a New Component
```bash
# 1. Navigate to Storybook
browser_navigate_playwright("http://localhost:6006")

# 2. Take accessibility snapshot
browser_snapshot_playwright()

# 3. Test interactions
browser_click_playwright(element="Button", ref="story-button")

# 4. Check console for errors
browser_console_messages_playwright()
```

### Code Development Workflow
```bash
# 1. Research existing patterns
codebase-retrieval("React component patterns with AG Grid")

# 2. Edit code
str-replace-editor(path="src/component.tsx", ...)

# 3. Check for errors
diagnostics()

# 4. Test build
launch-process("pnpm build", wait=true)
```

## 📝 Best Practices

### Browser Testing
- Always use `browser_snapshot_playwright` over screenshots for actions
- Check console messages for runtime errors
- Test both functionality and accessibility

### Code Editing
- Use `codebase-retrieval` before making changes to understand patterns
- Always check `diagnostics` after code changes
- Use large line ranges (500+) with `view` for efficiency

### Process Management
- Use `wait=true` for short commands that must complete
- Use `wait=false` for servers and long-running processes
- Always check process output with `read-process`

---

*This documentation reflects the MCP server capabilities available as of January 2025.*
*The Playwright MCP server is particularly powerful for testing React components and ensuring they work correctly in real browser environments.*
