# Architecture Overview

## Monorepo Structure
This project uses Turborepo with pnpm workspaces for managing multiple packages.

## Technology Stack
- **Build System:** Turborepo + pnpm
- **Framework:** React 18 with TypeScript
- **Bundler:** Vite
- **State Management:** Valtio
- **UI Components:** Custom components with Tailwind CSS
- **Data Grid:** AG-Grid

## Design Patterns
- **CQRS (Command Query Responsibility Segregation)** for state management
- **Component-driven development** with clear separation of concerns
- **Monorepo architecture** for code sharing and dependency management

## Package Types
1. **Applications** (`/apps/*`) - Deployable applications
2. **Packages** (`/packages/*`) - Shared libraries and components

## Key Architectural Decisions
[To be documented as decisions are made]