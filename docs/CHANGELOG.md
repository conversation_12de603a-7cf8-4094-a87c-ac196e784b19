# Change History - AU25 Turbo React Design

## 2025-05-29

### Implemented: Sankey Diagram Component Library Comparison
- **Files:**
  - `apps/dashboard/src/widgets/sankey/` - Complete Sankey implementation suite
  - `apps/dashboard/src/widgets/sankey/SankeyComparison.stories.tsx` - Comprehensive comparison story
  - `apps/dashboard/src/widgets/sankey/sankey-nivo.tsx` - Nivo implementation (working)
  - `apps/dashboard/src/widgets/sankey/sankey-plotly.tsx` - Plotly implementation (working)
  - `apps/dashboard/src/widgets/sankey/sankey-d3.tsx` - D3-Sankey implementation (working)
  - `apps/dashboard/src/widgets/sankey/sankey-reaviz.tsx` - Reaviz implementation (working)
  - `apps/dashboard/src/widgets/sankey/sankey-svg.tsx` - Custom SVG implementation (working)
  - `apps/dashboard/src/widgets/sankey/sankey-colors.ts` - Shared color scheme
  - `apps/dashboard/src/widgets/sankey/sankey-data.ts` - Test data generation
  - `apps/dashboard/src/widgets/sankey/SankeyStory.tsx` - Error boundary wrapper
- **What:** Created comprehensive comparison of 5 different Sankey diagram implementations for auction flow visualization
- **Key Features:**
  - **Auction Flow Visualization:** Shows flow from Max Sell Constraints → Seller Orders → Buyer Orders → Max Buy Constraints
  - **Library Comparison:** Evaluated Nivo, Plotly, D3-Sankey, Reaviz, and custom SVG implementations
  - **Self-Contained Components:** Each implementation includes internal data transformation (no shared utilities)
  - **Consistent Interface:** All components use same props: width, height, matrix_edges, round_trader_elements
  - **Error Boundaries:** SafeSankeyStory wrapper catches and displays implementation errors
  - **Comprehensive Testing:** All implementations verified working in Storybook without console errors
- **Working Implementations:**
  - **Nivo:** Full-featured with built-in animations and interactions
  - **Plotly:** Scientific-grade with zoom/pan capabilities
  - **D3-Sankey:** Direct D3 control with custom React wrapper
  - **Reaviz:** React data visualization library with JSX-based API
  - **Custom SVG:** Pure SVG implementation with manual layout calculations
- **Removed/Fixed:**
  - Removed Recharts implementation (no native Sankey support)
  - Removed React-Vis implementation (replaced with custom SVG)
  - Fixed Reaviz implementation to use correct JSX-based API with SankeyNode/SankeyLink components
- **Technical Implementation:**
  - Used Vue.js interface compatibility (DeMatrixEdgeElement[], DeRoundTraderElement[])
  - Implemented consistent color scheme across all implementations
  - Added proper TypeScript typing for all components
  - Created comprehensive Storybook stories with individual and comparison views
- **Testing:** Verified all implementations working in Storybook with Playwright MCP testing
- **Impact:** Complete Sankey diagram solution ready for auction flow visualization with multiple implementation options

## 2025-05-29

### Fixed: Navbar Button Visibility in Dark Theme
- **Files:**
  - `apps/dashboard/src/components/ui/button.tsx` - Enhanced ghost variant with text color
  - `apps/dashboard/src/App.tsx` - Added dark class for CSS custom properties
  - `apps/dashboard/.storybook/preview.ts` - Improved Storybook dark theme support
  - `apps/dashboard/src/widgets/common-status/CommonStatusWidget.stories.tsx` - Added dark theme wrappers
  - `apps/dashboard/src/data-grids/de-round-table/DeRoundTable.stories.tsx` - Added dark theme wrappers
- **Problem:** Navigation buttons (Status, Round Table, Users, etc.) were invisible until hovered due to missing text color in ghost button variant
- **Solution:**
  - **Button Component Fix:** Added `text-foreground` class to ghost variant for default visibility
  - **App Component Fix:** Added `dark` class to enable CSS custom properties for dark theme
  - **Storybook Enhancement:** Improved dark theme consistency across all stories
- **Technical Details:**
  - Ghost button variant: `"hover:bg-accent hover:text-accent-foreground"` → `"text-foreground hover:bg-accent hover:text-accent-foreground"`
  - App container: `"min-h-screen flex flex-col bg-gray-950"` → `"dark min-h-screen flex flex-col bg-gray-950"`
  - Uses CSS custom property `--foreground: 0 0% 98%` in dark mode for proper text contrast
- **Impact:** Navigation buttons now visible by default with proper dark theme styling
- **Testing:** Verified with hot module reload - all navigation buttons now clearly visible

## 2025-05-28

### Implemented: DeTraderHistoryTable Sorting & Auto-Scroll Features
- **Files:**
  - `apps/dashboard/src/data-grids/de-trader-history-table/DeTraderHistoryTable.tsx` - Enhanced with sorting and scroll controls
  - `apps/dashboard/src/data-grids/de-trader-history-table/DeTraderHistoryTable.stories.tsx` - Added interactive story
  - `apps/dashboard/src/data-grids/de-trader-history-table/index.ts` - Added ref type export
- **What:** Implemented comprehensive sorting and auto-scroll functionality for DeTraderHistoryTable
- **Key Features:**
  - **Ascending Sort by Round Number:** Table automatically sorts by round number with last round at bottom
  - **Auto-Scroll on New Rounds:** When new rounds are added, table automatically scrolls to show the last round
  - **Scroll Control Functions:** Added `scrollToFirst()`, `scrollToLast()`, and `scrollToRound(roundNumber)` via forwardRef
  - **Interactive Storybook Story:** Created "Interactive Scrolling" story with control buttons and add round functionality
  - **Disabled User Sorting:** Prevented user sorting to maintain controlled round order
  - **Real-time Round Counter:** Enhanced UI with live round count display
- **Technical Implementation:**
  - Used `useMemo` for efficient sorting of history rows by round number
  - Implemented `useImperativeHandle` to expose scroll functions via ref
  - Added `sortable: false` on all columns to prevent user sorting
  - Enhanced auto-scroll logic with `ensureIndexVisible` for smooth scrolling
  - Added comprehensive TypeScript types for ref interface
- **Testing:** Verified functionality with Playwright MCP testing - all features working correctly
- **Impact:** Component is now production-ready with full sorting and navigation control

## 2025-01-29

### Major: Package Consolidation into Dashboard
- **Files:** Moved all packages into `apps/dashboard/src/`
  - `packages/data-grid/` → `apps/dashboard/src/data-grids/`
  - `packages/api-client/` → `apps/dashboard/src/api-client/`
  - `packages/aug-ui/` → `.old/packages/aug-ui/` (archived)
- **What:** Consolidated monorepo packages into single self-contained application
- **Reason:** Unable to reliably configure tsconfig/vite/turborepo for:
  - Dist builds with source navigation (IDE goto definition)
  - Turborepo cache invalidation with source changes
  - Shared typescript-config without brittle configurations
- **Result:** Dashboard is now completely independent with zero workspace dependencies
- **Impact:** Reliable build system, simple @/ imports, deployable standalone app

### Implemented: Storybook Migration to Dashboard
- **Files:**
  - `apps/dashboard/.storybook/` - Storybook configuration
  - `apps/dashboard/stories/` - All migrated stories
  - Removed `apps/storybook/` entirely
- **What:** Integrated Storybook directly into dashboard application
- **Features:**
  - Updated all stories to use @/ imports instead of @repo/
  - Added Storybook scripts to dashboard package.json
  - Updated button story to use shadcn Button component
- **Commands:** `just dashboard-storybook` or `cd apps/dashboard && pnpm storybook`
- **Impact:** Self-contained documentation system within the application

### Created: Technical Debt Documentation
- **File:** `docs/TECHNICAL_DEBT.md`
- **What:** Comprehensive documentation of deferred work and known issues
- **Key Items:**
  - API client package extraction (high priority - needed for multi-repo sharing)
  - Monorepo toolchain reliability research
  - Bundle size optimization opportunities
- **Impact:** Clear roadmap for future improvements and package extraction

### Updated: Decision Documentation
- **File:** `docs/DECISIONS.md`
- **What:** Added detailed reasoning for package consolidation decision
- **Content:** Documented failed attempts at monorepo configuration and trade-offs
- **Impact:** Future teams can understand why consolidation was chosen over packages

## 2025-05-22

### Changed: Compact Statistics Display
- **File:** `apps/aug-admin-dashboard/src/App.tsx`
- **What:** Replaced 4 large statistics cards with single horizontal stats bar
- **Before:** Grid of cards (Total Users, Total Companies, Auctioneers, Traders)
- **After:** Inline stats bar with format "Users: 10 (10 online) | Companies: 10 | etc"
- **Impact:** ~80% reduction in vertical space usage

### Changed: Table Heights
- **File:** `apps/aug-admin-dashboard/src/App.tsx`
- **What:** Changed from fixed heights to dynamic calculation
- **Before:** `height="500px"` for users, `height="400px"` for companies
- **After:** `height="calc(100vh - 280px)"` for both
- **Impact:** Tables use available viewport height better

### Changed: Container Spacing
- **File:** `apps/aug-admin-dashboard/src/App.tsx`
- **What:** Reduced padding and added max width
- **Before:** `p-6 space-y-6`
- **After:** `px-4 py-3 space-y-4 max-w-7xl`
- **Impact:** More efficient use of horizontal space

### Removed: Company Users Sidebar
- **File:** `apps/aug-admin-dashboard/src/App.tsx`
- **What:** Removed the sidebar showing users for selected company
- **Before:** 3-column layout in companies tab with user list sidebar
- **After:** Single full-width companies table
- **Impact:** More space for companies table, simplified UI

### Created: Helper Components (not integrated)
- **Files:**
  - `apps/aug-admin-dashboard/src/components/ResponsiveUsersTable.tsx`
  - `apps/aug-admin-dashboard/src/components/ResponsiveCompaniesTable.tsx`
  - `apps/aug-admin-dashboard/src/components/UserCardView.tsx`
- **Note:** Created but not integrated - user didn't want mobile version

### Created: Project Documentation
- **Files:**
  - `CLAUDE.md` - Interaction rules and project context
  - `.claude/session-notes.md` - Decision log
  - `.claude/history.md` - This file

## 2025-05-23

### Implemented: DeRoundTable Component Migration
- **Files:**
  - `packages/data-grid/src/de-round-table/DeRoundTable.tsx` - Main component
  - `packages/data-grid/src/de-round-table/DeRoundTable.stories.tsx` - Storybook stories
  - `packages/data-grid/src/de-round-table/de-round-table-helpers.ts` - Types and helpers
  - `packages/data-grid/src/de-round-table/index.ts` - Exports
- **What:** Migrated DeRoundTable from Charlize Vue2 app to React
- **Before:** Vue2 + Pinia with separate cell renderer files
- **After:** React + Valtio with consolidated cell renderers in main file
- **Key Features:**
  - Enhanced BaseAgGrid with pinned rows and custom row heights
  - getDomainStore() pattern instead of React Context/Provider
  - Dynamic columns (one per round + trader names column)
  - Custom cell renderers for headers, prices, buy/sell bars, totals
  - Interactive features: round selection, sorting, click handlers
- **Impact:** Successfully replicated original Charlize functionality in React ecosystem

### Enhanced: BaseAgGrid Component
- **File:** `packages/data-grid/src/BaseAgGrid.tsx`
- **What:** Added support for advanced AG Grid features
- **Added Props:**
  - `pinnedTopRowData` and `pinnedBottomRowData` for pinned rows
  - `getRowHeight` for custom row heights
  - `suppressAutoSize` and `autoRefresh` for sizing control
- **Impact:** BaseAgGrid now supports complex table layouts needed for DeRoundTable

### Updated: Storybook Configuration
- **File:** `apps/storybook/.storybook/main.js`
- **What:** Added packages directory to story discovery
- **Before:** Only looked in `../stories/*.stories.tsx`
- **After:** Also looks in `../../../packages/**/*.stories.tsx`
- **Impact:** Stories can now be co-located with component code in packages

### Created: Comprehensive Storybook Stories
- **File:** `packages/data-grid/src/de-round-table/DeRoundTable.stories.tsx`
- **What:** Four story variants with realistic mock data
- **Stories:** Default (5 companies, 3 rounds), Large Table (10 companies, 6 rounds), Small Table (3 companies, 2 rounds), Interactive (with event handlers)
- **Features:** Uses `setMockDomainStore()` for testing, realistic auction data
- **Impact:** Component fully testable and demonstrable in Storybook

## 2025-01-08

### Implemented: DeRoundTableDemo Component & AuctioneerPage Refactor
- **Files:**
  - `apps/aug-admin-dashboard/src/components/DeRoundTableDemo.tsx` - New demo component
  - `apps/aug-admin-dashboard/src/components/AuctioneerPage.tsx` - Refactored to use demo
- **What:** Created comprehensive demo component with real-time data changes
- **Features:**
  - 50 traders × 20 rounds dataset for optimal performance
  - Real-time trader online status changes every 1 second (based on au21-frontend patterns)
  - Cell content blinker effects for quantity changes
  - Scroll controls for testing navigation (first/last/middle/random)
  - Loading states to prevent UI blocking during data setup
  - setTimeout-based timers (not setInterval) for better cleanup
- **AuctioneerPage Changes:**
  - Converted from direct table implementation to demo dashboard
  - Added "Round Table" button to show DeRoundTableDemo
  - Clean separation between dashboard and demo functionality
- **Impact:** Proper demo environment with real-time features for testing table functionality

### Enhanced: DeRoundTable Storybook Story
- **File:** `packages/data-grid/src/de-round-table/DeRoundTable.stories.tsx`
- **What:** Added "FullScale" story with auto-scroll demonstration
- **Features:** 50 traders × 20 rounds with programmatic scrolling after 2 seconds
- **Impact:** Comprehensive testing environment in Storybook

### Fixed: Table Rendering Performance Issue
- **Problem:** Initial 200×100 dataset (20,000+ data points) was blocking UI thread
- **Solution:** Reduced to 50×20 dataset (1,000 data points) for optimal performance
- **Added:** Loading states and proper timer management to prevent rendering conflicts
- **Impact:** Table now renders immediately without UI blocking

## 2025-01-28

### Fixed: Build System After Repository Rollback
- **Files:**
  - `packages/data-grid/tsup.config.ts` - Added AG Grid externals
  - `apps/book/package.json` - Added AG Grid dependencies
- **Problem:** Build failures after rolling back to previous commit
- **Root Cause:** Missing external dependencies and package resolution issues
- **Solution:**
  - Added `ag-grid-react` and `ag-grid-community` to external dependencies in data-grid tsup config
  - Added AG Grid dependencies to book app to resolve import resolution failures
  - Ensured proper package dependency installation after rollback
- **Result:** All 6 packages now build successfully (build time: 8.682s with 2 cached builds)
- **Impact:** Restored working build system, enabling continued development
