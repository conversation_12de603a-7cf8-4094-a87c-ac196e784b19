# Sankey Widget Requirements

## Overview

The Sankey widget visualizes the flow of product allocation in the double-sided auction system, showing how capacity flows from total market capacity through individual traders to actual matches and remaining capacity.

## Reference

See auction rules: [docs/design/A.auction-rules.md](docs/design/A.auction-rules.md)

## Visualization Structure

The Sankey diagram displays 4 columns representing the logical allocation flow from left to right:

1. **Column 1 (Max Sell Constraints)**: Individual sellers with their maximum sell constraints
2. **Column 2 (Seller Orders)**: Individual sellers with their actual order quantities
3. **Column 3 (Buyer Orders)**: Individual buyers with their actual order quantities
4. **Column 4 (Max Buy Constraints)**: Individual buyers with their maximum buy constraints

**Flow Direction**: Product flows logically from left to right (seller → buyer), representing the natural flow of goods in the auction system.

**Display Rule**: Only traders who have actually submitted orders are shown. Potential traders or capacity are not displayed at this stage.

## Example Scenario

Based on the actual data structures, here's a rules-compliant scenario:

### Test Data Structure

**Round Trader Elements** (Orders):
```typescript
const round_trader_elements: DeRoundTraderElement[] = [
  // Sellers
  { cid: "seller_a", company_shortname: "SellerA", order_type: OrderType.SELL, quantity_int: 10, constraints: { max_sell_quantity: 40, ... } },
  { cid: "seller_b", company_shortname: "SellerB", order_type: OrderType.SELL, quantity_int: 20, constraints: { max_sell_quantity: 30, ... } },
  // Buyers
  { cid: "buyer_c", company_shortname: "BuyerC", order_type: OrderType.BUY, quantity_int: 5, constraints: { max_buy_quantity: 10, ... } },
  { cid: "buyer_d", company_shortname: "BuyerD", order_type: OrderType.BUY, quantity_int: 15, constraints: { max_buy_quantity: 30, ... } }
];
```

**Matrix Edges** (Matches):
```typescript
const matrix_edges: DeMatrixEdgeElement[] = [
  { seller_cid: "seller_a", buyer_cid: "buyer_c", match: 5, seller_shortname: "SellerA", buyer_shortname: "BuyerC" },
  { seller_cid: "seller_a", buyer_cid: "buyer_d", match: 5, seller_shortname: "SellerA", buyer_shortname: "BuyerD" },
  { seller_cid: "seller_b", buyer_cid: "buyer_d", match: 10, seller_shortname: "SellerB", buyer_shortname: "BuyerD" }
];
```

### Data Transformation Logic

The Vue component transforms this data into a Sankey structure with:

1. **Nodes**: 4 columns as described (constraints → orders → orders → constraints)
2. **Links**: 3 types of flows:
   - **Constraint → Order**: From max capacity to actual order quantity
   - **Order → Order**: The actual matches (core auction result)
   - **Order → Constraint**: From allocated quantity to max capacity

### Expected Visualization

- **Column 1**: Seller max constraints (SellerA: 40, SellerB: 30)
- **Column 2**: Seller orders (SellerA: 10, SellerB: 20)
- **Column 3**: Buyer orders (BuyerC: 5, BuyerD: 15)
- **Column 4**: Buyer max constraints (BuyerC: 10, BuyerD: 30)

- **Flows**: Show constraint utilization and actual matching patterns

## Key Insights Revealed

1. **Market Utilization**:
   - Total matched: 20 units
   - Sell order utilization: 66.7% (20/30 total orders)
   - Seller A: 100% utilized (10/10)
   - Seller B: 50% utilized (10/20)
   - Buy allocation vs max capacity: 50% (20/40 total max buy)

2. **Allocation Efficiency**: Visual representation of actual seller→buyer matches in Col 2→3

3. **Constraint Impact**: Shows unused capacity at both ends:
   - Seller A: 30 units unused max capacity (40-10)
   - Seller B: 10 units unused max capacity (30-20) + 10 units unmatched order
   - Buyer C: 5 units unused max capacity (10-5)
   - Buyer D: 15 units unused max capacity (30-15)

4. **Flow Bottlenecks**: Reveals where constraints limit market activity

## Mandatory Interface

**CRITICAL**: Every Sankey implementation must use this exact interface - no exceptions:

```typescript
interface SankeyProps {
  width: number;
  height: number;
  matrix_edges: DeMatrixEdgeElement[];
  round_trader_elements: DeRoundTraderElement[];
}
```

**Interface Compliance Rules:**
1. **No custom data structures** - All implementations must accept the raw auction data
2. **No library-specific props** - Transform data internally, not via props
3. **Identical function signature** - Every component must be drop-in replaceable
4. **Import from api-client** - Use existing types: `import { DeMatrixEdgeElement, DeRoundTraderElement } from '../../api-client'`
5. **NO abstraction layers** - No SankeyNode, SankeyLink, SankeyData interfaces allowed
6. **NO shared transforms** - Each component handles its own data transformation internally
7. **Self-contained only** - No dependencies on transform utilities or shared data structures

**Data Understanding**:
- `matrix_edges`: Contains actual matches between buyers and sellers (edge.match = quantity)
- `round_trader_elements`: Contains individual orders (rte.order_type = BUY/SELL, rte.quantity_int = order size)
- `constraints`: Available in rte.constraints.max_buy_quantity and rte.constraints.max_sell_quantity
- **Flow**: Max Constraints → Orders → Matches → Max Constraints

## Widget Requirements

The Sankey widget should:
- Display real-time allocation flows as auction rounds progress
- Highlight the core matching flows (Col 2 → Col 3) as the primary focus
- Show capacity utilization through ribbon thickness
- Update dynamically as new rounds complete
- Provide hover details showing exact quantities and percentages

## Interactive Requirements

### Hover Behavior
- Show numbers on each ribbon, taking care to space them somehow if the ribbons are thin
- Mouse over a ribbon highlights the entire path of that ribbon from source to sink including any bars it intersects
- For example if the mouse is over the ribbon from Seller A Order to Buyer C, then the Seller A bar, the ribbon, and the Buyer C bar are all highlighted, in addition to the ribbons from sell max to sellerA and from buyerC to buy max

### Animation Requirements
- **Functional animations**: Smooth transitions when data is added/updated
- **Gentle hover effects**: Subtle highlighting and transitions on mouse interaction
- **Configurable**: Animations should be easily configurable or disableable
- **Implementation flexibility**: If animations are difficult to implement in a specific library, defer them rather than forcing complex solutions
- **Focus**: Meaningful animations that enhance understanding, not entertainment

### Responsive Design
- Works at different sizes
- Maintains readability at various scales

## Color Requirements

The color scheme must be consistent across all implementations and should be extracted from the existing Vue implementation. Required colors:

**8 Color Combinations** (buy/sell × highlighted/non-highlighted × colorblind/non-colorblind):
1. Buy - Normal - Non-colorblind
2. Buy - Normal - Colorblind
3. Buy - Highlighted - Non-colorblind
4. Buy - Highlighted - Colorblind
5. Sell - Normal - Non-colorblind
6. Sell - Normal - Colorblind
7. Sell - Highlighted - Non-colorblind
8. Sell - Highlighted - Colorblind

These colors should be documented in a shared constants file for use across all implementations.

## Library Adaptation Requirements

### When Libraries Don't Support the Interface
Some libraries may not have native Sankey support or may require different data formats:

**Adaptation Rules:**
1. **Transform data internally** - Convert auction data to library format inside the component
2. **No interface changes** - Component must still accept the mandatory SankeyProps
3. **Custom implementations allowed** - If library lacks Sankey, create custom SVG/Canvas solution
4. **Error boundaries required** - Wrap each implementation in error handling
5. **Fallback behavior** - Show meaningful error message if library fails

### Implementation Constraints
1. **No library-specific imports in props** - Don't import library types in the interface
2. **Self-contained components** - Each implementation handles its own data transformation
3. **Consistent error handling** - All implementations must handle missing/invalid data gracefully
4. **Performance considerations** - Large datasets should not crash the browser

## Mandatory Testing Requirements

### Browser Testing Protocol
**BEFORE claiming any implementation is complete:**

1. **Navigate to Storybook**: `browser_navigate_playwright` to http://localhost:6006
2. **Check console**: `browser_console_messages_playwright` - must be error-free
3. **Visual verification**: `browser_snapshot_playwright` - verify diagram renders correctly
4. **Test interactions**: Hover, click, data updates must work
5. **Test data toggle**: Use story controls to switch between simple/enhanced data
6. **Document results**: Report what works, what doesn't, any console errors

### Acceptance Criteria
- ✅ Component renders without console errors
- ✅ Shows 4-column flow (constraints → orders → orders → constraints)
- ✅ Displays correct data from test scenarios
- ✅ Handles data updates when story controls are used
- ✅ Provides meaningful tooltips/interactions where possible
- ❌ **FAIL**: Any console errors, crashes, or incorrect data display

## Implementation Approach

### Target Libraries
Six different Sankey diagram implementations:
1. **reaviz** (https://reaviz.dev/)
2. **plotly** (https://plotly.com/javascript/)
3. **nivo** (https://nivo.rocks/sankey/api/)
4. **react-vis** (https://uber.github.io/react-vis/) - custom implementation if no native support
5. **visx** (https://airbnb.io/visx/)
6. **d3-sankey + react** (fallback option)

### File Structure
```
apps/dashboard/src/widgets/sankey/
├── sankey-reaviz.tsx        # Self-contained Reaviz implementation
├── sankey-plotly.tsx        # Self-contained Plotly implementation
├── sankey-nivo.tsx          # Self-contained Nivo implementation
├── sankey-react-vis.tsx     # Self-contained React-Vis implementation
├── sankey-visx.tsx          # Self-contained Visx implementation
├── sankey-d3.tsx            # Self-contained D3 implementation
├── SankeyStory.tsx          # Common wrapper component only
├── sankey-data.ts           # Test data generation only (NO transforms)
├── sankey-colors.ts         # Color constants from Vue only
└── SankeyComparison.stories.tsx  # Unified Storybook story
```

**DELETED FILES** (no longer allowed):
- ❌ sankey-transform.ts - Each component handles its own transformation
- ❌ Any SankeyNode, SankeyLink, SankeyData interfaces - Abstraction layers prohibited

