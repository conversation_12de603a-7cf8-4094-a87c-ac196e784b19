# Coding Standards

## General Principles
- **Clarity over cleverness** - Write code that is easy to understand
- **Consistency** - Follow existing patterns in the codebase
- **Type safety** - Leverage TypeScript for compile-time safety

## TypeScript
- Use strict mode
- Prefer interfaces over types for object shapes
- Avoid `any` - use `unknown` if type is truly unknown
- Export types separately from implementations

## React
- Functional components only (no class components)
- Use TypeScript for all components
- Props interfaces should be exported and named `[ComponentName]Props`
- Prefer composition over inheritance

## State Management
- Use Valtio for global state
- Keep component state local when possible
- Follow CQRS pattern for state mutations

## File Organization
- One component per file
- Co-locate related files (component, styles, tests)
- Use barrel exports (`index.ts`) for clean imports

## Naming Conventions
- **Components:** PascalCase (e.g., `UserTable.tsx`)
- **Utilities:** camelCase (e.g., `formatDate.ts`)
- **Constants:** UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS`)
- **Interfaces:** PascalCase with descriptive names

## Documentation
- Each package must have a `docs/` folder
- Document complex logic with comments
- Keep comments up-to-date with code changes