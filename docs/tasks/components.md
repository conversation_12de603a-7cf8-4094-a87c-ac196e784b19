grids:

- [ ] auctionlist (on homepage)
- [x] users
- [x] companies
- [x] de history table
- [x] de round table
- [ ] awards
- [ ] credit
- [ ] add/remove traders

Widgets:

- [ ] volume meter (constraints bar)
  - [ ] both vertical and horizontal
  - [ ] a small vertical array version for de round table
  - [ ] possibly a secial one for de history table
- [ ] ribbon chart
- [ ] step chart
- [ ] sankey order matching
- [ ] chat
  - [ ] see this timeline example:
        - https://reablocks.dev/blocks/foundation/timeline
- [ ] auctioneer status
- [ ] * trader status

Pages:

- [ ] home page
- [ ] user management page
- [ ] auctioneer page
- [ ] trader page
- [ ] credit management page

Forms:

- [ ] user crud
- [ ] company crud
- [ ] user/company assignment (might not be a form)
- [ ] auction settings create/edit
- [ ] settings

Other:

- [ ] Appshell (fixed width, minimum height)
- [ ] dialogs (fixed width, minimum height, centered)
- [ ] rules
- [ ] tutorial

