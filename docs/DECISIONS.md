# Architectural Decisions

This document records important architectural and design decisions made for the AU25 Turbo React Design project.

## State Management

### Decision: Use Valtio, Avoid React Provider Pattern
**Date:** 2025-05-23
**Status:** Active

**Context:**
- Need global state management for React application
- Want to avoid prop drilling and complex context hierarchies
- Previous experience with Provider pattern complexity

**Decision:**
We use Valtio for global state management and explicitly avoid the React Provider pattern.

**Implementation:**
- Use `getDomainStore()` from `@repo/api-client` to access live client store
- Components access store directly rather than through React Context
- For testing, use `setMockDomainStore()` to provide mock data

**Rationale:**
- Simpler component hierarchy without nested providers
- Direct store access reduces boilerplate
- Better performance (no unnecessary re-renders from context changes)
- Easier testing with mock store injection

**Example:**
```typescript
// ✅ Preferred approach
const domainStore = getDomainStore();
const users = domainStore.users;

// ❌ Avoid Provider pattern
const { users } = useContext(UserContext);
```

## Architecture Pattern

### Decision: Modified CQRS with Real-time Event-driven Updates
**Date:** Project inception
**Status:** Active

**Context:**
- Need real-time updates for auction data
- Want clear separation between commands and queries
- Traditional request/response patterns insufficient for real-time needs

**Decision:**
Use a modified CQRS (Command Query Responsibility Segregation) pattern with publish/subscribe for real-time updates.

**Implementation:**
- Clients send commands to server
- Server materializes complete client state (LiveClientStore) after each command
- Clients receive full state updates, not incremental changes
- No traditional request/response for data queries

**Rationale:**
- Ensures all clients have consistent, up-to-date state
- Simplifies client-side state management (no need to merge updates)
- Better suited for real-time auction environments
- Reduces complexity of handling partial updates

**Reference:**
See detailed documentation at `packages/api-client/README.md#architecture-real-time-event-driven-cqrs`

## Component Architecture

### Decision: Consolidate Cell Renderers in Main Component Files
**Date:** 2025-05-23
**Status:** Active

**Context:**
- Vue.js required separate files for each cell renderer
- React allows more flexible component organization
- Want to reduce file proliferation

**Decision:**
Consolidate related cell renderers and sub-components within the main component file rather than creating separate files for each.

**Implementation:**
- Define cell renderer components as const functions within main component file
- Only create separate files when components are reused across multiple parents
- Use clear section comments to organize code within files

**Rationale:**
- Reduces cognitive overhead of navigating between many small files
- Keeps related code together
- Easier to understand component relationships
- Simpler imports and exports

**Example:**
```typescript
// ✅ Preferred: Consolidated in DeRoundTable.tsx
const HeaderCell: React.FC<Props> = (props) => { ... };
const BodyCell: React.FC<Props> = (props) => { ... };
export const DeRoundTable: React.FC<Props> = (props) => { ... };

// ❌ Avoid: Separate files unless reused
// DeRoundTableHeaderCell.tsx
// DeRoundTableBodyCell.tsx
```

## Package Management

### Decision: Use pnpm for Package Management
**Date:** Project inception
**Status:** Active

**Context:**
- Monorepo with multiple packages
- Need efficient dependency management
- Want fast installs and disk space efficiency

**Decision:**
Use pnpm as the primary package manager for the project.

**Rationale:**
- Better handling of monorepo dependencies
- Faster installs compared to npm/yarn
- More efficient disk usage with content-addressable storage
- Better security with strict dependency resolution

## Testing Strategy

### Decision: Co-locate Stories with Component Code
**Date:** 2025-05-23
**Status:** Active

**Context:**
- Storybook stories serve as both documentation and testing
- Want to keep related code together
- Need to support package-level development

**Decision:**
Keep Storybook stories co-located with component code in apps/dashboard/src rather than in a separate storybook app.

**Implementation:**
- Stories live in `apps/dashboard/src/**/*.stories.tsx` and `apps/dashboard/stories/`
- Storybook integrated into dashboard app for self-contained documentation
- All stories migrated from separate storybook app to dashboard

**Rationale:**
- Stories stay close to the code they document
- Easier to maintain stories when components change

## Package Consolidation into Dashboard

**Context:**
Originally had separate packages (api-client, data-grids, aug-ui) in a turborepo monorepo structure.

**Problem:**
Unable to configure the toolchain (tsconfig, vite, package.json, turborepo) to reliably:
- Use dist builds for packages while maintaining development experience
- Navigate to source files (src/) instead of dist/index.ts files in IDE
- Sync turborepo cached builds with source changes consistently
- Use shared typescript-config package without brittle configurations

**Attempts Made:**
- TypeScript composite project setup with references
- Various rootDirs and path mapping configurations
- Vite sourcemap exporting and import configurations
- Multiple tsconfig inheritance patterns
- Turborepo cache invalidation strategies

**Result:**
Spent significant time playing "whack-a-mole" with configuration issues. Problems didn't surface until clean rebuilds, making the build system unreliable.

**Decision:**
Consolidate all packages into apps/dashboard as a single, self-contained application.

**Implementation:**
- Move packages/data-grids → apps/dashboard/src/data-grids/
- Move packages/api-client → apps/dashboard/src/api-client/
- Replace @repo/ imports with @/ path aliases
- Remove all workspace dependencies from dashboard
- Keep packages/api-client skeleton for build infrastructure reference

**Benefits:**
- Reliable, predictable build system
- Simple import paths and IDE navigation
- No complex toolchain configuration
- Self-contained deployable application

**Trade-offs:**
- Lost package reusability (acceptable for current use case)
- Larger application bundle (manageable at 1.37MB/394KB gzipped)
- Need to extract packages again if reusability becomes important
- Supports package-level development and testing
- Reduces cognitive load when working on specific packages
