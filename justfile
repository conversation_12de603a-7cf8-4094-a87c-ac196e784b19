# au25-turbo-react-design justfile
# Run commands from the project root with: just <command>

# List available commands
default:
    @just --list

############## (1) DEV ##############################
book:
    #pnpm --filter book dev
    turbo run dev --filter=book

dashboard:
    turbo run dev --filter=dashboard

dashboard-storybook:
    turbo run storybook --filter=dashboard

# Run development servers for multiple apps
dev-all:
    turbo run dev

############## (2) BUILD ##############################
# Build all packages
build:
    pnpm install && \
    turbo run clean && \
    turbo run build

# Build a specific package
build-pkg package:
    pnpm --filter {{package}} build

########### (3) TEST ################################

test-api-client-helpers:
    pnpm --filter api-client test:helpers

test-api-client-connector:
    pnpm --filter api-client test

############# (5) MISC ############################
# Clean the repo
clean:
    # First, run 'turbo run clean' via pnpm to clean all workspaces (apps/packages).
    turbo run clean
    # Then, remove the root node_modules directory. This is done here because a script
    # in package.json (like 'pnpm clean') cannot reliably delete the node_modules
    # directory it is being executed from.
    rm -rf node_modules

# Lint the entire codebase
lint:
    turbo run lint

# Run format-code on the codebase
format:
    turbo run format

# Install dependencies
install:
    pnpm install

############### (6) Repomix ########################
repomix-au25-turbo-react-design:
    repomix \
      --include "**/*.js,**/*.ts,**/*.tsx,**/*.json" \
      --ignore "apps/book,packages/data-grid,packages/ui,apps/dashboard/src/,**/tests/**/*,packages/eslint-config,packages/tailwind-config,packages/typescript-config,packages/tailwind-config,packages/typescript-config" \
      -o "repomix.au25-tubro-react-design.txt" \
      --style "plain" \
      --remove-comments \
      --remove-empty-lines \
      --no-file-summary

